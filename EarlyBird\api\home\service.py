from .dantic import ParamSetting
from typing import List
import time
from EarlyBird.common import (
    TaskStatus,
    Result,
)

import logging

from EarlyBird.ExtendRegister.model_register import Setting
from EarlyBird.config.config import AppConfig
from EarlyBird.common.libs.api_result import Result
from EarlyBird.model.setting import Setting

LOGGER = logging.getLogger(__name__)


class HomeService:

    def getModelName(self) -> str:
        try:
            setting: Setting = Setting.query.filter(Setting.settingKey == "modelName").first()
            if setting and setting.settingValue:
                # 确保返回的模型名称是有效的
                model_name = setting.settingValue.lower()
                # 验证模型名称是否有效
                valid_models = ['qianwen', 'kimi', 'doubao', 'gtp35', 'mock', 'deepseekr1']
                if model_name in valid_models:
                    return model_name
                else:
                    logging.warning(f"无效的模型名称: {setting.settingValue}，使用默认模型")

            # 返回有效的默认模型名称
            return "qianwen"  # 使用千问作为默认模型
        except Exception as e:
            logging.error(f"获取模型名称失败: {str(e)}")
            return "qianwen"  # 出错时也返回有效的默认模型

    def getSetting(self) -> Result:
        try:
            # 获取所有设置项
            settings = {}

            # 获取模型名称
            settings["modelName"] = self.getModelName()

            # 获取API Key配置
            api_keys = [
                "apikeyQianwen",
                "apikeyDeepSeekR1",
                "apikeyKimi",
                "apikeyDoubao",
                "apikeyOpenai"
            ]

            for key in api_keys:
                try:
                    setting = Setting.query.filter_by(settingKey=key).first()
                    settings[key] = setting.settingValue if setting else ""
                    logging.debug(f"获取设置 {key}: {settings[key][:10]}..." if settings[key] else f"获取设置 {key}: (空)")
                except Exception as e:
                    logging.warning(f"获取设置 {key} 失败: {str(e)}")
                    settings[key] = ""

            # 添加系统基础信息
            settings["title"] = "早鸟论文"
            settings["subtitle"] = "AI论文生成助手"

            logging.info(f"获取设置成功，包含 {len(settings)} 个配置项")
            return Result().setData(settings)

        except Exception as e:
            logging.exception(f"获取设置失败: {str(e)}")
            # 返回默认设置，确保系统能正常工作
            default_settings = {
                "modelName": "qianwen",
                "apikeyQianwen": "",
                "apikeyDeepSeekR1": "",
                "apikeyKimi": "",
                "apikeyDoubao": "",
                "apikeyOpenai": "",
                "title": "早鸟论文",
                "subtitle": "AI论文生成助手"
            }
            logging.info("使用默认设置")
            return Result().setData(default_settings)

    def saveSetting(self, body: ParamSetting):
        """保存设置"""
        try:
            setting = body.setting
            saved_keys = []

            for key, value in setting.items():
                try:
                    # 查找现有设置
                    s: Setting = Setting.query.filter(Setting.settingKey == key).first()

                    if s is None:
                        # 创建新设置
                        s = Setting()
                        s.settingKey = key
                        s.settingValue = value
                        logging.info(f"创建新设置: {key} = {value}")
                    else:
                        # 更新现有设置
                        s.settingValue = value
                        logging.info(f"更新设置: {key} = {value}")

                    s.save()
                    saved_keys.append(key)

                except Exception as e:
                    logging.error(f"保存设置 {key} 失败: {str(e)}")
                    # 继续处理其他设置项
                    continue

            if saved_keys:
                logging.info(f"成功保存 {len(saved_keys)} 个设置项: {', '.join(saved_keys)}")
                return Result().setData({"saved_keys": saved_keys})
            else:
                return Result().error("没有成功保存任何设置项")

        except Exception as e:
            logging.exception(f"保存设置失败: {str(e)}")
            return Result().error(f"保存设置失败: {str(e)}")
