from .dantic import ParamSetting
from typing import List
import time
from EarlyBird.common import (
    TaskStatus,
    Result,
)

import logging

from EarlyBird.ExtendRegister.model_register import Setting
from EarlyBird.config.config import AppConfig
from EarlyBird.common.libs.api_result import Result
from EarlyBird.model.setting import Setting

LOGGER = logging.getLogger(__name__)


class HomeService:

    def getModelName(self) -> str:
        try:
            setting: Setting = Setting.query.filter(Setting.settingKey == "modelName").first()
            if setting and setting.settingValue:
                # 确保返回的模型名称是有效的
                model_name = setting.settingValue.lower()
                # 验证模型名称是否有效
                valid_models = ['qianwen', 'kimi', 'doubao', 'gtp35', 'mock', 'deepseekr1']
                if model_name in valid_models:
                    return model_name
                else:
                    logging.warning(f"无效的模型名称: {setting.settingValue}，使用默认模型")

            # 返回有效的默认模型名称
            return "qianwen"  # 使用千问作为默认模型
        except Exception as e:
            logging.error(f"获取模型名称失败: {str(e)}")
            return "qianwen"  # 出错时也返回有效的默认模型

    def getSetting(self) -> Result:
        try:
            return Result().setData({
                "modelName": self.getModelName(),
                "title": "早鸟论文",
                "subtitle": "AI论文生成助手"
            })
        except Exception as e:
            logging.exception(f"获取设置失败: {str(e)}")
            return Result().error(str(e))

    def saveSetting(self, body: ParamSetting):
        setting = body.setting
        for key, value in setting.items():
            s: Setting = Setting.query.filter(Setting.settingKey == key).first()
            if s is None:
                s = Setting()
                s.settingKey = key
                s.settingValue = value
            else:
                s.settingValue = value

            s.save()

        return Result()
