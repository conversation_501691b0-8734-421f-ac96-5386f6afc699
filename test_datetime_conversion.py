#!/usr/bin/env python3
"""
测试微信支付时间格式转换
验证修复效果
"""

import sys
import os
from datetime import datetime
import re

def _convert_wechat_datetime(wechat_time_str):
    """
    转换微信支付时间格式为MySQL兼容格式
    
    Args:
        wechat_time_str: 微信支付返回的时间字符串，格式如 '2025-07-29T13:54:37+08:00'
        
    Returns:
        datetime: MySQL兼容的datetime对象，或None如果转换失败
    """
    if not wechat_time_str:
        return None
        
    try:
        # 移除时区信息，将ISO 8601格式转换为标准格式
        # 从 '2025-07-29T13:54:37+08:00' 转换为 '2025-07-29 13:54:37'
        
        # 使用正则表达式提取日期时间部分，忽略时区
        match = re.match(r'(\d{4}-\d{2}-\d{2})T(\d{2}:\d{2}:\d{2})', wechat_time_str)
        if match:
            date_part, time_part = match.groups()
            datetime_str = f"{date_part} {time_part}"
            return datetime.strptime(datetime_str, '%Y-%m-%d %H:%M:%S')
        else:
            print(f"❌ 无法解析微信支付时间格式: {wechat_time_str}")
            return None
            
    except Exception as e:
        print(f"❌ 转换微信支付时间失败: {wechat_time_str}, 错误: {str(e)}")
        return None

def test_datetime_conversion():
    """测试时间转换函数"""
    print("🔧 测试微信支付时间格式转换")
    print("=" * 50)
    
    # 测试用例
    test_cases = [
        {
            "input": "2025-07-29T13:54:37+08:00",
            "expected": "2025-07-29 13:54:37",
            "desc": "标准微信支付时间格式"
        },
        {
            "input": "2025-07-29T05:30:15+00:00",
            "expected": "2025-07-29 05:30:15",
            "desc": "UTC时区格式"
        },
        {
            "input": "2025-12-31T23:59:59+08:00",
            "expected": "2025-12-31 23:59:59",
            "desc": "年末时间"
        },
        {
            "input": "",
            "expected": None,
            "desc": "空字符串"
        },
        {
            "input": None,
            "expected": None,
            "desc": "None值"
        },
        {
            "input": "invalid-format",
            "expected": None,
            "desc": "无效格式"
        }
    ]
    
    success_count = 0
    total_count = len(test_cases)
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n📋 测试用例 {i}: {case['desc']}")
        print(f"  输入: {case['input']}")
        
        result = _convert_wechat_datetime(case['input'])
        
        if case['expected'] is None:
            if result is None:
                print(f"  ✅ 结果: None (符合预期)")
                success_count += 1
            else:
                print(f"  ❌ 结果: {result} (预期: None)")
        else:
            if result:
                result_str = result.strftime('%Y-%m-%d %H:%M:%S')
                if result_str == case['expected']:
                    print(f"  ✅ 结果: {result_str} (符合预期)")
                    success_count += 1
                else:
                    print(f"  ❌ 结果: {result_str} (预期: {case['expected']})")
            else:
                print(f"  ❌ 结果: None (预期: {case['expected']})")
    
    print(f"\n🎉 测试完成: {success_count}/{total_count} 通过")
    
    if success_count == total_count:
        print("✅ 所有测试用例通过！时间转换函数工作正常。")
        return True
    else:
        print("❌ 部分测试用例失败，需要检查转换函数。")
        return False

def test_mysql_compatibility():
    """测试MySQL兼容性"""
    print("\n🔍 测试MySQL兼容性")
    print("-" * 30)
    
    # 模拟微信支付返回的时间
    wechat_time = "2025-07-29T13:54:37+08:00"
    converted_time = _convert_wechat_datetime(wechat_time)
    
    if converted_time:
        print(f"原始微信时间: {wechat_time}")
        print(f"转换后时间: {converted_time}")
        print(f"MySQL格式: {converted_time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 验证是否为datetime对象
        if isinstance(converted_time, datetime):
            print("✅ 转换结果是datetime对象，兼容MySQL")
            return True
        else:
            print("❌ 转换结果不是datetime对象")
            return False
    else:
        print("❌ 转换失败")
        return False

def simulate_database_insert():
    """模拟数据库插入操作"""
    print("\n💾 模拟数据库插入操作")
    print("-" * 30)
    
    try:
        # 模拟微信支付API返回
        mock_wechat_response = {
            'success_time': '2025-07-29T13:54:37+08:00',
            'transaction_id': 'wx_test_123456',
            'trade_state': 'SUCCESS'
        }
        
        # 转换时间
        converted_time = _convert_wechat_datetime(mock_wechat_response['success_time'])
        
        if converted_time:
            print(f"✅ 时间转换成功: {converted_time}")
            
            # 模拟数据库字段赋值
            payment_time = converted_time
            print(f"✅ 模拟payment_time字段赋值: {payment_time}")
            
            # 模拟格式化为字符串（用于API返回）
            formatted_time = payment_time.strftime('%Y-%m-%d %H:%M:%S')
            print(f"✅ 格式化为字符串: {formatted_time}")
            
            return True
        else:
            print("❌ 时间转换失败")
            return False
            
    except Exception as e:
        print(f"❌ 模拟操作失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🔧 微信支付时间格式转换测试工具")
    print("=" * 60)
    
    # 1. 测试转换函数
    conversion_ok = test_datetime_conversion()
    
    # 2. 测试MySQL兼容性
    mysql_ok = test_mysql_compatibility()
    
    # 3. 模拟数据库操作
    db_ok = simulate_database_insert()
    
    print("\n" + "=" * 60)
    print("📊 测试总结:")
    print(f"  转换函数测试: {'✅ 通过' if conversion_ok else '❌ 失败'}")
    print(f"  MySQL兼容性: {'✅ 通过' if mysql_ok else '❌ 失败'}")
    print(f"  数据库模拟: {'✅ 通过' if db_ok else '❌ 失败'}")
    
    if conversion_ok and mysql_ok and db_ok:
        print("\n🎉 所有测试通过！修复应该能解决数据库时间格式问题。")
    else:
        print("\n⚠️ 部分测试失败，需要进一步检查。")

if __name__ == "__main__":
    main()
