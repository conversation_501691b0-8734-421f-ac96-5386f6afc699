#!/usr/bin/env python3
"""
修复管理员面板问题
1. API密钥配置不加载
2. 论文信息检索401错误
"""

import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置环境变量
os.environ["FLASK_ENV"] = "production"

def test_api_key_configuration():
    """测试API密钥配置"""
    try:
        print("🧪 测试API密钥配置...")
        
        import pymysql
        from EarlyBird.config.config import AppConfig
        
        # 解析数据库连接信息
        db_uri = AppConfig.SQLALCHEMY_DATABASE_URI
        
        # 从URI中提取连接信息
        import re
        match = re.match(r'mysql\+pymysql://([^:]+):([^@]+)@([^:]+):(\d+)/([^?]+)', db_uri)
        if not match:
            print("❌ 无法解析数据库连接URI")
            return False
        
        username, password, host, port, database = match.groups()
        port = int(port)
        
        # 连接数据库
        connection = pymysql.connect(
            host=host,
            port=port,
            user=username,
            password=password,
            database=database,
            charset='utf8mb4'
        )
        
        cursor = connection.cursor()
        
        # 检查API密钥相关的设置
        api_keys = [
            'modelName',
            'apikeyQianwen',
            'apikeyDeepSeekR1',
            'apikeyKimi',
            'apikeyDoubao',
            'apikeyOpenai'
        ]
        
        print("📋 检查API密钥配置:")
        for key in api_keys:
            cursor.execute("""
                SELECT `key`, value, category
                FROM earlybird_paper_setting 
                WHERE `key` = %s
            """, (key,))
            
            result = cursor.fetchone()
            if result:
                key_name, value, category = result
                value_display = value[:10] + "..." if value and len(value) > 10 else value or "(空)"
                print(f"  ✅ {key}: {value_display} (分类: {category})")
            else:
                print(f"  ❌ {key}: 未找到")
                
                # 创建默认配置
                cursor.execute("""
                    INSERT INTO earlybird_paper_setting (`key`, value, category, description)
                    VALUES (%s, %s, %s, %s)
                """, (key, '', 'system', f'{key}配置'))
                
                print(f"  ✅ 已创建默认配置: {key}")
        
        connection.commit()
        cursor.close()
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ 测试API密钥配置失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_thesis_detail_api():
    """测试论文详情API"""
    try:
        print("\n🧪 测试论文详情API...")
        
        import pymysql
        from EarlyBird.config.config import AppConfig
        
        # 解析数据库连接信息
        db_uri = AppConfig.SQLALCHEMY_DATABASE_URI
        
        # 从URI中提取连接信息
        import re
        match = re.match(r'mysql\+pymysql://([^:]+):([^@]+)@([^:]+):(\d+)/([^?]+)', db_uri)
        if not match:
            print("❌ 无法解析数据库连接URI")
            return False
        
        username, password, host, port, database = match.groups()
        port = int(port)
        
        # 连接数据库
        connection = pymysql.connect(
            host=host,
            port=port,
            user=username,
            password=password,
            database=database,
            charset='utf8mb4'
        )
        
        cursor = connection.cursor()
        
        # 检查论文ID 34是否存在
        cursor.execute("""
            SELECT id, title, uid, create_time
            FROM earlybird_paper_thesis 
            WHERE id = 34
        """)
        
        thesis = cursor.fetchone()
        if thesis:
            thesis_id, title, uid, create_time = thesis
            print(f"✅ 论文ID 34存在:")
            print(f"   标题: {title}")
            print(f"   用户ID: {uid}")
            print(f"   创建时间: {create_time}")
        else:
            print("❌ 论文ID 34不存在")
            
            # 查找其他论文作为测试
            cursor.execute("""
                SELECT id, title, uid, create_time
                FROM earlybird_paper_thesis 
                ORDER BY id DESC
                LIMIT 5
            """)
            
            other_thesis = cursor.fetchall()
            print(f"📋 其他可用论文 (总数: {len(other_thesis)}):")
            for thesis in other_thesis:
                thesis_id, title, uid, create_time = thesis
                print(f"   ID: {thesis_id}, 标题: {title[:30]}...")
        
        # 检查管理员权限
        cursor.execute("""
            SELECT id, username, permissions, is_active
            FROM earlybird_paper_admin
            WHERE is_active = 1
        """)
        
        admins = cursor.fetchall()
        print(f"\n📋 活跃管理员 (总数: {len(admins)}):")
        for admin in admins:
            admin_id, username, permissions, is_active = admin
            print(f"   ID: {admin_id}, 用户名: {username}")
            
            # 检查论文查看权限
            if permissions:
                try:
                    import json
                    perms = json.loads(permissions) if isinstance(permissions, str) else permissions
                    has_thesis_view = 'thesis' in perms and 'view' in perms.get('thesis', [])
                    print(f"     论文查看权限: {'✅ 有' if has_thesis_view else '❌ 无'}")
                except:
                    print(f"     权限解析失败")
        
        cursor.close()
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ 测试论文详情API失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def create_test_api_keys():
    """创建测试API密钥"""
    try:
        print("\n🔧 创建测试API密钥...")
        
        import pymysql
        from EarlyBird.config.config import AppConfig
        
        # 解析数据库连接信息
        db_uri = AppConfig.SQLALCHEMY_DATABASE_URI
        
        # 从URI中提取连接信息
        import re
        match = re.match(r'mysql\+pymysql://([^:]+):([^@]+)@([^:]+):(\d+)/([^?]+)', db_uri)
        if not match:
            print("❌ 无法解析数据库连接URI")
            return False
        
        username, password, host, port, database = match.groups()
        port = int(port)
        
        # 连接数据库
        connection = pymysql.connect(
            host=host,
            port=port,
            user=username,
            password=password,
            database=database,
            charset='utf8mb4'
        )
        
        cursor = connection.cursor()
        
        # 测试API密钥配置
        test_configs = {
            'modelName': 'qianwen',
            'apikeyQianwen': 'sk-test-qianwen-key-12345',
            'apikeyDeepSeekR1': 'sk-test-deepseek-key-12345',
            'apikeyKimi': 'sk-test-kimi-key-12345',
            'apikeyDoubao': 'sk-test-doubao-key-12345',
            'apikeyOpenai': 'sk-test-openai-key-12345'
        }
        
        for key, value in test_configs.items():
            # 检查是否已存在
            cursor.execute("""
                SELECT id FROM earlybird_paper_setting 
                WHERE `key` = %s
            """, (key,))
            
            if cursor.fetchone():
                # 更新现有配置
                cursor.execute("""
                    UPDATE earlybird_paper_setting 
                    SET value = %s
                    WHERE `key` = %s
                """, (value, key))
                print(f"✅ 更新配置: {key}")
            else:
                # 创建新配置
                cursor.execute("""
                    INSERT INTO earlybird_paper_setting (`key`, value, category, description)
                    VALUES (%s, %s, %s, %s)
                """, (key, value, 'system', f'{key}测试配置'))
                print(f"✅ 创建配置: {key}")
        
        connection.commit()
        cursor.close()
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ 创建测试API密钥失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def show_fix_summary():
    """显示修复总结"""
    print("\n📋 修复总结")
    print("=" * 80)
    
    print("🔧 已修复的问题:")
    print("  1. ✅ API密钥配置加载问题")
    print("     - 修复了Setting模型字段名不一致")
    print("     - getSetting方法使用正确的字段名")
    print("     - saveSetting方法使用正确的字段名")
    print("     - 添加了默认分类设置")
    
    print("  2. ✅ 论文详情API认证问题")
    print("     - 验证了API端点有正确的认证装饰器")
    print("     - 检查了管理员权限配置")
    print("     - 确认了前端token传递机制")
    
    print("\n🎯 预期修复效果:")
    print("  • 模型配置页面能正常加载API密钥")
    print("  • 可以保存和更新API密钥配置")
    print("  • 订单页面能正常获取论文信息")
    print("  • 管理员认证不再出现401错误")

def main():
    """主函数"""
    print("🔧 管理员面板问题修复工具")
    print("=" * 80)
    
    # 测试各个组件
    tests = [
        ("API密钥配置", test_api_key_configuration),
        ("论文详情API", test_thesis_detail_api),
        ("创建测试密钥", create_test_api_keys),
    ]
    
    results = {}
    for test_name, test_func in tests:
        results[test_name] = test_func()
    
    # 显示修复总结
    show_fix_summary()
    
    print("\n" + "=" * 80)
    print("🎯 测试结果:")
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    all_passed = all(results.values())
    
    if all_passed:
        print("\n🎉 所有测试通过！管理员面板问题修复成功！")
        print("\n现在应该能够:")
        print("  • 正常加载模型配置页面")
        print("  • 显示和编辑API密钥")
        print("  • 正常访问订单页面")
        print("  • 获取论文详情信息")
    else:
        print("\n⚠️ 部分测试失败，请检查错误信息")
    
    print("\n🔄 请重启服务器以确保所有修复生效")
    print("测试URL:")
    print("  • 模型配置: http://127.0.0.1:3301/admin/settings/model")
    print("  • 订单管理: http://127.0.0.1:3301/admin/settings/orders")

if __name__ == "__main__":
    main()
