#!/usr/bin/env python3
"""
测试支付状态同步
"""

import sys
import os
import logging

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置环境变量
os.environ["FLASK_ENV"] = "production"

def test_payment_status_sync():
    """测试支付状态同步"""
    try:
        from EarlyBird.web_server import create_custom_app
        from EarlyBird.model.payment import Payment, ThesisDownloadRecord
        
        app = create_custom_app()
        if not app:
            print("❌ 无法创建Flask应用")
            return False
        
        with app.app_context():
            print("🔍 检查支付状态同步...")
            
            # 查找所有未标记为已支付的下载记录，但有支付订单号的
            download_records = ThesisDownloadRecord.query.filter(
                ThesisDownloadRecord.is_paid == False,
                ThesisDownloadRecord.payment_order_id.isnot(None)
            ).all()
            
            print(f"📋 找到 {len(download_records)} 个未同步的下载记录")
            
            sync_count = 0
            for record in download_records:
                # 查找对应的支付记录
                payment = Payment.query.filter_by(out_trade_no=record.payment_order_id).first()
                if payment:
                    print(f"  订单: {record.payment_order_id}")
                    print(f"    用户: {record.uid}, 论文: {record.thesis_id}")
                    print(f"    下载记录状态: is_paid={record.is_paid}")
                    print(f"    支付记录状态: status={payment.status}")
                    
                    # 如果支付记录显示已支付，但下载记录未更新
                    if payment.status == 1:
                        print(f"    ✅ 同步支付状态")
                        record.is_paid = True
                        record.payment_time = payment.pay_time
                        record.save()
                        sync_count += 1
                    else:
                        print(f"    ⚠️ 支付未完成")
                else:
                    print(f"  订单: {record.payment_order_id}")
                    print(f"    ❌ 未找到对应的支付记录")
            
            print(f"\n🎯 同步完成，共同步 {sync_count} 条记录")
            return True
                
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_payment_status_api():
    """测试支付状态API"""
    try:
        from EarlyBird.web_server import create_custom_app
        from EarlyBird.api.thesis.service import ThesisServie
        from EarlyBird.api.thesis.param import ParamPaymentStatus
        
        app = create_custom_app()
        if not app:
            print("❌ 无法创建Flask应用")
            return False
        
        with app.app_context():
            print("🔍 测试支付状态API...")
            
            # 模拟API调用
            service = ThesisServie()
            
            # 测试参数
            test_cases = [
                {"userId": 12, "thesisId": 32, "orderId": None},
                {"userId": 12, "thesisId": 32, "orderId": "thesis_download_3d1568f69e54"}
            ]
            
            for i, case in enumerate(test_cases, 1):
                print(f"\n📋 测试用例 {i}: {case}")
                
                # 创建参数对象
                param = ParamPaymentStatus()
                param.userId = case["userId"]
                param.thesisId = case["thesisId"]
                param.orderId = case["orderId"]
                
                # 调用API
                result = service.getPaymentStatus(param)
                
                if result.isSucc():
                    print(f"  ✅ API调用成功")
                    print(f"  📊 返回数据: {result.data}")
                else:
                    print(f"  ❌ API调用失败: {result.message}")
            
            return True
                
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 支付状态同步测试")
    print("=" * 50)
    
    success_count = 0
    total_tests = 2
    
    # 测试支付状态同步
    if test_payment_status_sync():
        success_count += 1
    
    print("\n" + "-" * 30)
    
    # 测试支付状态API
    if test_payment_status_api():
        success_count += 1
    
    print("\n" + "=" * 50)
    print(f"🎯 测试结果: {success_count}/{total_tests} 通过")
    
    if success_count == total_tests:
        print("🎉 所有测试通过！")
        print("\n📋 下一步操作:")
        print("1. 重启服务器")
        print("2. 重新构建前端")
        print("3. 测试支付流程")
    else:
        print("⚠️ 部分测试失败，请检查错误信息")

if __name__ == "__main__":
    main()
