#!/usr/bin/env python3
"""
检查当前配置状态
"""

import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置环境变量
os.environ["FLASK_ENV"] = "production"

def check_database_config():
    """检查数据库中的配置"""
    try:
        import pymysql
        from EarlyBird.config.config import AppConfig
        
        # 解析数据库连接信息
        db_uri = AppConfig.SQLALCHEMY_DATABASE_URI
        
        # 从URI中提取连接信息
        import re
        match = re.match(r'mysql\+pymysql://([^:]+):([^@]+)@([^:]+):(\d+)/([^?]+)', db_uri)
        if not match:
            print("❌ 无法解析数据库连接URI")
            return False
        
        username, password, host, port, database = match.groups()
        port = int(port)
        
        # 连接数据库
        connection = pymysql.connect(
            host=host,
            port=port,
            user=username,
            password=password,
            database=database,
            charset='utf8mb4'
        )
        
        cursor = connection.cursor()
        
        print("🔍 检查数据库配置状态...")
        
        # 查询论文下载相关配置
        cursor.execute("""
            SELECT config_key, config_value, name, description 
            FROM earlybird_payment_config 
            WHERE config_key LIKE 'thesis.download.%' AND is_deleted = 0
            ORDER BY config_key
        """)
        
        configs = cursor.fetchall()
        
        if not configs:
            print("❌ 未找到任何论文下载配置")
        else:
            print(f"📋 找到 {len(configs)} 个配置项:")
            for key, value, name, desc in configs:
                status = "✅" if key == 'thesis.download.is_active' and value == 'false' else "📋"
                print(f"  {status} {name}: {key} = {value}")
                if desc:
                    print(f"      描述: {desc}")
        
        cursor.close()
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ 检查失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def force_disable_payment():
    """强制禁用支付功能"""
    try:
        import pymysql
        from EarlyBird.config.config import AppConfig
        
        # 解析数据库连接信息
        db_uri = AppConfig.SQLALCHEMY_DATABASE_URI
        
        # 从URI中提取连接信息
        import re
        match = re.match(r'mysql\+pymysql://([^:]+):([^@]+)@([^:]+):(\d+)/([^?]+)', db_uri)
        if not match:
            print("❌ 无法解析数据库连接URI")
            return False
        
        username, password, host, port, database = match.groups()
        port = int(port)
        
        # 连接数据库
        connection = pymysql.connect(
            host=host,
            port=port,
            user=username,
            password=password,
            database=database,
            charset='utf8mb4'
        )
        
        cursor = connection.cursor()
        
        print("🔧 强制禁用支付功能...")
        
        # 确保 thesis.download.is_active 设置为 false
        cursor.execute("""
            UPDATE earlybird_payment_config 
            SET config_value = 'false', update_time = NOW()
            WHERE config_key = 'thesis.download.is_active' AND is_deleted = 0
        """)
        
        if cursor.rowcount > 0:
            print("✅ 已更新 thesis.download.is_active = false")
        else:
            # 如果没有找到，创建一个
            cursor.execute("""
                INSERT INTO earlybird_payment_config 
                (config_key, config_value, name, description, create_time, update_time, is_deleted, status) 
                VALUES ('thesis.download.is_active', 'false', '论文下载收费开关', '是否启用论文下载收费功能', NOW(), NOW(), 0, 1)
                ON DUPLICATE KEY UPDATE 
                config_value = 'false', update_time = NOW()
            """)
            print("✅ 已创建/更新 thesis.download.is_active = false")
        
        # 提交更改
        connection.commit()
        
        # 验证更改
        cursor.execute("SELECT config_value FROM earlybird_payment_config WHERE config_key = 'thesis.download.is_active' AND is_deleted = 0")
        result = cursor.fetchone()
        
        if result and result[0] == 'false':
            print("✅ 验证成功: thesis.download.is_active = false")
        else:
            print("❌ 验证失败: 配置未正确更新")
        
        cursor.close()
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ 强制禁用失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 检查和修复配置状态")
    print("=" * 50)
    
    # 检查当前配置
    check_database_config()
    
    print("\n" + "-" * 30)
    
    # 强制禁用支付功能
    if force_disable_payment():
        print("\n🎉 配置修复完成！")
        print("\n📋 重要提醒:")
        print("1. 请重启服务器以确保配置生效")
        print("2. 重启后测试论文下载功能")
        print("3. 应该看到免费下载，不再弹出支付窗口")
    else:
        print("\n❌ 配置修复失败")

if __name__ == "__main__":
    main()
