#!/usr/bin/env python3
"""
简化的支付修复测试
直接调用API测试修复效果
"""

import sys
import os
import requests
import json

def test_download_api():
    """测试下载API"""
    print("🔍 测试下载API...")
    
    # 测试用例
    test_cases = [
        {"thesisId": 35, "expected": "需要支付", "desc": "论文35（未支付）"},
        {"thesisId": 34, "expected": "可以下载", "desc": "论文34（已支付）"},
        {"thesisId": 33, "expected": "可以下载", "desc": "论文33（已支付）"}
    ]
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n📋 测试用例 {i}: {case['desc']}")
        
        try:
            response = requests.post('http://127.0.0.1:3301/api/thesis/downloadThesis', 
                                   json={"thesisId": case["thesisId"]},
                                   headers={'Content-Type': 'application/json'},
                                   cookies={'session': 'test_session'})
            
            if response.status_code == 200:
                result = response.json()
                print(f"  📊 响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
                
                if result.get('is_success'):
                    if result['data'].get('file'):
                        print(f"  ✅ 结果: 可以下载")
                        print(f"  📄 文件: {result['data']['file']}")
                        print(f"  🆓 原因: {result['data'].get('free_reason', 'N/A')}")
                    else:
                        print(f"  💰 结果: 需要支付")
                        print(f"  💵 价格: {result['data'].get('price', 'N/A')}")
                        print(f"  📝 原因: {result['data'].get('reason', 'N/A')}")
                else:
                    print(f"  ❌ 失败: {result.get('message', 'Unknown error')}")
            else:
                print(f"  ❌ HTTP错误: {response.status_code}")
                
        except Exception as e:
            print(f"  ❌ 请求失败: {str(e)}")

def test_payment_status_api():
    """测试支付状态API"""
    print("\n🔍 测试支付状态API...")
    
    # 测试用例
    test_cases = [
        {
            "thesisId": 35, 
            "orderId": "thesis_download_45e708ba9040",
            "desc": "论文35未支付订单"
        },
        {
            "thesisId": 34, 
            "orderId": "thesis_download_190e1fcb92a8",
            "desc": "论文34已支付订单"
        },
        {
            "thesisId": 33, 
            "orderId": "thesis_download_a8a07934b647",
            "desc": "论文33已支付订单"
        }
    ]
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n📋 测试用例 {i}: {case['desc']}")
        
        try:
            response = requests.post('http://127.0.0.1:3301/api/thesis/paymentStatus', 
                                   json={
                                       "thesisId": case["thesisId"],
                                       "orderId": case["orderId"]
                                   },
                                   headers={'Content-Type': 'application/json'},
                                   cookies={'session': 'test_session'})
            
            if response.status_code == 200:
                result = response.json()
                print(f"  📊 响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
                
                if result.get('is_success') and result.get('data'):
                    data = result['data']
                    print(f"  💰 是否已支付: {data.get('is_paid', False)}")
                    print(f"  📊 支付状态: {data.get('payment_status', 'N/A')}")
                    print(f"  🆔 订单号: {data.get('order_id', 'N/A')}")
                    
                    if data.get('download_file'):
                        print(f"  📄 下载文件: {data['download_file']}")
                        print(f"  🔄 自动下载: {data.get('auto_download', False)}")
                    
                    if data.get('sync_from_wechat'):
                        print(f"  🔄 从微信同步: {data['sync_from_wechat']}")
                        
                else:
                    print(f"  ❌ 失败: {result.get('message', 'Unknown error')}")
            else:
                print(f"  ❌ HTTP错误: {response.status_code}")
                
        except Exception as e:
            print(f"  ❌ 请求失败: {str(e)}")

def test_create_payment():
    """测试创建支付"""
    print("\n🔍 测试创建支付...")
    
    try:
        response = requests.post('http://127.0.0.1:3301/api/thesis/payDownload', 
                               json={
                                   "thesisId": 35,
                                   "paymentMethod": "wechat"
                               },
                               headers={'Content-Type': 'application/json'},
                               cookies={'session': 'test_session'})
        
        if response.status_code == 200:
            result = response.json()
            print(f"📊 响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
            
            if result.get('is_success') and result.get('data'):
                data = result['data']
                print(f"✅ 支付订单创建成功")
                print(f"🆔 订单号: {data.get('order_id', 'N/A')}")
                print(f"💵 价格: {data.get('price', 'N/A')}")
                print(f"📱 二维码: {data.get('qr_code_url', 'N/A')}")
                
                return data.get('order_id')
            else:
                print(f"❌ 创建失败: {result.get('message', 'Unknown error')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 请求失败: {str(e)}")
    
    return None

def test_simulate_payment(order_id):
    """测试模拟支付"""
    if not order_id:
        print("⚠️ 没有订单号，跳过模拟支付")
        return
        
    print(f"\n🔍 测试模拟支付 - 订单: {order_id}")
    
    try:
        response = requests.post('http://127.0.0.1:3301/api/thesis/simulatePayment', 
                               json={"order_id": order_id},
                               headers={'Content-Type': 'application/json'},
                               cookies={'session': 'test_session'})
        
        if response.status_code == 200:
            result = response.json()
            print(f"📊 响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
            
            if result.get('is_success'):
                print(f"✅ 模拟支付成功")
            else:
                print(f"❌ 模拟支付失败: {result.get('message', 'Unknown error')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 请求失败: {str(e)}")

def main():
    """主函数"""
    print("🔧 支付修复效果测试工具")
    print("=" * 50)
    
    # 1. 测试下载API
    test_download_api()
    
    # 2. 测试支付状态API
    test_payment_status_api()
    
    # 3. 测试完整支付流程
    print("\n🔄 测试完整支付流程...")
    order_id = test_create_payment()
    if order_id:
        test_simulate_payment(order_id)
        
        # 再次测试支付状态
        print(f"\n🔍 测试支付后状态查询...")
        try:
            response = requests.post('http://127.0.0.1:3301/api/thesis/paymentStatus', 
                                   json={
                                       "thesisId": 35,
                                       "orderId": order_id
                                   },
                                   headers={'Content-Type': 'application/json'},
                                   cookies={'session': 'test_session'})
            
            if response.status_code == 200:
                result = response.json()
                print(f"📊 支付后状态: {json.dumps(result, ensure_ascii=False, indent=2)}")
            else:
                print(f"❌ HTTP错误: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 请求失败: {str(e)}")
    
    print("\n🎉 测试完成！")

if __name__ == "__main__":
    main()
