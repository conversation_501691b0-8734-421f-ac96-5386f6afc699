from abc import ABCMeta, abstractmethod
from pydantic import BaseModel, ValidationError, constr, Field
from typing import Optional
import logging
import json
from EarlyBird.common import isDeplyOnAws
from EarlyBird.config.config import AppConfig

LOGGER = logging.getLogger(__name__)

MODEL_GPT35 = "Gtp35"
MODEL_GEMINI10 = "Gemini10"
MODEL_QIANWEN = "qianwen"
MODEL_KIMI = "kimi"
MODEL_MOCK = "mock"
MODEL_DOUBAO = 'doubao'

class AiQuery(BaseModel):
    userMessage: str = ""
    timeout: int = 20

class AiQueryResult(BaseModel):
    text: Optional[str] = ""
    totalToken: Optional[int] = 0
    isValid: Optional[bool] = True
    errMessage: Optional[str] = ""

    def __repr__(self) -> str:
        return json.dumps(self.dict())

class AiAdapter(object):
    __metaclass__ = ABCMeta

    @abstractmethod
    def query(self, query: <PERSON><PERSON><PERSON><PERSON>) -> AiQueryResult:
        pass

from .adapter.qianwen import <PERSON><PERSON><PERSON>
from .adapter.mock import Mock
from .adapter.kimi import Kimi
from .adapter.gpt35 import Gpt35
from .adapter.doubao import Doubao

from EarlyBird.common import isDevEnv
import logging

LOGGER = logging.getLogger(__name__)

def getAdapter(modelName: str = "") -> AiAdapter:
    """获取 AI 适配器实例"""
    if not modelName:
        modelName = MODEL_QIANWEN  # 默认使用千问模型
    
    modelName = modelName.lower()
    LOGGER.info(f"使用模型: {modelName}")

    if modelName == MODEL_KIMI.lower():
        return Kimi()
    elif modelName == MODEL_QIANWEN.lower():
        return Qianwen()
    elif modelName == MODEL_DOUBAO.lower():
        return Doubao()
    elif modelName == MODEL_MOCK.lower():
        return Mock()
    elif modelName == "deepseekr1":
        from .adapter.deepseek_r1 import DeepSeekR1
        return DeepSeekR1()
    
    raise Exception(f"未知的模型类型: {modelName}")
