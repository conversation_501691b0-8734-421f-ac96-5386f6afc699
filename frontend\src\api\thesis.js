import request from '@/utils/request'

/**
 * 获取论文列表
 * @returns {Promise} 论文列表
 */
export function getThesisList() {
    return request({
        url: '/api/thesis/getList',
        method: 'post',
        data: {}
    })
}

/**
 * 获取论文详情
 * @param {Object} data 包含thesisId的对象
 * @returns {Promise} 论文详情
 */
export function getThesisDetail(data) {
    return request({
        url: '/api/thesis/getDetail',
        method: 'post',
        data
    })
}

/**
 * 下载论文
 * @param {Object} data 包含thesisId的对象
 * @returns {Promise} 下载结果
 */
export function downloadThesis(data) {
    return request({
        url: '/api/thesis/downloadThesis',
        method: 'post',
        data
    })
}

/**
 * 删除论文
 * @param {Object} data 包含thesisId的对象
 * @returns {Promise} 删除结果
 */
export function deleteThesis(data) {
    return request({
        url: '/api/thesis/deleteThesis',
        method: 'post',
        data
    })
}

/**
 * 支付论文下载费用
 * @param {Object} data 包含thesisId、paymentMethod等信息的对象
 * @returns {Promise} 支付结果
 */
export function payThesisDownload(data) {
    return request({
        url: '/api/thesis/payDownload',
        method: 'post',
        data
    })
}

/**
 * 获取论文下载支付状态
 * @param {Object} data 包含thesisId的对象
 * @returns {Promise} 支付状态
 */
export function getThesisPaymentStatus(data) {
    return request({
        url: '/api/thesis/paymentStatus',
        method: 'post',
        data
    })
}

/**
 * 确认支付并下载论文
 * @param {Object} data 包含thesisId和orderId的对象
 * @returns {Promise} 确认支付结果和下载信息
 */
export function confirmPayment(data) {
    return request({
        url: '/api/thesis/confirmPayment',
        method: 'post',
        data
    })
}

export function getOutline(data) {
    return request({
        url: '/api/thesis/getOutline',
        method: 'post',
        data
    })
}

export function getProgress(data) {
    return request({
        url: '/api/thesis/getProgress',
        method: 'post',
        data
    })
}

export function paragraphDelete(data) {
    return request({
        url: '/api/thesis/paragraphDelete',
        method: 'post',
        data
    })
}

export function paragraphMove(data) {
    return request({
        url: '/api/thesis/paragraphMove',
        method: 'post',
        data
    })
}

export function saveThanks(data) {
    return request({
        url: '/api/thesis/saveThanks',
        method: 'post',
        data
    })
}

export function saveReference(data) {
    return request({
        url: '/api/thesis/saveReference',
        method: 'post',
        data
    })
}

export function saveNewParagraph(data) {
    return request({
        url: '/api/thesis/saveNewParagraph',
        method: 'post',
        data
    })
}

export function saveSingleParagraph(data) {
    return request({
        url: '/api/thesis/saveSingleParagraph',
        method: 'post',
        data
    })
}

export function saveDigest(data) {
    return request({
        url: '/api/thesis/saveDigest',
        method: 'post',
        data
    })
}


