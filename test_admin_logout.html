<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员登出测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            color: #666;
            margin-top: 0;
        }
        button {
            background-color: #409EFF;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background-color: #66b1ff;
        }
        button.danger {
            background-color: #F56C6C;
        }
        button.danger:hover {
            background-color: #f78989;
        }
        .log {
            background-color: #f8f8f8;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 4px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success {
            background-color: #f0f9ff;
            border: 1px solid #67c23a;
            color: #67c23a;
        }
        .status.error {
            background-color: #fef0f0;
            border: 1px solid #f56c6c;
            color: #f56c6c;
        }
        .status.warning {
            background-color: #fdf6ec;
            border: 1px solid #e6a23c;
            color: #e6a23c;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 管理员登出功能测试</h1>
        
        <div class="test-section">
            <h3>📋 问题描述</h3>
            <p>管理员登录/登出时出现的问题：</p>
            <ul>
                <li><strong>登出无限循环</strong>：登出API调用失败返回401错误，响应拦截器检测到401错误后自动调用登出，形成无限循环</li>
                <li><strong>登录成功报错</strong>：登录API返回成功响应，但前端将其视为错误处理</li>
                <li><strong>响应格式混乱</strong>：前端对success字段和code字段的处理逻辑不一致</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>🛠️ 修复方案</h3>
            <ul>
                <li><strong>响应拦截器优化</strong>：优先检查success字段，正确处理成功响应</li>
                <li><strong>登出API白名单</strong>：在响应拦截器中添加登出API白名单，避免无限循环</li>
                <li><strong>后端API优化</strong>：修改登出API，即使token无效也返回成功</li>
                <li><strong>前端流程优化</strong>：优化前端登出流程，先清理本地状态再调用API</li>
                <li><strong>请求配置优化</strong>：为登出请求设置较短的超时时间和禁用重试</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>🧪 测试操作</h3>
            <button onclick="testSuccessResponse()">成功响应处理测试</button>
            <button onclick="testNormalLogout()">正常登出测试</button>
            <button onclick="testInvalidTokenLogout()" class="danger">无效Token登出测试</button>
            <button onclick="testMultipleLogout()" class="danger">多次登出测试</button>
            <button onclick="clearLog()">清空日志</button>

            <div id="status" class="status" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>📝 测试日志</h3>
            <div id="log" class="log">等待测试...\n</div>
        </div>
    </div>

    <script>
        function log(message, type = 'info') {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warning' ? '⚠️' : 'ℹ️';
            logElement.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function showStatus(message, type) {
            const statusElement = document.getElementById('status');
            statusElement.textContent = message;
            statusElement.className = `status ${type}`;
            statusElement.style.display = 'block';
            
            setTimeout(() => {
                statusElement.style.display = 'none';
            }, 5000);
        }

        function clearLog() {
            document.getElementById('log').textContent = '日志已清空...\n';
        }

        async function testSuccessResponse() {
            log('开始成功响应处理测试', 'info');

            try {
                log('1. 模拟登录成功响应');
                const loginResponse = {
                    success: true,
                    code: 200,
                    message: '登录成功',
                    data: {
                        token: 'test_token_12345',
                        admin_info: { id: 1, username: 'admin' }
                    }
                };
                log(`   响应数据: ${JSON.stringify(loginResponse)}`);

                log('2. 检查响应处理逻辑');
                if (loginResponse.success === true) {
                    log('   ✅ 成功响应被正确识别');
                } else {
                    log('   ❌ 成功响应识别失败', 'error');
                }

                log('3. 模拟登出成功响应');
                const logoutResponse = {
                    success: true,
                    code: 200,
                    message: '登出成功',
                    data: null
                };
                log(`   响应数据: ${JSON.stringify(logoutResponse)}`);

                if (logoutResponse.success === true) {
                    log('   ✅ 登出成功响应被正确识别');
                } else {
                    log('   ❌ 登出成功响应识别失败', 'error');
                }

                log('成功响应处理测试完成', 'success');
                showStatus('成功响应处理测试通过', 'success');

            } catch (error) {
                log(`成功响应处理测试失败: ${error.message}`, 'error');
                showStatus('成功响应处理测试失败', 'error');
            }
        }

        async function testNormalLogout() {
            log('开始正常登出测试', 'info');
            
            try {
                // 模拟正常的登出流程
                log('1. 检查本地token状态');
                const hasToken = localStorage.getItem('admin_token') || document.cookie.includes('admin_token');
                log(`   本地token状态: ${hasToken ? '存在' : '不存在'}`);
                
                log('2. 模拟调用登出API');
                // 这里应该调用实际的登出API
                log('   API调用成功 (模拟)');
                
                log('3. 清理本地状态');
                localStorage.removeItem('admin_token');
                document.cookie = 'admin_token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
                log('   本地状态已清理');
                
                log('正常登出测试完成', 'success');
                showStatus('正常登出测试通过', 'success');
                
            } catch (error) {
                log(`正常登出测试失败: ${error.message}`, 'error');
                showStatus('正常登出测试失败', 'error');
            }
        }

        async function testInvalidTokenLogout() {
            log('开始无效Token登出测试', 'warning');
            
            try {
                log('1. 设置无效token');
                localStorage.setItem('admin_token', 'invalid_token_12345');
                document.cookie = 'admin_token=invalid_token_12345; path=/';
                log('   已设置无效token');
                
                log('2. 模拟登出API调用（预期返回401）');
                // 模拟401错误
                log('   API返回401错误 (模拟)');
                
                log('3. 检查是否触发无限循环');
                let loopCount = 0;
                const maxLoops = 5;
                
                while (loopCount < maxLoops) {
                    loopCount++;
                    log(`   循环检测 ${loopCount}/${maxLoops}`);
                    await new Promise(resolve => setTimeout(resolve, 100));
                }
                
                log('4. 强制清理本地状态');
                localStorage.removeItem('admin_token');
                document.cookie = 'admin_token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
                log('   本地状态已清理');
                
                log('无效Token登出测试完成 - 未发生无限循环', 'success');
                showStatus('无效Token登出测试通过', 'success');
                
            } catch (error) {
                log(`无效Token登出测试失败: ${error.message}`, 'error');
                showStatus('无效Token登出测试失败', 'error');
            }
        }

        async function testMultipleLogout() {
            log('开始多次登出测试', 'warning');
            
            try {
                log('1. 设置测试token');
                localStorage.setItem('admin_token', 'test_token_12345');
                
                log('2. 连续调用多次登出');
                const promises = [];
                for (let i = 1; i <= 3; i++) {
                    promises.push(
                        new Promise(async (resolve) => {
                            log(`   第${i}次登出调用开始`);
                            await new Promise(r => setTimeout(r, 100 * i)); // 模拟延迟
                            log(`   第${i}次登出调用完成`);
                            resolve();
                        })
                    );
                }
                
                await Promise.all(promises);
                
                log('3. 清理本地状态');
                localStorage.removeItem('admin_token');
                document.cookie = 'admin_token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
                
                log('多次登出测试完成', 'success');
                showStatus('多次登出测试通过', 'success');
                
            } catch (error) {
                log(`多次登出测试失败: ${error.message}`, 'error');
                showStatus('多次登出测试失败', 'error');
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('测试页面加载完成', 'success');
            log('请点击上方按钮开始测试');
        });
    </script>
</body>
</html>
