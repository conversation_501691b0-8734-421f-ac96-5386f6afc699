#!/usr/bin/env python3
"""
调试支付状态问题
检查为什么论文33显示已支付但实际没有支付
"""

import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置环境变量
os.environ["FLASK_ENV"] = "production"

def debug_payment_status():
    """调试支付状态"""
    try:
        import pymysql
        from EarlyBird.config.config import AppConfig
        
        # 解析数据库连接信息
        db_uri = AppConfig.SQLALCHEMY_DATABASE_URI
        
        # 从URI中提取连接信息
        import re
        match = re.match(r'mysql\+pymysql://([^:]+):([^@]+)@([^:]+):(\d+)/([^?]+)', db_uri)
        if not match:
            print("❌ 无法解析数据库连接URI")
            return False
        
        username, password, host, port, database = match.groups()
        port = int(port)
        
        # 连接数据库
        connection = pymysql.connect(
            host=host,
            port=port,
            user=username,
            password=password,
            database=database,
            charset='utf8mb4'
        )
        
        cursor = connection.cursor()
        
        print("🔍 调试用户14论文33的支付状态...")
        
        # 1. 检查下载记录
        print("\n📝 检查下载记录:")
        cursor.execute("""
            SELECT id, thesis_id, uid, is_paid, price, payment_method, payment_order_id, payment_time, create_time
            FROM earlybird_paper_thesis_download_record 
            WHERE uid = 14 AND thesis_id = 33
            ORDER BY create_time DESC
        """)
        
        download_records = cursor.fetchall()
        
        if download_records:
            print(f"  找到 {len(download_records)} 条下载记录:")
            for record in download_records:
                record_id, thesis_id, uid, is_paid, price, payment_method, payment_order_id, payment_time, create_time = record
                print(f"    记录ID: {record_id}")
                print(f"    论文ID: {thesis_id}, 用户ID: {uid}")
                print(f"    是否已支付: {is_paid}")
                print(f"    价格: {price}")
                print(f"    支付方式: {payment_method}")
                print(f"    订单号: {payment_order_id}")
                print(f"    支付时间: {payment_time}")
                print(f"    创建时间: {create_time}")
                print()
        else:
            print("  ❌ 未找到下载记录")
        
        # 2. 检查支付记录
        print("💰 检查支付记录:")
        cursor.execute("""
            SELECT id, user_id, product_id, amount, out_trade_no, status, pay_time, create_time
            FROM earlybird_paper_payment 
            WHERE user_id = 14 AND (product_id LIKE '%thesis_download%' OR product_id LIKE '%33%')
            ORDER BY create_time DESC
        """)
        
        payment_records = cursor.fetchall()
        
        if payment_records:
            print(f"  找到 {len(payment_records)} 条支付记录:")
            for record in payment_records:
                record_id, user_id, product_id, amount, out_trade_no, status, pay_time, create_time = record
                print(f"    支付ID: {record_id}")
                print(f"    用户ID: {user_id}")
                print(f"    产品ID: {product_id}")
                print(f"    金额: {amount}")
                print(f"    订单号: {out_trade_no}")
                print(f"    状态: {status} ({'已支付' if status == 1 else '未支付'})")
                print(f"    支付时间: {pay_time}")
                print(f"    创建时间: {create_time}")
                print()
        else:
            print("  ❌ 未找到支付记录")
        
        # 3. 检查是否有订单号匹配的问题
        if download_records and payment_records:
            print("🔗 检查订单号匹配:")
            for download_record in download_records:
                payment_order_id = download_record[6]  # payment_order_id
                if payment_order_id:
                    matching_payment = None
                    for payment_record in payment_records:
                        if payment_record[4] == payment_order_id:  # out_trade_no
                            matching_payment = payment_record
                            break
                    
                    if matching_payment:
                        print(f"  ✅ 下载记录订单号 {payment_order_id} 找到匹配的支付记录")
                        print(f"      支付状态: {matching_payment[5]} ({'已支付' if matching_payment[5] == 1 else '未支付'})")
                    else:
                        print(f"  ❌ 下载记录订单号 {payment_order_id} 未找到匹配的支付记录")
        
        # 4. 模拟后端逻辑判断
        print("\n🧠 模拟后端逻辑判断:")
        
        if download_records:
            download_record = download_records[0]  # 最新的记录
            is_paid = download_record[2]  # is_paid
            payment_method = download_record[5]  # payment_method
            payment_order_id = download_record[6]  # payment_order_id
            
            print(f"  最新下载记录: is_paid={is_paid}, payment_method={payment_method}")
            
            if is_paid:
                if payment_method not in ['free_first_time', 'vip_free', 'free_disabled']:
                    print(f"  → 付费记录，需要验证支付订单")
                    if payment_order_id:
                        # 查找对应的支付记录
                        matching_payment = None
                        for payment_record in payment_records:
                            if payment_record[4] == payment_order_id:  # out_trade_no
                                matching_payment = payment_record
                                break
                        
                        if matching_payment and matching_payment[5] == 1:  # status == 1
                            print(f"  → 结果: 已支付，可以下载")
                        else:
                            print(f"  → 结果: 支付订单状态异常，应该重置为未支付")
                    else:
                        print(f"  → 结果: 没有支付订单号，应该重置为未支付")
                else:
                    print(f"  → 免费记录，可以下载")
            else:
                print(f"  → 未支付，需要支付")
        
        cursor.close()
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ 调试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 支付状态调试工具")
    print("=" * 50)
    
    debug_payment_status()

if __name__ == "__main__":
    main()
