#!/usr/bin/env python3
"""
修复价格配置问题
将数据库中的价格配置更新为13.88
"""

import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置环境变量
os.environ["FLASK_ENV"] = "production"

def update_price_config():
    """更新价格配置"""
    try:
        import pymysql
        from EarlyBird.config.config import AppConfig
        
        # 解析数据库连接信息
        db_uri = AppConfig.SQLALCHEMY_DATABASE_URI
        
        # 从URI中提取连接信息
        import re
        match = re.match(r'mysql\+pymysql://([^:]+):([^@]+)@([^:]+):(\d+)/([^?]+)', db_uri)
        if not match:
            print("❌ 无法解析数据库连接URI")
            return False
        
        username, password, host, port, database = match.groups()
        port = int(port)
        
        # 连接数据库
        connection = pymysql.connect(
            host=host,
            port=port,
            user=username,
            password=password,
            database=database,
            charset='utf8mb4'
        )
        
        cursor = connection.cursor()
        
        print("🔧 修复价格配置...")
        
        # 查询当前配置
        cursor.execute("""
            SELECT id, config_value, update_time
            FROM earlybird_paper_payment_config 
            WHERE config_key = 'thesis.download.price' AND is_deleted = 0
        """)
        
        current_config = cursor.fetchone()
        
        if current_config:
            config_id, current_value, update_time = current_config
            print(f"📋 当前配置:")
            print(f"  ID: {config_id}")
            print(f"  当前值: '{current_value}'")
            print(f"  更新时间: {update_time}")
            
            # 更新配置值为13.88
            from datetime import datetime
            now = datetime.now()
            
            cursor.execute("""
                UPDATE earlybird_paper_payment_config 
                SET config_value = %s, update_time = %s
                WHERE id = %s
            """, ('13.88', now, config_id))
            
            connection.commit()
            
            print(f"✅ 配置已更新:")
            print(f"  新值: '13.88'")
            print(f"  更新时间: {now}")
            
            # 验证更新结果
            cursor.execute("""
                SELECT config_value, update_time
                FROM earlybird_paper_payment_config 
                WHERE id = %s
            """, (config_id,))
            
            updated_config = cursor.fetchone()
            if updated_config:
                new_value, new_update_time = updated_config
                print(f"🎯 验证结果:")
                print(f"  配置值: '{new_value}'")
                print(f"  更新时间: {new_update_time}")
                print(f"  更新成功: {'✅ 是' if new_value == '13.88' else '❌ 否'}")
            
        else:
            print("❌ 未找到价格配置记录")
            return False
        
        cursor.close()
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ 更新价格配置失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def update_first_free_config():
    """更新首次免费配置"""
    try:
        import pymysql
        from EarlyBird.config.config import AppConfig
        
        # 解析数据库连接信息
        db_uri = AppConfig.SQLALCHEMY_DATABASE_URI
        
        # 从URI中提取连接信息
        import re
        match = re.match(r'mysql\+pymysql://([^:]+):([^@]+)@([^:]+):(\d+)/([^?]+)', db_uri)
        if not match:
            print("❌ 无法解析数据库连接URI")
            return False
        
        username, password, host, port, database = match.groups()
        port = int(port)
        
        # 连接数据库
        connection = pymysql.connect(
            host=host,
            port=port,
            user=username,
            password=password,
            database=database,
            charset='utf8mb4'
        )
        
        cursor = connection.cursor()
        
        print("\n🔧 修复首次免费配置...")
        
        # 查询当前配置
        cursor.execute("""
            SELECT id, config_value, update_time
            FROM earlybird_paper_payment_config 
            WHERE config_key = 'thesis.download.first_free' AND is_deleted = 0
        """)
        
        current_config = cursor.fetchone()
        
        if current_config:
            config_id, current_value, update_time = current_config
            print(f"📋 当前首次免费配置:")
            print(f"  ID: {config_id}")
            print(f"  当前值: '{current_value}'")
            print(f"  更新时间: {update_time}")
            
            # 更新配置值为true（根据管理面板显示）
            from datetime import datetime
            now = datetime.now()
            
            cursor.execute("""
                UPDATE earlybird_paper_payment_config 
                SET config_value = %s, update_time = %s
                WHERE id = %s
            """, ('true', now, config_id))
            
            connection.commit()
            
            print(f"✅ 首次免费配置已更新:")
            print(f"  新值: 'true'")
            print(f"  更新时间: {now}")
            
        cursor.close()
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ 更新首次免费配置失败: {str(e)}")
        return False

def update_vip_free_config():
    """更新VIP免费配置"""
    try:
        import pymysql
        from EarlyBird.config.config import AppConfig
        
        # 解析数据库连接信息
        db_uri = AppConfig.SQLALCHEMY_DATABASE_URI
        
        # 从URI中提取连接信息
        import re
        match = re.match(r'mysql\+pymysql://([^:]+):([^@]+)@([^:]+):(\d+)/([^?]+)', db_uri)
        if not match:
            print("❌ 无法解析数据库连接URI")
            return False
        
        username, password, host, port, database = match.groups()
        port = int(port)
        
        # 连接数据库
        connection = pymysql.connect(
            host=host,
            port=port,
            user=username,
            password=password,
            database=database,
            charset='utf8mb4'
        )
        
        cursor = connection.cursor()
        
        print("\n🔧 修复VIP免费配置...")
        
        # 查询当前配置
        cursor.execute("""
            SELECT id, config_value, update_time
            FROM earlybird_paper_payment_config 
            WHERE config_key = 'thesis.download.vip_free' AND is_deleted = 0
        """)
        
        current_config = cursor.fetchone()
        
        if current_config:
            config_id, current_value, update_time = current_config
            print(f"📋 当前VIP免费配置:")
            print(f"  ID: {config_id}")
            print(f"  当前值: '{current_value}'")
            print(f"  更新时间: {update_time}")
            
            # 更新配置值为true（根据管理面板显示）
            from datetime import datetime
            now = datetime.now()
            
            cursor.execute("""
                UPDATE earlybird_paper_payment_config 
                SET config_value = %s, update_time = %s
                WHERE id = %s
            """, ('true', now, config_id))
            
            connection.commit()
            
            print(f"✅ VIP免费配置已更新:")
            print(f"  新值: 'true'")
            print(f"  更新时间: {now}")
            
        cursor.close()
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ 更新VIP免费配置失败: {str(e)}")
        return False

def verify_all_configs():
    """验证所有配置"""
    try:
        import pymysql
        from EarlyBird.config.config import AppConfig
        
        # 解析数据库连接信息
        db_uri = AppConfig.SQLALCHEMY_DATABASE_URI
        
        # 从URI中提取连接信息
        import re
        match = re.match(r'mysql\+pymysql://([^:]+):([^@]+)@([^:]+):(\d+)/([^?]+)', db_uri)
        if not match:
            print("❌ 无法解析数据库连接URI")
            return False
        
        username, password, host, port, database = match.groups()
        port = int(port)
        
        # 连接数据库
        connection = pymysql.connect(
            host=host,
            port=port,
            user=username,
            password=password,
            database=database,
            charset='utf8mb4'
        )
        
        cursor = connection.cursor()
        
        print("\n🎯 验证所有配置...")
        
        # 查询所有相关配置
        cursor.execute("""
            SELECT config_key, config_value, update_time
            FROM earlybird_paper_payment_config 
            WHERE config_key IN (
                'thesis.download.is_active',
                'thesis.download.price',
                'thesis.download.first_free',
                'thesis.download.vip_free'
            ) AND is_deleted = 0
            ORDER BY config_key
        """)
        
        configs = cursor.fetchall()
        
        print(f"📋 最终配置验证:")
        expected_configs = {
            'thesis.download.is_active': 'true',
            'thesis.download.price': '13.88',
            'thesis.download.first_free': 'true',
            'thesis.download.vip_free': 'true'
        }
        
        all_correct = True
        for config in configs:
            config_key, config_value, update_time = config
            expected_value = expected_configs.get(config_key, 'unknown')
            is_correct = config_value == expected_value
            status = "✅ 正确" if is_correct else "❌ 错误"
            
            print(f"  {config_key}: '{config_value}' (期望: '{expected_value}') {status}")
            print(f"    更新时间: {update_time}")
            
            if not is_correct:
                all_correct = False
        
        cursor.close()
        connection.close()
        
        return all_correct
        
    except Exception as e:
        print(f"❌ 验证配置失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🔧 价格配置修复工具")
    print("=" * 80)
    
    # 1. 更新价格配置
    price_ok = update_price_config()
    
    # 2. 更新首次免费配置
    first_free_ok = update_first_free_config()
    
    # 3. 更新VIP免费配置
    vip_free_ok = update_vip_free_config()
    
    # 4. 验证所有配置
    verify_ok = verify_all_configs()
    
    print("\n" + "=" * 80)
    print("🎯 修复结果:")
    print(f"  价格配置: {'✅ 成功' if price_ok else '❌ 失败'}")
    print(f"  首次免费: {'✅ 成功' if first_free_ok else '❌ 失败'}")
    print(f"  VIP免费: {'✅ 成功' if vip_free_ok else '❌ 失败'}")
    print(f"  配置验证: {'✅ 通过' if verify_ok else '❌ 失败'}")
    
    if price_ok and first_free_ok and vip_free_ok and verify_ok:
        print("\n🎉 所有配置修复成功！")
        print("现在系统应该:")
        print("  • 显示正确的价格 ¥13.88")
        print("  • 新用户首次下载免费")
        print("  • VIP用户免费下载")
    else:
        print("\n⚠️ 部分配置修复失败，请检查错误信息")
    
    print("\n🔄 请重启服务器以使配置生效")

if __name__ == "__main__":
    main()
