#!/usr/bin/env python3
"""
初始化模型设置脚本
用于创建默认的模型配置和API Key设置
"""

import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def init_model_settings():
    """初始化模型设置"""
    try:
        # 直接操作数据库
        import sqlite3
        
        # 连接数据库
        db_path = "EarlyBird/resources/database/earlybird_paper.db"
        if not os.path.exists(db_path):
            print(f"❌ 数据库文件不存在: {db_path}")
            return
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔧 开始初始化模型设置...")
        
        # 检查表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='earlybird_paper_setting'")
        if not cursor.fetchone():
            print("❌ 设置表不存在")
            return
        
        # 要初始化的设置项
        default_settings = [
            ("modelName", "qianwen", "默认AI模型"),
            ("apikeyQianwen", "", "千问API Key"),
            ("apikeyDeepSeekR1", "", "DeepSeek R1 API Key"),
            ("apikeyKimi", "", "Kimi API Key"),
            ("apikeyDoubao", "", "豆包API Key"),
            ("apikeyOpenai", "", "OpenAI API Key")
        ]
        
        for setting_key, default_value, description in default_settings:
            # 检查是否已存在
            cursor.execute("SELECT id FROM earlybird_paper_setting WHERE settingKey = ?", (setting_key,))
            existing = cursor.fetchone()
            
            if existing:
                print(f"📋 设置项已存在: {setting_key}")
            else:
                # 创建新设置项
                cursor.execute("""
                    INSERT INTO earlybird_paper_setting 
                    (settingKey, settingValue, description, create_time, update_time, is_deleted, status) 
                    VALUES (?, ?, ?, datetime('now'), datetime('now'), 0, 1)
                """, (setting_key, default_value, description))
                print(f"✅ 创建设置项: {setting_key} = {default_value}")
        
        # 提交更改
        conn.commit()
        
        # 验证设置
        print("\n🔍 验证设置结果:")
        cursor.execute("SELECT settingKey, settingValue FROM earlybird_paper_setting WHERE settingKey IN ('modelName', 'apikeyQianwen', 'apikeyDeepSeekR1', 'apikeyKimi', 'apikeyDoubao', 'apikeyOpenai')")
        settings = cursor.fetchall()
        
        for key, value in settings:
            print(f"  {key}: {value if value else '(空)'}")
        
        conn.close()
        print("\n🎉 模型设置初始化完成！")
        
    except Exception as e:
        print(f"❌ 初始化失败: {str(e)}")
        import traceback
        traceback.print_exc()

def check_current_settings():
    """检查当前设置"""
    try:
        import sqlite3
        
        db_path = "EarlyBird/resources/database/earlybird_paper.db"
        if not os.path.exists(db_path):
            print(f"❌ 数据库文件不存在: {db_path}")
            return
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔍 检查当前模型设置...")
        
        # 查询所有相关设置
        cursor.execute("""
            SELECT settingKey, settingValue, description 
            FROM earlybird_paper_setting 
            WHERE settingKey LIKE '%model%' OR settingKey LIKE '%apikey%'
            ORDER BY settingKey
        """)
        
        settings = cursor.fetchall()
        
        if not settings:
            print("❌ 未找到任何模型相关设置")
        else:
            print(f"📋 找到 {len(settings)} 个相关设置:")
            for key, value, desc in settings:
                display_value = value if value else "(空)"
                if "apikey" in key.lower() and value:
                    display_value = value[:10] + "..." if len(value) > 10 else value
                print(f"  {key}: {display_value} ({desc})")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 检查失败: {str(e)}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("🔧 模型设置初始化工具")
    print("=" * 50)
    
    # 检查当前设置
    check_current_settings()
    
    print("\n" + "=" * 50)
    
    # 初始化设置
    init_model_settings()
    
    print("\n" + "=" * 50)
    
    # 再次检查
    print("🔍 验证初始化结果...")
    check_current_settings()

if __name__ == "__main__":
    main()
