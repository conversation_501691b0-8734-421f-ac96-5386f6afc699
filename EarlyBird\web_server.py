import logging
import os
import sys
from flask import send_from_directory, redirect, Flask
from datetime import timedelta

# 添加项目根目录到 Python 路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.insert(0, project_root)
sys.path.insert(0, current_dir)

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s  %(filename)s %(lineno)s: %(levelname)s  %(message)s [%(threadName)s]",
    datefmt="%Y-%m-%d %H:%M:%S",
)

logger = logging.getLogger(__name__)

# 使用新的环境变量配置
os.environ["FLASK_DEBUG"] = "0"  # 生产环境设为0，开发环境设为1
os.environ["FLASK_ENV"] = "production"  # 添加这个，配置文件需要

def create_custom_app():
    """创建自定义Flask应用，确保静态文件路由正确注册"""
    try:
        # 静态文件路径
        current_dir = os.path.dirname(os.path.abspath(__file__))
        static_dir = os.path.join(current_dir, "resources", "ui")
        
        logger.info(f"Static directory: {static_dir}")
        logger.info(f"Static directory exists: {os.path.exists(static_dir)}")
        
        # 确保工作目录正确
        os.chdir(current_dir)
        logger.info(f"设置工作目录: {os.getcwd()}")
        
        # 创建Flask应用
        app = Flask(__name__, static_folder=static_dir, static_url_path='/static')
        
        # 使用统一的初始化函数进行配置
        from EarlyBird.common.init_flask import init_app
        init_app(app, None)
        
        # 其他配置（SECRET_KEY已在init_app中设置，这里不再重复设置）
        app.config["SEND_FILE_MAX_AGE_DEFAULT"] = 1
        
        # 设置CORS - 确保支持credentials和cookies
        from flask_cors import CORS
        CORS(app, 
             supports_credentials=True,
             origins=["http://127.0.0.1:3301", "http://localhost:3301", "http://localhost:3000", "http://127.0.0.1:3000"],
             allow_headers=["Content-Type", "Authorization", "X-Requested-With"],
             expose_headers=["Content-Type", "Set-Cookie"],
             methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"]
        )

        # 注册配置和数据库（但不注册钩子函数）
        from EarlyBird.ExtendRegister.conf_register import register_config
        register_config(app)

        from EarlyBird.ExtendRegister.db_register import register_db, db
        register_db(app)
        
        from flask_migrate import Migrate
        migrate = Migrate(app, db)

        # 先注册静态文件路由（要最先注册）
        @app.route('/', defaults={'path': ''})
        @app.route('/<path:path>')
        def serve_spa(path):
            logger.info(f"Serving request for path: {path}")
            
            # 如果是API路径，跳过静态文件处理
            if path.startswith('api/'):
                logger.info(f"Skipping static file handling for API path: {path}")
                from flask import abort
                abort(404)  # 让API路由处理
            
            if path != "" and os.path.exists(os.path.join(static_dir, path)):
                logger.info(f"Serving static file: {path}")
                return send_from_directory(static_dir, path)
            else:
                logger.info(f"Serving index.html for path: {path}")
                return send_from_directory(static_dir, 'index.html')

        # 添加支付测试页面路由
        @app.route('/test-payment')
        def test_payment():
            return '''<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>支付功能测试</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; background-color: #f5f5f5; }
        .container { background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #333; text-align: center; margin-bottom: 30px; }
        .section { margin-bottom: 30px; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
        .section h3 { color: #666; margin-top: 0; }
        button { background-color: #409EFF; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; margin-right: 10px; margin-bottom: 10px; }
        button:hover { background-color: #66b1ff; }
        button.success { background-color: #67c23a; }
        button.success:hover { background-color: #85ce61; }
        .input-group { margin: 10px 0; }
        .input-group label { display: inline-block; width: 100px; font-weight: bold; }
        .input-group input { padding: 8px; border: 1px solid #ddd; border-radius: 4px; width: 200px; }
        .result { background-color: #f8f8f8; border: 1px solid #ddd; padding: 15px; border-radius: 4px; font-family: monospace; font-size: 12px; white-space: pre-wrap; max-height: 300px; overflow-y: auto; margin-top: 10px; }
        .status { padding: 10px; border-radius: 4px; margin: 10px 0; font-weight: bold; }
        .status.success { background-color: #f0f9ff; border: 1px solid #67c23a; color: #67c23a; }
        .status.error { background-color: #fef0f0; border: 1px solid #f56c6c; color: #f56c6c; }
        .status.warning { background-color: #fdf6ec; border: 1px solid #e6a23c; color: #e6a23c; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 支付功能测试</h1>

        <div class="section">
            <h3>📝 测试参数</h3>
            <div class="input-group">
                <label>用户ID:</label>
                <input type="number" id="userId" value="14" />
            </div>
            <div class="input-group">
                <label>论文ID:</label>
                <input type="number" id="thesisId" value="33" />
            </div>
        </div>

        <div class="section">
            <h3>🔧 测试操作</h3>
            <button onclick="testDownloadCheck()">1. 检查下载权限</button>
            <button onclick="testCreatePayment()">2. 创建支付订单</button>
            <button onclick="testPaymentStatus()">3. 查询支付状态</button>
            <button onclick="testSimulatePayment()" class="success">4. 模拟支付成功</button>
            <button onclick="testDownloadAfterPay()">5. 支付后下载</button>
            <button onclick="clearResult()">清空结果</button>

            <div id="test-status" class="status warning">等待测试操作...</div>
        </div>

        <div class="section">
            <h3>📊 测试结果</h3>
            <div id="test-result" class="result">等待测试结果...\\n</div>
        </div>
    </div>

    <script>
        let currentOrderId = '';

        function log(message, type = 'info') {
            const resultElement = document.getElementById('test-result');
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warning' ? '⚠️' : 'ℹ️';
            resultElement.textContent += `[${timestamp}] ${prefix} ${message}\\n`;
            resultElement.scrollTop = resultElement.scrollHeight;
        }

        function showStatus(message, type) {
            const statusElement = document.getElementById('test-status');
            statusElement.textContent = message;
            statusElement.className = `status ${type}`;
        }

        function clearResult() {
            document.getElementById('test-result').textContent = '日志已清空...\\n';
            showStatus('等待测试操作...', 'warning');
        }

        function getParams() {
            return {
                userId: parseInt(document.getElementById('userId').value),
                thesisId: parseInt(document.getElementById('thesisId').value)
            };
        }

        async function testDownloadCheck() {
            const params = getParams();
            log(`检查下载权限 - 用户: ${params.userId}, 论文: ${params.thesisId}`, 'info');

            try {
                const response = await fetch('/api/thesis/downloadThesis', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    credentials: 'include',
                    body: JSON.stringify({ thesisId: params.thesisId })
                });

                const result = await response.json();

                if (result.is_success) {
                    if (result.data.need_payment) {
                        log(`需要支付: ${result.data.price} 元`, 'warning');
                        log(`原因: ${result.data.reason || '未提供原因'}`, 'info');
                        showStatus(`需要支付 ${result.data.price} 元`, 'warning');
                    } else {
                        log(`可以免费下载: ${result.data.file}`, 'success');
                        log(`免费原因: ${result.data.free_reason}`, 'info');
                        showStatus('可以免费下载', 'success');
                    }
                } else {
                    log(`检查失败: ${result.message}`, 'error');
                    showStatus(`检查失败: ${result.message}`, 'error');
                }
            } catch (error) {
                log(`请求失败: ${error.message}`, 'error');
                showStatus(`请求失败: ${error.message}`, 'error');
            }
        }

        async function testCreatePayment() {
            const params = getParams();
            log(`创建支付订单 - 用户: ${params.userId}, 论文: ${params.thesisId}`, 'info');

            try {
                const response = await fetch('/api/thesis/payDownload', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    credentials: 'include',
                    body: JSON.stringify({ thesisId: params.thesisId, paymentMethod: 'wechat' })
                });

                const result = await response.json();

                if (result.is_success) {
                    currentOrderId = result.data.order_id;
                    log(`支付订单创建成功: ${currentOrderId}`, 'success');
                    log(`支付金额: ${result.data.price} 元`, 'info');
                    showStatus(`订单创建成功: ${currentOrderId}`, 'success');
                } else {
                    log(`创建失败: ${result.message}`, 'error');
                    showStatus(`创建失败: ${result.message}`, 'error');
                }
            } catch (error) {
                log(`请求失败: ${error.message}`, 'error');
                showStatus(`请求失败: ${error.message}`, 'error');
            }
        }

        async function testPaymentStatus() {
            const params = getParams();
            const orderId = currentOrderId || prompt('请输入订单号:');

            if (!orderId) {
                log('请先创建支付订单或输入订单号', 'error');
                return;
            }

            log(`查询支付状态 - 订单: ${orderId}`, 'info');

            try {
                const response = await fetch('/api/thesis/paymentStatus', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    credentials: 'include',
                    body: JSON.stringify({ thesisId: params.thesisId, orderId: orderId })
                });

                const result = await response.json();

                if (result.is_success) {
                    log(`支付状态: ${result.data.payment_status}`, 'info');
                    log(`是否已支付: ${result.data.is_paid}`, 'info');

                    if (result.data.is_paid) {
                        showStatus('支付已完成', 'success');
                    } else {
                        showStatus('支付未完成', 'warning');
                    }
                } else {
                    log(`查询失败: ${result.message}`, 'error');
                    showStatus(`查询失败: ${result.message}`, 'error');
                }
            } catch (error) {
                log(`请求失败: ${error.message}`, 'error');
                showStatus(`请求失败: ${error.message}`, 'error');
            }
        }

        async function testSimulatePayment() {
            const orderId = currentOrderId || prompt('请输入订单号:');

            if (!orderId) {
                log('请先创建支付订单或输入订单号', 'error');
                return;
            }

            log(`模拟支付成功 - 订单: ${orderId}`, 'info');

            try {
                const response = await fetch('/api/thesis/simulatePayment', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    credentials: 'include',
                    body: JSON.stringify({ order_id: orderId })
                });

                const result = await response.json();

                if (result.is_success) {
                    log(`模拟支付成功`, 'success');
                    showStatus('模拟支付成功', 'success');
                } else {
                    log(`模拟支付失败: ${result.message}`, 'error');
                    showStatus(`模拟支付失败: ${result.message}`, 'error');
                }
            } catch (error) {
                log(`请求失败: ${error.message}`, 'error');
                showStatus(`请求失败: ${error.message}`, 'error');
            }
        }

        async function testDownloadAfterPay() {
            log('测试支付后下载...', 'info');
            await testDownloadCheck();
        }

        document.addEventListener('DOMContentLoaded', function() {
            log('🧪 支付功能测试工具已加载', 'success');
            log('📋 请按顺序执行测试步骤', 'info');
        });
    </script>
</body>
</html>'''

        # 然后注册API蓝图（这样API路由能够正确处理）
        from EarlyBird.ExtendRegister.bp_register import register_bp as register_blueprints
        register_blueprints(app)

        # 最后注册钩子函数（这样不会影响静态文件路由）
        from EarlyBird.ExtendRegister.hook_register import register_hook
        register_hook(app)

        # 注册异常处理
        from EarlyBird.ExtendRegister.excep_register import register_excep
        register_excep(app)

        return app
    except Exception as e:
        logger.exception("创建应用失败")
        return None

def run_web_server():
    try:
        from common.init_flask import printEnvInfo
        printEnvInfo()
        
        app = create_custom_app()
        
        if app is None:
            logger.error("Failed to create Flask app")
            return

        from task.task_api_server import TaskApiServer
        from task.task_paper_generator import TaskPaperGenerator
        from threading import Event

        stop_event = Event()
        TaskApiServer(stop_event, app).start()
        TaskPaperGenerator(stop_event, app).start()

        # 直接运行 Flask 服务器
        print("\n=== 服务器已启动 ===")
        print("请在浏览器中访问: http://127.0.0.1:3301")
        print("=================\n")
        
        # 禁用自动重载，使用 waitress 作为生产服务器
        from waitress import serve
        serve(app, host="127.0.0.1", port=3301)

    except Exception as e:
        logger.exception(e)
        logger.error(f"catch exception when running web server: {str(e)}")

if __name__ == "__main__":
    run_web_server() 