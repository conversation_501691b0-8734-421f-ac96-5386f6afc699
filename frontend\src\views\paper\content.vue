<template>
  <!-- 生成论文架构的VUE子模块，这个模块定义了调用服务器的方法和路由，具体在/api/generate中进行了定义 -->
  <div class="flex">
    <!---home-main-->
    <div class="home-main grid-content bg-purple-light">
      <!-- 论文信息区域 -->
      <div class="thesis-info-section" v-if="selecedThesis.id">
        <div class="thesis-info-header">
          <div class="thesis-basic-info">
            <div class="info-row">
              <div class="info-item">
                <span class="label">编号：</span>
                <span class="value">{{ selecedThesis.id }}</span>
              </div>
              <div class="info-item">
                <span class="label">水平：</span>
                <span class="value">{{ selecedThesis.level }}</span>
              </div>
              <div class="info-item">
                <span class="label">语言：</span>
                <span class="value">{{ selecedThesis.lang }}</span>
              </div>
              <div class="info-item">
                <span class="label">总字数：</span>
                <span class="value">{{ totalLength }}</span>
              </div>
            </div>
            <div class="info-row">
              <div class="info-item title-item">
                <span class="label">标题：</span>
                <span class="value title-text">{{ selecedThesis.title }}</span>
              </div>
            </div>
          </div>
          
          <!-- 用户下载权限信息面板 -->
          <div class="thesis-download-rights-wrapper">
            <UserDownloadRights
              :thesisId="selecedThesisId"
              ref="downloadRights"
              v-if="selecedThesisId > 0"
            />
          </div>
        </div>
        
        <!-- 操作按钮区域 -->
        <div class="thesis-actions-container">
          <div class="thesis-actions">
            <el-button
              v-if="!statusIsRunning()"
              type="primary"
              @click="handlerGenerateAll"
              class="action-btn"
              size="small"
            >
              一键生成
            </el-button>
            <el-button
              type="primary"
              @click="onlyShowTitle = !onlyShowTitle"
              class="action-btn"
              size="small"
            >
              {{ onlyShowTitle ? "文本模式" : "大纲模式" }}
            </el-button>
            <el-button
              type="primary"
              @click="handlerDownload"
              class="action-btn"
              size="small"
            >
              下载
            </el-button>

            <!-- 段落字数控制设置 -->
            <el-popover
              placement="bottom"
              width="400"
              trigger="click"
              v-model="paragraphLengthSettingsVisible"
            >
              <div class="paragraph-length-settings">
                <h4 style="margin: 0 0 15px 0; color: #333;">段落字数控制设置</h4>
                <el-form label-width="120px" size="small">
                  <el-form-item label="默认段落字数">
                    <el-input-number
                      v-model="globalParagraphLength"
                      :min="100"
                      :max="3000"
                      :step="50"
                      controls-position="right"
                      style="width: 150px;"
                    ></el-input-number>
                    <span style="margin-left: 10px; color: #666;">字</span>
                  </el-form-item>
                  <el-form-item label="字数控制模式">
                    <el-radio-group v-model="lengthControlMode" size="small">
                      <el-radio label="fixed">固定字数</el-radio>
                      <el-radio label="range">字数范围</el-radio>
                      <el-radio label="auto">智能调整</el-radio>
                    </el-radio-group>
                  </el-form-item>
                  <el-form-item v-if="lengthControlMode === 'range'" label="字数范围">
                    <div style="display: flex; align-items: center; gap: 10px;">
                      <el-input-number
                        v-model="minParagraphLength"
                        :min="100"
                        :max="maxParagraphLength"
                        :step="50"
                        controls-position="right"
                        style="width: 120px;"
                      ></el-input-number>
                      <span>至</span>
                      <el-input-number
                        v-model="maxParagraphLength"
                        :min="minParagraphLength"
                        :max="3000"
                        :step="50"
                        controls-position="right"
                        style="width: 120px;"
                      ></el-input-number>
                      <span style="color: #666;">字</span>
                    </div>
                  </el-form-item>
                  <el-form-item label="特殊要求">
                    <el-input
                      v-model="globalInstruction"
                      type="textarea"
                      placeholder="对所有段落的生成要求，如：'内容要简洁明了'、'要有具体案例'等"
                      maxlength="200"
                      show-word-limit
                      :rows="3"
                    ></el-input>
                  </el-form-item>
                </el-form>
                <div style="text-align: right; margin-top: 15px;">
                  <el-button size="small" @click="paragraphLengthSettingsVisible = false">取消</el-button>
                  <el-button type="primary" size="small" @click="saveParagraphLengthSettings">保存设置</el-button>
                </div>
              </div>
              <el-button slot="reference" type="info" size="small" icon="el-icon-setting" class="action-btn">
                字数设置
              </el-button>
            </el-popover>
          </div>

          <!-- 生成中状态 -->
          <div v-if="statusIsRunning()" class="thesis-actions">
            <el-button type="primary" plain size="small" class="action-btn">
              <i class="el-icon-loading"></i>一键生成中
            </el-button>
            <el-button
              type="primary"
              plain
              size="small"
              @click="leftVisiable = 'outline'"
              class="action-btn"
            >
              查看进度
            </el-button>
          </div>

          <!-- 始终显示的按钮 -->
          <div class="thesis-actions-secondary">
            <el-button type="primary" @click="leftVisiableToggle('list')" size="small" class="action-btn">
              论文列表
            </el-button>
            <el-button type="primary" @click="leftVisiableToggle('outline')" size="small" class="action-btn">
              论文大纲
            </el-button>
          </div>
        </div>
      </div>

      <div class="result-box-wrapper" v-if="title == ''" style="flex:1">
        <Placeholder></Placeholder>
      </div>

      <!-- 添加论文列表为空时的提示 -->
      <div class="empty-thesis-container" v-if="!thesisList || thesisList.length == 0">
        <div class="empty-thesis-content">
          <i class="el-icon-document-add" style="font-size: 64px; color: #909399; margin-bottom: 20px;"></i>
          <h2>暂无论文</h2>
          <p>您还没有创建任何论文，请先拟定一个论文提纲</p>
          <el-button type="primary" @click="gotoOutline" size="medium" style="margin-top: 20px;">
            去创建论文
          </el-button>
        </div>
      </div>

      <div class="result-box-wrapper" v-if="title != ''" id="resultBox">
        <div class="result-box">
          <PaperCover :title="title" v-if="!onlyShowTitle"></PaperCover>
          <DigestBox
            :digestValue="digest"
            :thesisId="selecedThesisId"
            @onDigestUpdate="onDigestUpdate"
            v-if="!onlyShowTitle"
          ></DigestBox>
          <ParagraphBox
            :onlyShowTitle="onlyShowTitle"
            :thesisId="selecedThesisId"
            :paragraphs="paragraphs"
            :lang="form.lang"
            @onUpdate="onParasUpdate"
            @onReGen="regenerateParagraph"
            @onEdit="onEditPara"
            @onTableTools="onTableTools"
            @findParagraph="findParagraphById"
          >
          </ParagraphBox>

          <div class="delete-box">
            <div class="freeduty">
              免责声明：本网站生成内容均为阿里千问AI大模型生成。无法保证全部正确，无法保证全部真实。仅供学习参考使用，禁止将生成内容直接用于正式严肃场合。
              <br />
              本网站仅提供生成功能，内容的创作由用户自行完成，生成文档版权归用户所有。
            </div>
            <div class="agreement">
              <a href="/api/agreement/privacy" target="_blank">隐私政策协议 </a>
              <a href="/api/agreement/user" target="_blank">用户服务协议 </a>
            </div>
            <el-button type="danger" @click="handlerDelete" size="small">
              删除本论文
            </el-button>
          </div>
        </div>
      </div>
      
      <!-- 微信支付二维码弹框 -->
      <WeChatPayDialog
        :visible.sync="wechatPayDialogVisible"
        :amount="paymentData.price || 10.00"
        :thesisId="paymentData.thesisId"
        @payment-success="handleWeChatPaymentSuccess"
        @close="handleWeChatPaymentClose"
      />
    </div>
    
    <!---右侧提纲的bar -->
    <div class="outline-siderbar left-siderbar" v-if="leftVisiable == 'outline'">
      <div class="outline-siderbar_headeer">
        <i class="el-icon-tickets"></i>{{ $t("contentPage.leftTitleOutline") }}
      </div>
      <OutlineTree :outlineTree="outlineTree"></OutlineTree>
    </div>
    <!---右侧全部论文的bar-->
    <div class="thesis-list-siderbar left-siderbar" v-if="leftVisiable == 'list'">
      <div class="thesis-list-siderbar_header">
        <i class="el-icon-document-copy"></i>{{ $t("contentPage.leftTitleThesisList") }}
      </div>
      <div
        :class="
          selecedThesisId == item.id
            ? 'thesis-list-siderbar_title s'
            : 'thesis-list-siderbar_title '
        "
        v-for="item in thesisList"
        :key="item.id"
        @click="handlerGotoThesisId(item.id)"
      >
        <span>{{ item.id }}.{{ item.title }}</span>
      </div>
    </div>
    
    <!---弹出框，段落编辑-->
    <ParagraphEditBox
      :isShow="editBoxVisible"
      :para="editPara"
      @onClose="onEditBoxClose"
      @onParaUpdated="onParaUpdated"
    ></ParagraphEditBox>

    <!--段落重新生成的dialog--->
    <div class="dialog-box_bg" v-if="paragraphRegenFormVisible" @click="paragraphRegenFormVisible = false"></div>
    <div class="dialog-box" v-if="paragraphRegenFormVisible">
      <div class="dialog-box_header">
        <div class="dialog-box_header_left">AI生成本段内容</div>
        <div class="dialog-box_header_right">
          <i class="el-icon-circle-close" @click="paragraphRegenFormVisible = false"></i>
        </div>
      </div>
      <div class="dialog-box_body">
        <el-form class="paragraph_regen_form">
          <el-form-item label="段落标题">
            <el-input
              v-model="paragraphRegenForm.paragraphTitle"
              placeholder="段落标题"
              disabled
            ></el-input>
          </el-form-item>
          
          <el-form-item label="字数控制方式" required>
            <el-radio-group v-model="paragraphRegenForm.lengthMode" @change="onLengthModeChange">
              <el-radio label="fixed">固定字数</el-radio>
              <el-radio label="range">字数范围</el-radio>
              <el-radio label="auto">智能调整</el-radio>
            </el-radio-group>
          </el-form-item>
          
          <el-form-item v-if="paragraphRegenForm.lengthMode === 'fixed'" label="期望字数" required>
            <el-input-number
              v-model="paragraphRegenForm.length"
              :min="100"
              :max="3000"
              :step="50"
              controls-position="right"
              style="width: 200px;"
              placeholder="请填写本段你期望的字数"
            ></el-input-number>
            <span style="margin-left: 10px; color: #666;">字</span>
          </el-form-item>
          
          <el-form-item v-if="paragraphRegenForm.lengthMode === 'range'" label="字数范围" required>
            <div style="display: flex; align-items: center; gap: 10px;">
              <el-input-number
                v-model="paragraphRegenForm.minLength"
                :min="100"
                :max="paragraphRegenForm.maxLength"
                :step="50"
                controls-position="right"
                style="width: 150px;"
                placeholder="最小字数"
              ></el-input-number>
              <span>至</span>
              <el-input-number
                v-model="paragraphRegenForm.maxLength"
                :min="paragraphRegenForm.minLength"
                :max="3000"
                :step="50"
                controls-position="right"
                style="width: 150px;"
                placeholder="最大字数"
              ></el-input-number>
              <span style="color: #666;">字</span>
            </div>
          </el-form-item>
          
          <el-form-item v-if="paragraphRegenForm.lengthMode === 'auto'" label="智能调整">
            <div style="color: #666; font-size: 12px;">
              系统将根据段落重要性和上下文自动调整字数，预计在 {{ paragraphRegenForm.estimatedLength }} 字左右
            </div>
          </el-form-item>
          
          <el-form-item label="内容风格">
            <el-select v-model="paragraphRegenForm.contentStyle" placeholder="选择内容风格" style="width: 200px;">
              <el-option label="学术严谨" value="academic"></el-option>
              <el-option label="通俗易懂" value="popular"></el-option>
              <el-option label="简洁明了" value="concise"></el-option>
              <el-option label="详细深入" value="detailed"></el-option>
              <el-option label="案例丰富" value="case_rich"></el-option>
            </el-select>
          </el-form-item>
          
          <el-form-item label="给AI的建议和提示:">
            <el-input
              v-model="paragraphRegenForm.instruction"
              type="textarea"
              placeholder="本次生成，你希望AI如何来做呢？(可留空,可以写：'请内容简洁一点','请添加数据来证明','要有案例说明'...等)"
              maxlength="200"
              show-word-limit
              :rows="3"
            ></el-input>
          </el-form-item>
        </el-form>
      </div>
      <div class="dialog-box_footer">
        <el-button
          type="primary"
          size="medium"
          @click="paragraphRegenFormVisible = false"
        >
          取消
        </el-button>

        <el-button type="primary" @click="handlerGenerateSinglePara2">开始生成</el-button>
      </div>
    </div>
    
    <!-- 智能表格编辑器 -->
    <TableEditor
      :visible.sync="tableEditorVisible"
      :context="currentContext"
      @onConfirm="onTableConfirm"
    />
  </div>
</template>

<script>
import { h } from 'vue';
import {
  generateSingleParagraph,
  generateAll,
  generateThanks,
  generateReference,
  stopGenerateAll,
} from "@/api/generate";
import {
  getThesisList,
  getThesisDetail,
  getOutline,
  deleteThesis,
  saveThanks,
  saveReference,
  downloadThesis,
  saveSingleParagraph,
} from "@/api/thesis";
import Placeholder from "../components/Placeholder";
import ParagraphBox from "../components/ParagraphBox.vue";
import ParagraphEditBox from "../components/ParagraphEditBox.vue";
import DigestBox from "../components/DigestBox.vue";
import OutlineTree from "../components/OutlineTree.vue";
import PaperCover from "../components/PaperCover.vue";
import PaperSinglePara from "../components/PaperSinglePara.vue";
import TableEditor from "../components/TableEditor.vue";
import { mapState } from "vuex";
import WeChatPayDialog from '@/components/WeChatPayDialog.vue'
import UserDownloadRights from '@/components/UserDownloadRights.vue'

export default {
  name: "GetContent",
  components: {
    Placeholder,
    ParagraphBox,
    ParagraphEditBox,
    DigestBox,
    OutlineTree,
    PaperCover,
    PaperSinglePara,
    TableEditor,
    WeChatPayDialog,
    UserDownloadRights
  },
  data() {
    // 这是测试数据
    return {
      TaskStatus: {
        DEFAULT: 1,
        INIT: 11,
        RUNING: 12,
        SUCCESS: 13,
        ERROR: 14,
      },
      thesisList: [],

      // 被选中的论文的属性
      title: "",
      digest: {
        digest: "",
        digestEn: "",
        keywords: "",
        keywordsEn: "",
      },
      thanks: "",
      paragraphs: [],
      selecedThesisId: 0,
      selecedThesis: {},
      selecedThesisStatus: "",

      // 表单，已经没有作用了。
      form: {
        title: "自媒体对经济学的影响",
        lang: "中文",
        length: "短(约4000字左右)",
      },
      // 控制编辑框的显示
      editBoxVisible: false,
      editPara: {},

      // 右侧卡片是否开关
      leftVisiable: "outline",

      isVip: true,

      // 右侧提纲列表数据
      outlineTree: {},

      // 进度条弹出，没有用了，进度放在提纲里了。
      progressList: [],

      onlyShowTitle: false, // 只显示提纲

      // 段落重新生成dialog
      paragraphRegenFormVisible: false,
      paragraphRegenForm: {
        paragraphId: "",
        thesisId: "",
        paragraphTitle: "",
        length: 500,
        lengthMode: "fixed",
        minLength: 300,
        maxLength: 800,
        estimatedLength: 500,
        contentStyle: "academic",
        instruction: "",
      },

      // 段落字数控制设置
      paragraphLengthSettingsVisible: false,
      
      // 支付相关数据
      wechatPayDialogVisible: false,
      paymentData: {
        thesisId: 0,
        price: 10.00
      },
      paymentLoading: false
    };
  },

  // 定时器，更新提纲更新状态
  ProgressTimerInterval: null,

  created() {
    this.pageInit();
    this.loadParagraphLengthSettings();
  },
  mounted() {
    // 添加键盘事件监听器，按ESC键关闭对话框
    this.handleKeydown = (event) => {
      if (event.key === 'Escape' && this.paragraphRegenFormVisible) {
        this.paragraphRegenFormVisible = false;
      }
    };
    document.addEventListener('keydown', this.handleKeydown);
    
    // 注意：删除按钮的事件监听器已移至ParagraphBox.vue中处理
    // 避免重复执行删除操作
  },
  beforeDestroy() {
    // 确保组件销毁时关闭对话框
    this.paragraphRegenFormVisible = false;
    // 移除键盘事件监听器
    if (this.handleKeydown) {
      document.removeEventListener('keydown', this.handleKeydown);
    }
    // 清除定时器
    if (this.ProgressTimerInterval) {
      clearInterval(this.ProgressTimerInterval);
      this.ProgressTimerInterval = null;
    }
    if (this.ContentRefreshInterval) {
      clearInterval(this.ContentRefreshInterval);
      this.ContentRefreshInterval = null;
    }
  },
  computed: {
    ...mapState({
      selectedPara: (state) => state.thesis.outlineTreeSelectPara,
    }),

    totalLength() {
      // 安全检查：确保paragraphs存在且为数组
      if (!this.paragraphs || !Array.isArray(this.paragraphs)) {
        return 0;
      }
      
      var getLength = (p) => {
        // 安全检查：确保p存在且有length属性
        if (!p || typeof p.length === 'undefined') {
          return 0;
        }
        
        var r = p.length;
        if (p["subtitle"] != undefined && p["subtitle"] && Array.isArray(p["subtitle"]) && p["subtitle"].length > 0) {
          for (var i in p["subtitle"]) {
            r += getLength(p["subtitle"][i]);
          }
        }
        return r;
      };
      var result = 0;

      for (var i in this.paragraphs) {
        result += getLength(this.paragraphs[i]);
      }
      return result;
    },
  },
  watch: {
    // 点击不同论文，path不变，query变。不执行生命周期，这个监听一下，自己执行，把初始化放在init里。
    // 每次init都重头在再来，防止论文切换，各种幺蛾子切不干净
    $route(n, o) {
      this.pageInit();
    },
    // 提纲中被选中的标题变了，内容页同是滑动到对应的段落
    selectedPara(newVal, oldVal) {
      let oldSelectedPara = document.querySelector(
        '.paragraph_one[data-paraId="' + oldVal.id + '"]'
      );
      if (oldSelectedPara != null) {
        oldSelectedPara.classList.remove("s");
      }
      let selectedPara = document.querySelector(
        '.paragraph_one[data-paraId="' + newVal.id + '"]'
      );
      selectedPara.classList.add("s");

      let count = 0;
      this.$nextTick(() => {
        let resultBox = document.getElementById("resultBox");
        const targetValue = selectedPara.offsetTop - 200;
        const timer = setInterval(() => {
          if (count > 20) {
            clearInterval(timer);
            return;
          }
          var currentValue = resultBox.scrollTop;
          if (targetValue - currentValue < 30 && targetValue - currentValue > -30) {
            clearInterval(timer);
            return;
          }
          count += 1;
          resultBox.scrollTop += (targetValue - currentValue) * 0.1;
        }, 5);
      });
    },
  },
  methods: {
    pageInit() {
      // 登录后才能使用

      let thesisId = -1;
      if (this.$route.query["thesisId"] != undefined) {
        let idFromUrl = parseInt(this.$route.query["thesisId"]);
        if (idFromUrl > 0) {
          thesisId = idFromUrl;
        }
      }
      clearInterval(this.ProgressTimerInterval);
      clearInterval(this.ContentRefreshInterval); // Clear content refresh timer
      
      // 确保关闭所有对话框
      this.paragraphRegenFormVisible = false;
      this.editBoxVisible = false;
      
      this.refreshThesisList(thesisId);
    },
    openLoginBox() {
      alert("need login");
    },
    onSingleParagraphGenerate(type) {
      let api = "";
      let target = "";
      if (type == "thanks") {
        api = generateThanks;
        target = "#signelParaThanks";
      } else if (type == "reference") {
        api = generateReference;
        target = "#signelParaReference";
      }
      if (api != "") {
        var loading = this.$loading({
          text: "AI生成中，请稍后",
          target,
          background: "#000000aa",
        });
        api({ thesisId: this.selecedThesisId })
          .then((res) => {
            loading.close();
            this.refreshCurrentThesis();
          })
          .catch((e) => {
            loading.close();
            this.$notify.error(e);
          });
      }
    },
    onSaveReference(c) {
      var loading = this.$loading({
        text: "保存中",
        target: "#signelParaReference",
        background: "#000000aa",
      });
      saveReference({ thesisId: this.selecedThesisId, content: c })
        .then((res) => {
          loading.close();
          this.refreshCurrentThesis();
        })
        .catch((e) => {
          console.log(e);
          loading.close();
          this.$notify.error(e);
        });
    },
    onSaveThanks(c) {
      var loading = this.$loading({
        text: "保存中",
        target: "#signelParaThanks",
      });
      saveThanks({ thesisId: this.selecedThesisId, content: c })
        .then((res) => {
          loading.close();
          this.refreshCurrentThesis();
        })
        .catch((e) => {
          console.log(e);

          loading.close();
          this.$notify.error(e);
        });
    },
    statusIsRunning() {
      return (
        this.selecedThesisStatus == this.TaskStatus.INIT ||
        this.selecedThesisStatus == this.TaskStatus.RUNING
      );
    },
    // handleTreeClick(p) {
    //   console.log(p);
    // },

    leftVisiableToggle(boxName) {
      this.leftVisiable = boxName;
      localStorage.setItem("contentPageLeftBar", this.leftVisiable);
    },

    gotoOutline() {
      this.$router.push({
        path: "/paper/outline",
      });
    },
    onParasUpdate() {
      this.refreshCurrentThesis();
    },
    onDigestUpdate() {
      this.refreshCurrentThesis();
    },
    onParaUpdated() {
      this.refreshCurrentThesis();
    },
    onEditBoxClose() {
      this.editBoxVisible = false;
    },
    onEditPara(para) {
      this.editBoxVisible = true;
      this.editPara = {
        thesisId: this.selecedThesisId,
        paragraphId: para["id"],
        title: para["title"],
        text: para["text"],
      };
    },

    onTableTools(para) {
      // 设置当前选中的段落
      this.$store.commit("thesis/SET_SELECTED_PARAGRAPH", para);
      
      // 直接进入智能生成页面
      this.showTableEditor();
    },

    // 在所有段落中查找指定ID的段落
    findParagraphById(paraId, callback) {
      console.log('在父组件中查找段落，paraId:', paraId);
      console.log('父组件段落数据:', this.paragraphs);
      console.log('父组件段落数据类型:', typeof this.paragraphs);
      console.log('父组件段落数据长度:', this.paragraphs ? this.paragraphs.length : 'undefined');
      
      // 递归查找段落
      const findParagraphRecursively = (paragraphs) => {
        if (!paragraphs || !Array.isArray(paragraphs)) {
          console.log('段落数据无效:', paragraphs);
          return null;
        }
        
        for (let para of paragraphs) {
          console.log('检查段落:', { id: para.id, title: para.title });
          if (para.id === paraId || String(para.id) === String(paraId)) {
            console.log('找到匹配的段落:', para);
            return para;
          }
          if (para.subtitle && Array.isArray(para.subtitle)) {
            const found = findParagraphRecursively(para.subtitle);
            if (found) return found;
          }
        }
        return null;
      };
      
      const foundParagraph = findParagraphRecursively(this.paragraphs);
      console.log('父组件查找结果:', foundParagraph);
      
      // 调用回调函数返回结果
      if (callback && typeof callback === 'function') {
        console.log('调用回调函数，传递结果:', foundParagraph);
        callback(foundParagraph);
      } else {
        console.error('回调函数无效:', callback);
      }
    },

    showTableEditor() {
      if (!this.selectedPara || !this.selectedPara.id) {
        this.$message.warning('请先选择一个段落');
        return;
      }
      
      // 确保正确获取段落内容
      const para = this.selectedPara;
      const paragraphText = para.text || '';
      const paragraphTitle = para.title || '';
      
      console.log('显示智能表格编辑器，当前段落:', {
        id: para.id,
        title: paragraphTitle,
        text: paragraphText.substring(0, 100) + '...',
        textLength: paragraphText.length
      });
      
      // 构建更详细的上下文信息
      this.currentContext = {
        title: paragraphTitle,
        content: paragraphText,
        paragraphId: para.id,
        thesisId: this.selecedThesisId,
        type: 'paragraph',
        // 添加更多上下文信息
        thesisTitle: this.title,
        paragraphIndex: this.getParagraphIndex(para.id),
        // 提取段落中的关键信息用于表格生成
        keywords: this.extractKeywords(paragraphText),
        dataPoints: this.extractDataPoints(paragraphText)
      };
      
      console.log('传递给TableEditor的上下文:', this.currentContext);
      
      this.tableEditorVisible = true;
      this.$message.info('正在分析段落内容并生成智能表格...');
    },

    // 获取段落索引
    getParagraphIndex(paragraphId) {
      const findIndex = (paragraphs, targetId, currentIndex = 0) => {
        for (let i = 0; i < paragraphs.length; i++) {
          if (paragraphs[i].id === targetId) {
            return currentIndex + i;
          }
          if (paragraphs[i].subtitle && paragraphs[i].subtitle.length > 0) {
            const found = findIndex(paragraphs[i].subtitle, targetId, currentIndex + i + 1);
            if (found !== -1) return found;
          }
        }
        return -1;
      };
      
      return findIndex(this.paragraphs, paragraphId);
    },

    // 提取段落中的关键词
    extractKeywords(text) {
      if (!text) return [];
      
      // 简单的关键词提取逻辑
      const keywords = [];
      const sentences = text.split(/[。！？；]/);
      
      sentences.forEach(sentence => {
        if (sentence.length > 10) {
          // 提取可能的数据相关词汇
          const dataWords = sentence.match(/[0-9]+%?|[一二三四五六七八九十]+%?|增加|减少|上升|下降|提高|降低/g);
          if (dataWords) {
            keywords.push(...dataWords);
          }
        }
      });
      
      return [...new Set(keywords)]; // 去重
    },

    // 提取段落中的数据点
    extractDataPoints(text) {
      if (!text) return [];
      
      const dataPoints = [];
      
      // 提取百分比
      const percentages = text.match(/[0-9]+%|[一二三四五六七八九十]+%/g);
      if (percentages) {
        dataPoints.push(...percentages.map(p => ({ type: 'percentage', value: p })));
      }
      
      // 提取数字
      const numbers = text.match(/[0-9]+/g);
      if (numbers) {
        dataPoints.push(...numbers.map(n => ({ type: 'number', value: n })));
      }
      
      return dataPoints;
    },

    onTableConfirm(tableHtml) {
      console.log('表格确认，HTML内容:', tableHtml);
      
      if (!this.selectedPara || !this.selectedPara.id) {
        this.$message.error('未找到目标段落');
        this.tableEditorVisible = false;
        return;
      }
      
      // 插入表格到段落的方法
      this.insertTableToParagraph(tableHtml);
      this.$message.success('表格已成功插入到段落中！');
      this.tableEditorVisible = false;
    },

    // 插入表格到段落的方法
    insertTableToParagraph(tableHtml) {
      // 获取当前段落的原始内容
      const originalText = this.selectedPara.text || '';
      
      // 直接插入原始表格，不添加删除按钮
      const newText = originalText + '\n\n' + tableHtml;
      
      // 调用API更新段落内容
      const updateData = {
        thesisId: this.selecedThesisId,
        paragraphId: this.selectedPara.id,
        title: this.selectedPara.title,
        text: newText
      };
      
      // 直接使用已导入的API
      saveSingleParagraph(updateData)
        .then((res) => {
          this.$message.success('表格已成功插入到段落中！');
          // 刷新当前论文数据
          this.refreshCurrentThesis();
        })
        .catch((e) => {
          this.$message.error('保存失败: ' + e);
        });
    },

    insertSimpleTable() {
      if (!this.selectedPara || !this.selectedPara.id) {
        this.$message.warning('请先选择一个段落');
        return;
      }
      
      // 基于段落标题生成表格
      const tableTitle = this.selectedPara.title || '数据表格';
      const simpleTable = `
<table border="1" style="border-collapse: collapse; width: 100%; margin: 10px 0; font-size: 14px;">
  <caption style="caption-side: top; text-align: center; font-weight: bold; margin-bottom: 10px; color: #333;">${tableTitle}</caption>
  <thead>
    <tr>
      <th style="border: 1px solid #ddd; padding: 12px; text-align: center; background-color: #f5f5f5; font-weight: bold;">项目</th>
      <th style="border: 1px solid #ddd; padding: 12px; text-align: center; background-color: #f5f5f5; font-weight: bold;">数值</th>
      <th style="border: 1px solid #ddd; padding: 12px; text-align: center; background-color: #f5f5f5; font-weight: bold;">说明</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td style="border: 1px solid #ddd; padding: 10px; text-align: center;">示例项目1</td>
      <td style="border: 1px solid #ddd; padding: 10px; text-align: center;">100</td>
      <td style="border: 1px solid #ddd; padding: 10px; text-align: left;">这是第一个示例项目</td>
    </tr>
    <tr>
      <td style="border: 1px solid #ddd; padding: 10px; text-align: center;">示例项目2</td>
      <td style="border: 1px solid #ddd; padding: 10px; text-align: center;">200</td>
      <td style="border: 1px solid #ddd; padding: 10px; text-align: left;">这是第二个示例项目</td>
    </tr>
    <tr>
      <td style="border: 1px solid #ddd; padding: 10px; text-align: center;">示例项目3</td>
      <td style="border: 1px solid #ddd; padding: 10px; text-align: center;">300</td>
      <td style="border: 1px solid #ddd; padding: 10px; text-align: left;">这是第三个示例项目</td>
    </tr>
  </tbody>
</table>`;
      
      // 直接插入到段落中
      this.insertTableToParagraph(simpleTable);
      this.$message.success('基础表格已插入到段落中');
    },

    insertTableRow() {
      if (!this.selectedPara || !this.selectedPara.id) {
        this.$message.warning('请先选择一个段落');
        return;
      }
      
      const tableRow = `
    <tr>
      <td style="border: 1px solid #ddd; padding: 10px; text-align: center;">新数据</td>
      <td style="border: 1px solid #ddd; padding: 10px; text-align: center;">新数值</td>
      <td style="border: 1px solid #ddd; padding: 10px; text-align: left;">新说明</td>
    </tr>`;
      
      // 检查段落内容是否包含表格
      if (this.selectedPara.text && this.selectedPara.text.includes('<table')) {
        // 如果包含表格，在最后一个</tbody>前插入新行
        const newText = this.selectedPara.text.replace('</tbody>', tableRow + '\n    </tbody>');
        const updateData = {
          thesisId: this.selecedThesisId,
          paragraphId: this.selectedPara.id,
          title: this.selectedPara.title,
          text: newText
        };
        this.saveParagraphContent(updateData);
        this.$message.success('表格行已插入到现有表格中');
      } else {
        this.$message.warning('当前段落没有表格，请先插入表格');
      }
    },

    insertTableColumn() {
      if (!this.selectedPara || !this.selectedPara.id) {
        this.$message.warning('请先选择一个段落');
        return;
      }
      
      const tableColumn = `
      <th style="border: 1px solid #ddd; padding: 12px; text-align: center; background-color: #f5f5f5; font-weight: bold;">新列</th>`;
      
      // 检查段落内容是否包含表格
      if (this.selectedPara.text && this.selectedPara.text.includes('<table')) {
        // 如果包含表格，在表头行插入新列
        const newText = this.selectedPara.text.replace('</tr>', tableColumn + '\n      </tr>');
        const updateData = {
          thesisId: this.selecedThesisId,
          paragraphId: this.selectedPara.id,
          title: this.selectedPara.title,
          text: newText
        };
        this.saveParagraphContent(updateData);
        this.$message.success('表格列已插入到现有表格中');
      } else {
        this.$message.warning('当前段落没有表格，请先插入表格');
      }
    },

    refreshThesisList(thesisId = -1) {
      getThesisList()
        .then((res) => {
          this.thesisList = res.data;
          if (this.thesisList.length < 1) {
            // 清空当前选中的论文数据，显示空状态
            this.selecedThesisId = null;
            this.selecedThesisStatus = null;
            this.selecedThesis = {};
            this.title = '';
            this.paragraphs = [];
            return;
          }
          // 如果输入id =-1,默认选最新的一个
          if (thesisId < 0) {
            thesisId = res.data[0]["id"];
          }
          this.queryThesisStatusById(thesisId);
        })
        .catch((e) => console.log(e));
    },
    queryThesisStatusById(id) {
      clearInterval(this.ProgressTimerInterval);
      var loading = this.$loading({ text: "加载中" });
      getThesisDetail({ thesisId: id })
        .then((res) => {
          loading.close();
          this._setThesisField(res.data);
          this.$nextTick(() => {
            if (this.statusIsRunning()) {
              this.refreshOutlineInterval();
            } else {
              this.refreshOutlineOnce();
            }
          });
        })
        .catch((e) => {
          loading.close();
          this._msgError(e);
        });
    },

    refreshCurrentThesis() {
      this.refreshThesisList(this.selecedThesisId);
      // Start auto-refresh if thesis is being generated
      if (this.statusIsRunning()) {
        clearInterval(this.ContentRefreshInterval);
        this.ContentRefreshInterval = setInterval(() => {
          this.refreshThesisList(this.selecedThesisId);
        }, 5000); // Refresh every 5 seconds
      } else {
        clearInterval(this.ContentRefreshInterval);
      }
    },

    regenerateParagraph(param) {
      let loading = this.$loading({
        target: '.paragraph_one[data-paraid="' + param["paragraphId"] + '"]',
        text: "请稍等",
      });
      generateSingleParagraph(param)
        .then((res) => {
          loading.close();
          this.refreshCurrentThesis();
        })
        .catch((e) => {
          loading.close();
          this.$notify.error(e);
        });
    },
    handlerGenerateSinglePara(para) {
      this.paragraphRegenFormVisible = true;
      this.paragraphRegenForm["paragraphId"] = para["id"];
      this.paragraphRegenForm["thesisId"] = this.selecedThesisId;
      this.paragraphRegenForm["paragraphTitle"] = para["title"];
      
      // 根据全局设置初始化字数
      this.paragraphRegenForm["length"] = this.globalParagraphLength;
      this.paragraphRegenForm["lengthMode"] = this.lengthControlMode;
      this.paragraphRegenForm["minLength"] = this.minParagraphLength;
      this.paragraphRegenForm["maxLength"] = this.maxParagraphLength;
      this.paragraphRegenForm["instruction"] = this.globalInstruction;
      
      // 计算智能调整的预估字数
      this.calculateEstimatedLength(para);
    },
    
    onLengthModeChange(mode) {
      if (mode === 'auto') {
        this.calculateEstimatedLength();
      }
    },
    
    calculateEstimatedLength(para) {
      // 根据段落标题和上下文智能估算字数
      let estimated = 500; // 默认500字
      
      if (para && para.title) {
        const title = para.title.toLowerCase();
        if (title.includes('引言') || title.includes('结论')) {
          estimated = 800; // 引言和结论段落稍长
        } else if (title.includes('摘要')) {
          estimated = 300; // 摘要段落较短
        } else if (title.includes('方法') || title.includes('实验')) {
          estimated = 600; // 方法和实验段落中等长度
        } else if (title.includes('讨论') || title.includes('分析')) {
          estimated = 700; // 讨论和分析段落较长
        }
      }
      
      this.paragraphRegenForm.estimatedLength = estimated;
    },
    
    handlerGenerateSinglePara2() {
      let length = 0;
      
      // 根据不同的字数控制模式处理
      switch (this.paragraphRegenForm.lengthMode) {
        case 'fixed':
          length = parseInt(this.paragraphRegenForm["length"], 0);
          if (isNaN(length) || length < 100 || length > 3000) {
            this.$message({ type: "error", message: "单个段落的字数应该在100-3000字之间" });
            return;
          }
          break;
        case 'range':
          const minLength = parseInt(this.paragraphRegenForm["minLength"], 0);
          const maxLength = parseInt(this.paragraphRegenForm["maxLength"], 0);
          if (isNaN(minLength) || isNaN(maxLength) || minLength < 100 || maxLength > 3000 || minLength >= maxLength) {
            this.$message({ type: "error", message: "字数范围设置不正确，最小字数应小于最大字数，且都在100-3000字之间" });
            return;
          }
          length = Math.floor((minLength + maxLength) / 2); // 取中间值
          break;
        case 'auto':
          length = this.paragraphRegenForm.estimatedLength;
          break;
        default:
          this.$message({ type: "error", message: "请选择字数控制方式" });
          return;
      }
      
      // 构建生成参数
      const generateParams = {
        paragraphId: this.paragraphRegenForm["paragraphId"],
        thesisId: this.paragraphRegenForm["thesisId"],
        length: length,
        instruction: this.paragraphRegenForm["instruction"],
        contentStyle: this.paragraphRegenForm["contentStyle"],
        lengthMode: this.paragraphRegenForm["lengthMode"]
      };
      
      // 如果是范围模式，添加范围信息
      if (this.paragraphRegenForm.lengthMode === 'range') {
        generateParams.minLength = this.paragraphRegenForm["minLength"];
        generateParams.maxLength = this.paragraphRegenForm["maxLength"];
      }
      
      // 先关闭对话框
      this.paragraphRegenFormVisible = false;
      
      this.$confirm("当前内容将会被覆盖掉,确定要重新生成吗？", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then((res) => this.regenerateParagraph(generateParams))
        .catch((e) => {
          console.log(e);
          // 用户取消时，确保对话框已关闭
          this.paragraphRegenFormVisible = false;
        });
    },
    handlerStopGenerate() {
      this.$confirm("确定要终止当前生成任务吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          stopGenerateAll({ thesisId: this.selecedThesisId })
            .then((res) => {
              this._msgSuccess("操作成功");
              this.refreshCurrentThesis();
            })
            .catch((e) => this.$notify.error("操作失败:" + e));
        })
        .catch((e) => this._msgSuccess("已取消"));
    },
    handlerGenerateAll() {
      // 登录后才能使用

      if (!this.selecedThesisId) {
        this.$notify.info("请先拟定一套论文提纲");
        return;
      }
      
      // 检查字数设置
      if (this.lengthControlMode === 'range') {
        if (this.minParagraphLength >= this.maxParagraphLength) {
          this.$message({ type: "error", message: "字数范围设置不正确，最小字数应小于最大字数" });
          return;
        }
      }
      
      let loading = this.$loading({
        text: "请求中",
      });

      // 构建批量生成参数，包含字数控制设置
      const generateAllParams = {
        thesisId: this.selecedThesisId,
        globalSettings: {
          paragraphLength: this.globalParagraphLength,
          lengthControlMode: this.lengthControlMode,
          minParagraphLength: this.minParagraphLength,
          maxParagraphLength: this.maxParagraphLength,
          globalInstruction: this.globalInstruction
        }
      };

      generateAll(generateAllParams)
        .then((res) => {
          loading.close();
          this.leftVisiableToggle("outline");
          this._msgSuccess(this.$t("contentPage.notifyStartGeneratTask"));
          this.selecedThesisStatus = this.TaskStatus.RUNING;
          this.refreshOutlineInterval();
        })
        .catch((e) => {
          loading.close();
          this._msgError(e);
        });
    },

    refreshOutlineOnce() {
      getOutline({ thesisId: this.selecedThesisId })
        .then((res) => (this.outlineTree = res.data["outline"]))
        .catch((e) => console.log(e));
    },
    refreshOutlineInterval() {
      // 先清除可能存在的旧定时器，避免多个定时器同时运行
      if (this.ProgressTimerInterval) {
        clearInterval(this.ProgressTimerInterval);
        this.ProgressTimerInterval = null;
      }
      if (this.ContentRefreshInterval) {
        clearInterval(this.ContentRefreshInterval);
        this.ContentRefreshInterval = null;
      }
      
      let _a = () => {
        getOutline({ thesisId: this.selecedThesisId })
          .then((res) => {
            this.outlineTree = res.data["outline"];
            if (res.data["thesisStatus"] > 12) {
              // 清除所有定时器
              if (this.ProgressTimerInterval) {
                clearInterval(this.ProgressTimerInterval);
                this.ProgressTimerInterval = null;
              }
              if (this.ContentRefreshInterval) {
                clearInterval(this.ContentRefreshInterval);
                this.ContentRefreshInterval = null;
              }
              
              // 刷新一次当前论文数据以获取最新内容
              this.refreshCurrentThesis();
              
              this.$nextTick(() => {
                this.$confirm("大王,全部段落都已经过AI自动生成", "提示", {
                  confirmButtonText: "朕知道了",
                  type: "success",
                  closeOnClickModal: true,
                  closeOnPressEscape: true,
                  showClose: true
                });
              });
            }
          })
          .catch((e) => {
            console.log(e);
            // 出错时也清除所有定时器
            if (this.ProgressTimerInterval) {
              clearInterval(this.ProgressTimerInterval);
              this.ProgressTimerInterval = null;
            }
            if (this.ContentRefreshInterval) {
              clearInterval(this.ContentRefreshInterval);
              this.ContentRefreshInterval = null;
            }
            console.log("进度查询遇到错误，查询退出");
          });
      };
      
      // 立即执行一次
      _a();
      
      // 设置新的定时器
      console.log("安装定时器，更新进度");
      this.ProgressTimerInterval = setInterval(_a, 3000);
    },
    async handlerDownload() {
      try {
        console.log('开始处理论文下载请求，论文ID:', this.selecedThesisId);
        
        // 显示加载提示
        const loading = this.$loading({
          lock: true,
          text: '正在检查下载权限...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        
        // 先调用下载API检查支付状态
        console.log('调用下载API，检查支付状态');
        const response = await downloadThesis({ thesisId: this.selecedThesisId });

        // 关闭加载提示
        loading.close();
        console.log('下载API响应:', response);
        console.log('响应数据详情:', {
          success: response.success,
          data: response.data,
          need_payment: response.data?.need_payment,
          price: response.data?.price,
          file: response.data?.file
        });

        // 检查是否需要支付
        if (response.data && response.data.need_payment === true) {
          console.log('需要支付才能下载论文，价格:', response.data.price);

          // 显示收费原因提示
          let reasonMessage = '此论文需要付费下载';
          if (response.data.reason) {
            reasonMessage = response.data.reason;
          }

          // 显示收费原因通知
          this.$notify({
            title: '论文下载收费提示',
            message: `${reasonMessage}，需要支付 ${response.data.price} 元`,
            type: 'warning',
            duration: 5000,
            position: 'top-right'
          });

          // 设置支付数据并显示支付对话框
          this.paymentData = {
            thesisId: this.selecedThesisId,
            price: response.data.price || 10.00
          };

          // 显示微信支付对话框
          this.wechatPayDialogVisible = true;
          console.log('已设置wechatPayDialogVisible为true');
          return;
        }
        
        // 如果不需要支付，直接下载
        if (response.data && response.data.file) {
          console.log('论文可以下载，文件名:', response.data.file);
          
          // 如果有免费原因，显示相应提示
          if (response.data.free_reason) {
            console.log('免费下载原因:', response.data.free_reason, '消息:', response.data.message);
            
            // 根据不同的免费原因显示不同的提示
            let title = "下载成功";
            let message = response.data.message || "论文下载成功";
            let type = "success";
            
            if (response.data.free_reason === "first_free") {
              title = "首次下载免费";
              message = "您是首次下载论文，系统已为您免费开通下载权限！";
              type = "success";
              
              // 显示更醒目的首次免费提示
              this.$notify({
                title: "🎉 首次下载免费特权",
                message: "恭喜您获得首次下载免费特权！后续下载将需要支付费用。",
                type: "success",
                duration: 8000,
                position: "top-right"
              });
            } else if (response.data.free_reason === "vip_free") {
              title = "VIP特权";
              message = "您是VIP用户，可以免费下载所有论文！";
              type = "success";
            } else if (response.data.free_reason === "already_paid") {
              title = "下载成功";
              message = "您已支付过此论文，可以免费下载";
              type = "success";
            }
            
            this.$message({
              message: message,
              type: type
            });
          }
          
          // 创建下载链接并点击
          console.log('开始下载文件');
          const link = document.createElement('a');
          link.href = `/api/thesis/download?fileName=${response.data.file}`;
          link.target = '_blank';
          document.body.appendChild(link);
          link.click();
          console.log('文件下载链接已点击');
          document.body.removeChild(link);
        } else {
          this.$message.error('下载失败，请稍后重试');
        }
      } catch (error) {
        console.error('下载论文失败:', error);
        this.$message.error('下载失败，请稍后重试');
      }
    },
    
    // 处理微信支付成功
    handleWeChatPaymentSuccess(data) {
      console.log('微信支付成功:', data);
      this.$message.success('支付成功，正在准备下载...');

      // 关闭支付弹窗
      this.wechatPayDialogVisible = false;

      // 刷新下载权限信息
      if (this.$refs.downloadRights) {
        this.$refs.downloadRights.refresh();
      }

      // 重新触发下载
      setTimeout(() => {
        this.handlerDownload();
      }, 1000);
    },
    
    // 处理微信支付弹窗关闭
    handleWeChatPaymentClose() {
      console.log('微信支付弹窗关闭');
      this.wechatPayDialogVisible = false;
    },

    handlerDelete() {
      this.$confirm("确定要删除《" + this.title + "》论文吗？", "提示", {
        confirmButtonText: "继续删除",
        cancelButtonText: "不删除了",
        type: "warning",
      })
        .then(() => {
          this.$confirm("删除是物理删除，删除后无法找回，请切实确认好?", "提示", {
            confirmButtonText: "执行删除命令",
            cancelButtonText: "不删除了",
            type: "warning",
          })
            .then(() => {
              deleteThesis({ thesisId: this.selecedThesisId })
                .then((res) => {
                  this.$confirm("删除成功", "成功", {
                    confirmButtonText: "刷新页面",
                    showCancelButton: false,
                    showClose: false, //是否显示关闭按钮
                    closeOnClickModal: false, //是否可以通过点击空白处关闭弹窗
                    type: "info",
                  }).then(() => {
                    this.$router.push({
                      path: "/paper/content",
                    });
                  });
                })
                .catch((e) => this.$notify.error("删除失败错误:" + e));
            })
            .catch((e) => this._msgSuccess("已取消删除"));
        })
        .catch((e) => this._msgSuccess("已取消删除"));
    },

    handlerGotoThesisId(id) {
      this.$router.push({
        path: "/paper/content",
        query: { thesisId: id },
      });
    },

    _msgSuccess(m) {
      this.$message({
        type: "success",
        message: m,
      });
    },
    _msgError(m) {
      this.$message({
        type: "error",
        message: m,
      });
    },
    _setThesisField(thesis) {
      this.selecedThesisId = thesis.id;
      this.selecedThesisStatus = thesis.status;
      this.selecedThesis = thesis;
      this.title = thesis.title;
      this.thanks = thesis.thanks;
      this.references = thesis.references;
      this.digest["digest"] = thesis.digest;
      this.digest["digestEn"] = thesis.digestEn;
      this.digest["keywords"] = thesis.keywords;
      this.digest["keywordsEn"] = thesis.keywordsEn;
      
      // 设置段落数据，确保数据正确
      if (thesis.outline && thesis.outline.subtitle) {
        this.paragraphs = thesis.outline.subtitle;
        console.log('Content - 设置段落数据成功，段落数量:', this.paragraphs.length);
      } else if (thesis.outline) {
        // 如果没有subtitle，使用outline本身
        this.paragraphs = [thesis.outline];
        console.log('Content - 使用outline作为段落数据，段落数量:', this.paragraphs.length);
      } else {
        this.paragraphs = [];
        console.log('Content - 没有找到段落数据');
      }
      
      this._msgSuccess("加载完成");
    },

    saveParagraphLengthSettings() {
      // 验证设置
      if (this.lengthControlMode === 'range') {
        if (this.minParagraphLength >= this.maxParagraphLength) {
          this.$message({ type: "error", message: "字数范围设置不正确，最小字数应小于最大字数" });
          return;
        }
      }
      
      // 保存到本地存储
      const settings = {
        globalParagraphLength: this.globalParagraphLength,
        lengthControlMode: this.lengthControlMode,
        minParagraphLength: this.minParagraphLength,
        maxParagraphLength: this.maxParagraphLength,
        globalInstruction: this.globalInstruction
      };
      
      localStorage.setItem('paragraphLengthSettings', JSON.stringify(settings));
      
      this.paragraphLengthSettingsVisible = false;
      this.$message({ type: "success", message: "段落字数设置已保存" });
    },
    
    loadParagraphLengthSettings() {
      // 从本地存储加载设置
      const settings = localStorage.getItem('paragraphLengthSettings');
      if (settings) {
        try {
          const parsedSettings = JSON.parse(settings);
          this.globalParagraphLength = parsedSettings.globalParagraphLength || 1000;
          this.lengthControlMode = parsedSettings.lengthControlMode || 'fixed';
          this.minParagraphLength = parsedSettings.minParagraphLength || 100;
          this.maxParagraphLength = parsedSettings.maxParagraphLength || 3000;
          this.globalInstruction = parsedSettings.globalInstruction || '';
        } catch (error) {
          console.error('加载段落字数设置失败:', error);
        }
      }
    },
    // 下载文件方法
    downloadFile(fileName) {
      console.log('开始下载文件');
      
      // 显示下载成功通知
      this.$notify.success({
        title: "下载成功",
        message: "论文下载成功",
        duration: 3000
      });
      
      // 创建下载链接并点击
      let link = document.createElement("a");
      link.style.display = "none";
      link.href = "/api/thesis/download?fileName=" + fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      console.log('文件下载链接已点击');
    },
  },
};
</script>

<style lang="scss" scoped>
.flex {
  display: flex;
  flex-direction: row;
  height: 100%;
}

.home-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 0px;

  .result-box-wrapper {
    overflow-y: scroll;
  }
}

.result-box {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 60px;
  width: 900px;
  border-radius: 1px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  margin: 20px auto;
  margin-top: 0;
  background-color: #fff;
  overflow: hidden;

  .delete-box {
    margin-top: 100px;
    display: flex;
    justify-content: flex-end;
    .freeduty {
      padding: 3px;
      color: red;
      max-width: 600px;
      line-height: 24px;
      font-size: 12px;
    }
    .agreement {
      margin-right: 20px;
      margin-left: 20px;
      line-height: 24px;
      a {
        font-size: 12px;
        display: block;
        white-space: nowrap;
        color: blue;
      }
    }
  }
}

.result-title {
  text-align: center;
  font-size: 30px;
}

.empty-thesis-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  flex: 1;
  padding: 20px;
  
  .empty-thesis-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    background-color: #fff;
    border-radius: 8px;
    padding: 40px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    max-width: 500px;
    width: 100%;
    
    h2 {
      margin: 10px 0;
      color: #606266;
    }
    
    p {
      margin: 10px 0 20px;
      color: #909399;
      font-size: 14px;
      line-height: 1.6;
    }
  }
}

.left-siderbar {
  width: 300px;
  min-width: 250px;
  height: 100%;
  overflow-y: scroll;
  padding: 20px 10px;
  background: #f8f8f8;
}

.outline-siderbar {
  .outline-siderbar_headeer {
    font-size: 1.2rem;
    margin-bottom: 10px;

    i {
      margin-right: 0.5rem;
    }
  }
}

.thesis-list-siderbar {
  width: 240px;
  background-color: #fff;
  border-left: 1px solid #f1f1f1;
  overflow-y: scroll;

  .thesis-list-siderbar_header {
    font-weight: bold;
    padding: 15px;
    font-size: 18px;
    border-bottom: 1px solid #f1f1f1;
    background-color: #fafafa;

    i {
      color: #409eff;
      margin-right: 5px;
    }
  }

  .thesis-list-siderbar_title {
    padding: 15px;
    border-bottom: 1px solid #f1f1f1;
    cursor: pointer;

    &:hover {
      background-color: #f9f9f9;
    }

    &.s {
      background-color: #409eff;
      color: #fff;
    }
  }

  .empty {
    padding: 40px 20px;
    text-align: center;
    
    .empty-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 12px;
      
      i {
        font-size: 48px;
        color: #ddd;
        margin-bottom: 8px;
      }
      
      h3 {
        margin: 0;
        color: #666;
        font-size: 16px;
        font-weight: 500;
      }
      
      p {
        margin: 0;
        color: #999;
        font-size: 13px;
        line-height: 1.4;
        max-width: 180px;
      }
      
      .el-button {
        margin-top: 8px;
      }
    }
  }
}

.dialog-box_bg {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: #00000088;
  z-index: 10;
}

.dialog-box {
  position: fixed;
  top: 50px;
  left: 50%;
  width: 500px;
  margin-left: -250px;
  background: #fff;
  border-radius: 4px;
  z-index: 11;
  display: flex;
  flex-direction: column;
}

.dialog-box_header {
  display: flex;
  border-bottom: 1px solid #f1f1f1;
  font-size: 20px;
  font-weight: bold;

  i {
    font-size: 24px;
    font-weight: bold;
  }
}

.dialog-box_header_left {
  flex: 1;
  padding: 10px 20px;
}

.dialog-box_header_right {
  padding: 10px 20px;
}

.dialog-box_header_right {
  i {
    cursor: pointer;
  }
}
.dialog-box_body {
  padding: 20px 40px;
  flex: 1;
  font-size: 14px;
}

.dialog-box_footer {
  display: flex;
  justify-content: center;
  padding: 20px;
  border-top: 1px solid #f1f1f1;

  button {
    padding: 10px 40px;
    font-size: 16px;
    margin: 0 20px;
  }
}

.thesis-info-section {
  padding: 20px 24px;
  border-bottom: 1px solid #e4e7ed;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  border-radius: 8px 8px 0 0;
  position: relative;
}

.thesis-info-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid rgba(228, 231, 237, 0.6);

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 12px;
    padding-bottom: 8px;
  }
}

.thesis-basic-info {
  display: flex;
  flex-direction: column;
  gap: 12px;
  flex: 1;
}

.info-row {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  align-items: center;

  @media (max-width: 768px) {
    gap: 16px;
  }
}

.info-item {
  display: flex;
  align-items: center;
  min-width: 130px;
  background: rgba(255, 255, 255, 0.7);
  padding: 6px 12px;
  border-radius: 6px;
  border: 1px solid rgba(228, 231, 237, 0.5);
  transition: all 0.2s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.9);
    border-color: rgba(0, 123, 255, 0.3);
    transform: translateY(-1px);
  }

  @media (max-width: 768px) {
    min-width: 110px;
    padding: 4px 8px;
  }
}

.info-item .label {
  font-weight: 600;
  color: #6c757d;
  margin-right: 8px;
  min-width: 50px;
  font-size: 12px;
  letter-spacing: 0.5px;

  @media (max-width: 768px) {
    font-size: 11px;
    min-width: 40px;
  }
}

.info-item .value {
  color: #212529;
  font-size: 13px;
  font-weight: 500;

  @media (max-width: 768px) {
    font-size: 12px;
  }
}

.info-item.title-item {
  flex: 1;
  min-width: 300px;
  background: linear-gradient(135deg, rgba(0, 123, 255, 0.05) 0%, rgba(255, 255, 255, 0.8) 100%);
  border: 1px solid rgba(0, 123, 255, 0.2);
  padding: 8px 16px;

  @media (max-width: 768px) {
    min-width: 250px;
    padding: 6px 12px;
  }
}

.info-item .title-text {
  max-width: 400px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-weight: 600;
  color: #0056b3;
  font-size: 14px;

  @media (max-width: 768px) {
    max-width: 200px;
    font-size: 13px;
  }
}

.thesis-help-link {
  text-align: right;
  min-width: 180px;
  margin-left: 20px;
  
  @media (max-width: 768px) {
    text-align: left;
    margin-left: 0;
    min-width: auto;
  }
}

.thesis-help-link a {
  color: #0083b0;
  text-decoration: none;
  font-size: 12px;
  line-height: 1.4;
  display: block;
  
  @media (max-width: 768px) {
    font-size: 11px;
  }
  
  &:hover {
    text-decoration: underline;
    color: #0066cc;
  }
}

.thesis-actions-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 16px;
  margin-top: 20px;
  padding-top: 16px;
  border-top: 1px solid rgba(228, 231, 237, 0.6);

  @media (max-width: 1200px) {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
}

.thesis-actions {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;

  @media (max-width: 768px) {
    gap: 8px;
  }
}

.thesis-actions-secondary {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-left: auto;

  @media (max-width: 1200px) {
    margin-left: 0;
    width: 100%;
    justify-content: flex-start;
  }

  @media (max-width: 768px) {
    gap: 8px;
  }
}

.action-btn {
  margin: 0;
  padding: 6px 12px;
  font-size: 12px;
  height: 28px;
  line-height: 1;
  border-radius: 4px;
  transition: all 0.2s ease;
  white-space: nowrap;
  
  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  }
  
  @media (max-width: 768px) {
    padding: 4px 8px;
    font-size: 11px;
    height: 26px;
  }
}

/* 用户下载权限信息面板样式 */
.thesis-download-rights-wrapper {
  margin: 16px 0;
  padding: 12px 16px;
  background: linear-gradient(135deg, rgba(40, 167, 69, 0.05) 0%, rgba(255, 255, 255, 0.8) 100%);
  border: 1px solid rgba(40, 167, 69, 0.2);
  border-radius: 8px;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: linear-gradient(to bottom, #28a745, #20c997);
    border-radius: 4px 0 0 4px;
  }
}

/* 确保权限信息面板在论文信息区域内正确显示 */
.thesis-info-section .thesis-download-rights-wrapper {
  margin: 16px 0 0 0;
}

/* 改进权限信息组件内部样式 */
.thesis-download-rights-wrapper .user-download-rights {
  margin: 0;
}

.thesis-download-rights-wrapper .rights-text {
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 6px;

  &:last-child {
    margin-bottom: 0;
  }
}

.thesis-download-rights-wrapper .rights-text.current-thesis {
  color: #28a745;
  font-weight: 600;
}

.thesis-download-rights-wrapper .rights-text.free-downloads {
  color: #17a2b8;
  font-weight: 600;
}

.thesis-download-rights-wrapper .rights-text.payment-required {
  color: #fd7e14;
  font-weight: 600;
}

.thesis-download-rights-wrapper .rights-text.vip-status {
  color: #dc3545;
  font-weight: 500;
}

.thesis-download-rights-wrapper .rights-text.user-status {
  color: #6c757d;
  font-weight: 400;
}

/* 改进按钮样式以匹配整体设计 */
.thesis-actions .el-button {
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
}

.thesis-actions .el-button--primary {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  border: none;

  &:hover {
    background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
  }
}

.thesis-actions .el-button--success {
  background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
  border: none;

  &:hover {
    background: linear-gradient(135deg, #1e7e34 0%, #155724 100%);
  }
}

.thesis-actions .el-button--info {
  background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
  border: none;

  &:hover {
    background: linear-gradient(135deg, #495057 0%, #343a40 100%);
  }
}

/* 响应式设计优化 */
@media (max-width: 768px) {
  .thesis-info-section {
    padding: 16px 16px;
    border-radius: 6px 6px 0 0;
  }

  .thesis-actions-container {
    margin-top: 16px;
    padding-top: 12px;
  }

  .thesis-download-rights-wrapper {
    padding: 10px 12px;
    margin: 12px 0 0 0;
  }

  .info-item {
    min-width: 100px;
    padding: 4px 8px;
  }

  .info-item.title-item {
    min-width: 200px;
  }
}
</style>
