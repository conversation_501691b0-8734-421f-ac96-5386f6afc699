import axios from 'axios'
import { Notification } from 'element-ui'
import { getToken } from "@/utils/auth"
import i18n  from '@/i18n'
import store from '@/store'
import router from '@/router'
import Cookies from 'js-cookie'

const i18nErrMsg = i18n.messages[i18n.locale]['errorMsg']

// 不需要登录就能访问的API白名单
const API_WHITE_LIST = [
  '/api/user/login',
  '/api/user/register',
  '/api/home/<USER>',
  '/api/home/<USER>',
  '/api/help/',
  '/api/guide/',
  '/api/agreement/',
  '/api/admin/auth/login',
  '/api/admin/auth/logout',
  '/api/generate/tableContent',  // 表格生成API - 临时添加到白名单
  '/api/pay/',  // 支付相关接口
]

// 不应该触发自动登出的API列表（避免无限循环）
const NO_AUTO_LOGOUT_APIS = [
  '/api/admin/auth/logout',
  '/api/user/logout'
]

// 创建axios实例
const service = axios.create({
  baseURL: process.env.VUE_APP_BASE_API, // url = base url + request url
  withCredentials: true, // 跨域请求时发送cookies
  timeout: 120000, // 请求超时时间增加到120秒
  headers: {
    'Cache-Control': 'no-cache',
    'Pragma': 'no-cache'
  }
})

// 存储请求取消令牌
const pendingRequests = new Map()

// 生成请求的唯一键
const getRequestKey = (config) => {
  const { url, method, params, data } = config
  return [url, method, JSON.stringify(params), JSON.stringify(data)].join('&')
}

// 添加请求取消功能
const addPendingRequest = (config) => {
  const requestKey = getRequestKey(config)
  config.cancelToken = config.cancelToken || new axios.CancelToken(cancel => {
    if (!pendingRequests.has(requestKey)) {
      pendingRequests.set(requestKey, cancel)
    }
  })
}

// 移除请求
const removePendingRequest = (config) => {
  const requestKey = getRequestKey(config)
  if (pendingRequests.has(requestKey)) {
    const cancel = pendingRequests.get(requestKey)
    cancel('请求被取消') // 执行取消操作
    pendingRequests.delete(requestKey) // 从Map中移除
  }
}

// 记录请求日志
function logRequest(config, isError = false, errorData = null) {
  const method = config.method ? config.method.toUpperCase() : 'UNKNOWN'
  const url = config.url || 'UNKNOWN URL'
  const isAdminApi = url.includes('/api/admin/')
  const prefix = isAdminApi ? '[管理员API]' : '[用户API]'
  
  if (isError) {
    console.error(`${prefix} ${method} ${url} 请求失败:`, errorData)
  } else {
    console.log(`${prefix} ${method} ${url} 请求发送`)
  }
}

// 记录响应日志
function logResponse(response, isError = false, errorData = null) {
  const config = response.config || {}
  const method = config.method ? config.method.toUpperCase() : 'UNKNOWN'
  const url = config.url || 'UNKNOWN URL'
  const status = response.status || 'UNKNOWN STATUS'
  const isAdminApi = url.includes('/api/admin/')
  const prefix = isAdminApi ? '[管理员API]' : '[用户API]'
  
  if (isError) {
    console.error(`${prefix} ${method} ${url} 响应失败 (${status}):`, errorData)
  } else {
    console.log(`${prefix} ${method} ${url} 响应成功 (${status})`)
  }
}

// 获取管理员token
function getAdminToken() {
  // 优先从store获取，其次从cookie获取，最后从localStorage获取
  return store.state.admin && store.state.admin.adminToken || 
         Cookies.get('admin_token') || 
         localStorage.getItem('admin_token')
}

// 保存管理员token
function saveAdminToken(token) {
  if (token) {
    // 确保cookie和localStorage都有最新的token
    Cookies.set('admin_token', token, { expires: 7 })
    localStorage.setItem('admin_token', token)
    
    // 如果store中有adminToken模块，也更新它
    if (store.state.admin) {
      store.commit('admin/SET_TOKEN', token)
    }
  }
}

// 检查是否是白名单API
function isWhiteListApi(url) {
  return API_WHITE_LIST.some(route => url.includes(route))
}

// 检查是否是不应该触发自动登出的API
function isNoAutoLogoutApi(url) {
  return NO_AUTO_LOGOUT_APIS.some(route => url.includes(route))
}

// 添加请求重试功能
const retryRequest = async (config, error) => {
  // 检查是否配置了重试
  if (!config || !config.retry) {
    return Promise.reject(error)
  }
  
  // 设置重试计数器
  config.__retryCount = config.__retryCount || 0
  
  // 检查是否已达到最大重试次数
  if (config.__retryCount >= config.retry) {
    return Promise.reject(error)
  }
  
  // 增加重试计数
  config.__retryCount += 1
  
  // 如果是管理员API，尝试刷新token
  if (config.url && config.url.includes('/api/admin')) {
    console.log('尝试刷新管理员token...')
    // 从localStorage和cookie重新获取token
    const adminToken = getAdminToken()
    if (adminToken) {
      config.headers['Authorization'] = `Bearer ${adminToken}`
      console.log('已刷新管理员token')
    }
  }
  
  // 创建新的Promise用于延迟
  const delayRetry = new Promise(resolve => {
    setTimeout(() => {
      console.log(`重试请求 (${config.__retryCount}/${config.retry}): ${config.url}`)
      resolve()
    }, config.retryDelay || 1000)
  })
  
  // 延迟后重新发送请求
  return delayRetry.then(() => service(config))
}

// request拦截器
service.interceptors.request.use(
  config => {
    // 临时禁用请求取消机制，避免支付配置请求被意外取消
    if (!config.url.includes('/api/admin/wechat_pay_config/')) {
      // 如果存在相同的请求，先取消之前的请求
      removePendingRequest(config)
      // 添加新的请求取消令牌
      addPendingRequest(config)
    }
    
    // 根据路径判断是否是管理员API
    const isAdminApi = config.url.includes('/api/admin/')
    
    // 检查是否是白名单API
    const isWhiteList = isWhiteListApi(config.url)
    
    // 添加token到请求头
    if (isAdminApi) {
      // 管理员API使用admin_token
      const adminToken = getAdminToken()
      
      if (adminToken) {
        config.headers['Authorization'] = `Bearer ${adminToken}`
        saveAdminToken(adminToken)
      } else if (!isWhiteList) {
        // 只在非白名单API时记录警告
          console.warn('未找到管理员Token，请求可能会失败:', config.url)
      }
    } else {
      // 普通API使用user_token
      const token = getToken()
      if (token) {
        config.headers['Authorization'] = `Bearer ${token}`
      }
    }
    
    // 记录请求日志
    logRequest(config)
    
    return config
  },
  error => {
    console.error('请求拦截器错误:', error)
    return Promise.reject(error)
  }
)

// response 拦截器
service.interceptors.response.use(
  response => {
    // 请求完成后，移除请求取消令牌
    removePendingRequest(response.config)
    
    // 记录响应日志
    logResponse(response)
    
    //只返回response对象的data里面的内容一般是指的服务器返回的出去开销以外的payload的内容
    //服务器端一般会返回一个json格式的payload
    let resData = response.data
    
    // 检查响应是否是HTML而不是JSON
    if (typeof resData === 'string' && resData.trim().startsWith('<!doctype html>')) {
      console.warn('接收到HTML响应而不是JSON:', response.config.url)
      
      // 检查是否是管理员API
      const isAdminApi = response.config.url && response.config.url.includes('/api/admin')
      
      if (isAdminApi) {
        // 可能是认证问题，尝试重新获取token
        console.error('管理员API认证失败，返回了HTML页面')
        
        // 检查是否配置了重试
        if (response.config.retry && (!response.config.__retryCount || response.config.__retryCount < response.config.retry)) {
          console.log('尝试重试请求...')
          return retryRequest(response.config, new Error('接收到HTML响应而不是JSON'))
        }
        
        // 如果重试次数已用完或未配置重试，则清除token并重定向
        store.dispatch('admin/adminLogout')
        
        // 如果当前不在登录页，重定向到登录页
        if (router.currentRoute.path !== '/admin/login') {
          router.push('/admin/login')
        }
        
        return Promise.reject(new Error('登录已过期，请重新登录'))
      }
      
      return Promise.reject(new Error('服务器返回了无效的响应格式'))
    }
    
    // 检查响应是否包含错误信息
    // 优先检查success字段，如果存在则以success为准
    if (resData && resData.success !== undefined) {
      if (resData.success === true) {
        // 成功响应，直接返回
        return resData
      } else {
        // 失败响应，进行错误处理
        const errMsg = resData.message || i18nErrMsg['unknown']
        console.error('API返回错误:', errMsg, resData)

        // 检查是否是认证错误
        if (resData.code === 401 || resData.code === 403 || errMsg.includes('登录') || errMsg.includes('认证') || errMsg.includes('token')) {
          console.log('检测到认证错误')

          // 检查是否是不应该触发自动登出的API（避免无限循环）
          const shouldSkipAutoLogout = isNoAutoLogoutApi(response.config.url)

          if (shouldSkipAutoLogout) {
            console.log('跳过自动登出，避免无限循环:', response.config.url)
            return Promise.reject(new Error(errMsg))
          }

          console.log('执行登出操作')

          // 检查是否是管理员API
          const isAdminApi = response.config.url && response.config.url.includes('/api/admin')

          if (isAdminApi) {
            store.dispatch('admin/adminLogout')

            // 如果当前不在登录页，重定向到登录页
            if (router.currentRoute.path !== '/admin/login') {
              router.push('/admin/login')
            }
          } else {
            store.dispatch('user/logout')

            // 如果当前不在登录页，重定向到登录页
            if (router.currentRoute.path !== '/login') {
              router.push('/login')
            }
          }
        }

        return Promise.reject(new Error(errMsg))
      }
    }

    // 兼容旧的code字段检查
    if (resData && resData.code !== undefined && resData.code !== 0 && resData.code !== 200) {
      // 特殊处理支付相关错误，使其能够被正确处理
      if (resData.message === '需要支付' && resData.data && resData.data.need_payment) {
        console.log('检测到支付需求，保留完整响应数据')
        return resData
      }

      // 处理其他错误
      const errMsg = resData.message || i18nErrMsg['unknown']
      console.error('API返回错误:', errMsg, resData)

      // 检查是否是认证错误（兼容旧的code字段）
      if (resData.code === 401 || resData.code === 403 || errMsg.includes('登录') || errMsg.includes('认证') || errMsg.includes('token')) {
        console.log('检测到认证错误（兼容模式）')

        // 检查是否是不应该触发自动登出的API（避免无限循环）
        const shouldSkipAutoLogout = isNoAutoLogoutApi(response.config.url)

        if (shouldSkipAutoLogout) {
          console.log('跳过自动登出，避免无限循环:', response.config.url)
          return Promise.reject(new Error(errMsg))
        }

        console.log('执行登出操作')

        // 检查是否是管理员API
        const isAdminApi = response.config.url && response.config.url.includes('/api/admin')

        if (isAdminApi) {
          store.dispatch('admin/adminLogout')

          // 如果当前不在登录页，重定向到登录页
          if (router.currentRoute.path !== '/admin/login') {
            router.push('/admin/login')
          }
        } else {
          store.dispatch('user/logout')

          // 如果当前不在登录页，重定向到登录页
          if (router.currentRoute.path !== '/login') {
            router.push('/login')
          }
        }
      }
      
      return Promise.reject(new Error(errMsg))
    }
    
    return resData
  },
  error => {
    // 请求失败，移除请求取消令牌
    if (error.config) {
      removePendingRequest(error.config)
      
      // 记录响应日志
      logResponse(error.response || { config: error.config }, true, error)
    }
    
    // 检查是否是取消的请求
    if (axios.isCancel(error)) {
      console.log('请求已取消:', error.message)
      return Promise.reject(new Error('请求已取消'))
    }
    
    // 检查是否有响应
    if (error.response) {
      // 服务器返回了错误状态码
      const { status, data } = error.response
      
      // 特殊处理支付相关错误
      if (data && data.message === '需要支付' && data.data && data.data.need_payment) {
        console.log('检测到支付需求，保留完整响应数据')
        return data
      }
      
      // 处理不同的错误状态码
      switch (status) {
        case 401:
        case 403:
          // 认证错误
          console.log('检测到认证错误')

          // 检查是否是不应该触发自动登出的API（避免无限循环）
          const shouldSkipAutoLogout = error.config && isNoAutoLogoutApi(error.config.url)

          if (shouldSkipAutoLogout) {
            console.log('跳过自动登出，避免无限循环:', error.config.url)
            return Promise.reject(new Error(i18nErrMsg['auth']))
          }

          console.log('执行登出操作')

          // 检查是否是管理员API
          const isAdminApi = error.config && error.config.url && error.config.url.includes('/api/admin')

          if (isAdminApi) {
            store.dispatch('admin/adminLogout')

            // 如果当前不在登录页，重定向到登录页
            if (router.currentRoute.path !== '/admin/login') {
              router.push('/admin/login')
            }
          } else {
            store.dispatch('user/logout')

            // 如果当前不在登录页，重定向到登录页
            if (router.currentRoute.path !== '/login') {
              router.push('/login')
            }
          }

          return Promise.reject(new Error(i18nErrMsg['auth']))
        case 404:
          return Promise.reject(new Error(i18nErrMsg['notFound']))
        case 500:
          return Promise.reject(new Error(i18nErrMsg['serverError']))
        default:
          return Promise.reject(new Error(data && data.message ? data.message : i18nErrMsg['unknown']))
      }
    } else if (error.request) {
      // 请求已发送但没有收到响应
      console.error('请求超时或网络错误:', error.request)
      return Promise.reject(new Error(i18nErrMsg['network']))
    } else {
      // 请求设置时发生错误
      console.error('请求设置错误:', error.message)
      return Promise.reject(error)
    }
  }
)

// 导出取消请求方法
export const cancelRequest = (url) => {
  for (const [key, cancel] of pendingRequests.entries()) {
    if (key.includes(url)) {
      cancel('手动取消请求')
      pendingRequests.delete(key)
    }
  }
}

export default service
