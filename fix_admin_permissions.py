#!/usr/bin/env python3
"""
修复管理员权限问题
确保管理员有正确的权限配置
"""

import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置环境变量
os.environ["FLASK_ENV"] = "production"

def check_admin_permissions():
    """检查管理员权限"""
    try:
        import pymysql
        from EarlyBird.config.config import AppConfig
        
        # 解析数据库连接信息
        db_uri = AppConfig.SQLALCHEMY_DATABASE_URI
        
        # 从URI中提取连接信息
        import re
        match = re.match(r'mysql\+pymysql://([^:]+):([^@]+)@([^:]+):(\d+)/([^?]+)', db_uri)
        if not match:
            print("❌ 无法解析数据库连接URI")
            return False
        
        username, password, host, port, database = match.groups()
        port = int(port)
        
        # 连接数据库
        connection = pymysql.connect(
            host=host,
            port=port,
            user=username,
            password=password,
            database=database,
            charset='utf8mb4'
        )
        
        cursor = connection.cursor()
        
        print("🔍 检查管理员权限...")
        
        # 查询所有管理员
        cursor.execute("""
            SELECT id, username, realname, is_superadmin, permissions, is_active
            FROM earlybird_paper_admin
            ORDER BY id
        """)
        
        admins = cursor.fetchall()
        
        print(f"📋 管理员列表 (总数: {len(admins)}):")
        for admin in admins:
            admin_id, username, realname, is_superadmin, permissions, is_active = admin
            status = "✅ 激活" if is_active else "❌ 禁用"
            super_status = "👑 超级管理员" if is_superadmin else "👤 普通管理员"
            
            print(f"  ID: {admin_id}, 用户名: {username}, 姓名: {realname}")
            print(f"    状态: {status}, 类型: {super_status}")
            
            # 解析权限
            if permissions:
                try:
                    import json
                    perms = json.loads(permissions) if isinstance(permissions, str) else permissions
                    print(f"    权限: {perms}")
                except:
                    print(f"    权限: {permissions} (解析失败)")
            else:
                print(f"    权限: 无")
            print()
        
        cursor.close()
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ 检查管理员权限失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def fix_admin_permissions():
    """修复管理员权限"""
    try:
        import pymysql
        from EarlyBird.config.config import AppConfig
        import json
        
        # 解析数据库连接信息
        db_uri = AppConfig.SQLALCHEMY_DATABASE_URI
        
        # 从URI中提取连接信息
        import re
        match = re.match(r'mysql\+pymysql://([^:]+):([^@]+)@([^:]+):(\d+)/([^?]+)', db_uri)
        if not match:
            print("❌ 无法解析数据库连接URI")
            return False
        
        username, password, host, port, database = match.groups()
        port = int(port)
        
        # 连接数据库
        connection = pymysql.connect(
            host=host,
            port=port,
            user=username,
            password=password,
            database=database,
            charset='utf8mb4'
        )
        
        cursor = connection.cursor()
        
        print("🔧 修复管理员权限...")
        
        # 定义正确的权限配置
        correct_permissions = {
            'user': ['view', 'create', 'edit', 'delete', 'vip'],
            'thesis': ['view', 'delete', 'stats'],
            'system': ['config', 'logs', 'stats'],
            'admin': ['view', 'create', 'edit', 'delete']
        }
        
        # 查询所有管理员
        cursor.execute("""
            SELECT id, username, is_superadmin, permissions
            FROM earlybird_paper_admin
            WHERE is_active = 1
        """)
        
        admins = cursor.fetchall()
        
        for admin in admins:
            admin_id, username, is_superadmin, current_permissions = admin
            
            # 为超级管理员设置完整权限
            if is_superadmin:
                new_permissions = correct_permissions
            else:
                # 普通管理员去掉admin权限
                new_permissions = {
                    'user': ['view', 'create', 'edit', 'delete', 'vip'],
                    'thesis': ['view', 'delete', 'stats'],
                    'system': ['config', 'logs', 'stats']
                }
            
            # 更新权限
            permissions_json = json.dumps(new_permissions, ensure_ascii=False)
            
            cursor.execute("""
                UPDATE earlybird_paper_admin 
                SET permissions = %s
                WHERE id = %s
            """, (permissions_json, admin_id))
            
            print(f"✅ 更新管理员 {username}(ID:{admin_id}) 的权限")
            print(f"   新权限: {new_permissions}")
        
        connection.commit()
        cursor.close()
        connection.close()
        
        print(f"\n🎉 成功更新 {len(admins)} 个管理员的权限配置")
        return True
        
    except Exception as e:
        print(f"❌ 修复管理员权限失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def create_wechat_pay_config_table():
    """创建微信支付配置表"""
    try:
        import pymysql
        from EarlyBird.config.config import AppConfig
        
        # 解析数据库连接信息
        db_uri = AppConfig.SQLALCHEMY_DATABASE_URI
        
        # 从URI中提取连接信息
        import re
        match = re.match(r'mysql\+pymysql://([^:]+):([^@]+)@([^:]+):(\d+)/([^?]+)', db_uri)
        if not match:
            print("❌ 无法解析数据库连接URI")
            return False
        
        username, password, host, port, database = match.groups()
        port = int(port)
        
        # 连接数据库
        connection = pymysql.connect(
            host=host,
            port=port,
            user=username,
            password=password,
            database=database,
            charset='utf8mb4'
        )
        
        cursor = connection.cursor()
        
        print("🔧 检查微信支付配置表...")
        
        # 检查表是否存在
        cursor.execute("""
            SELECT COUNT(*) 
            FROM information_schema.tables 
            WHERE table_schema = %s AND table_name = 'wechat_pay_config'
        """, (database,))
        
        table_exists = cursor.fetchone()[0] > 0
        
        if not table_exists:
            print("📋 创建微信支付配置表...")
            
            # 创建表
            create_table_sql = """
            CREATE TABLE `wechat_pay_config` (
                `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '配置ID',
                `name` varchar(100) NOT NULL DEFAULT '微信支付配置' COMMENT '配置名称',
                `appid` varchar(64) NOT NULL COMMENT '微信支付应用ID',
                `mchid` varchar(32) NOT NULL COMMENT '微信支付商户号',
                `api_v3_key` varchar(128) NOT NULL COMMENT '商户APIv3密钥',
                `serial_no` varchar(64) NOT NULL COMMENT '商户证书序列号',
                `private_key_path` varchar(255) NOT NULL COMMENT '商户私钥文件路径',
                `platform_cert_path` varchar(255) DEFAULT NULL COMMENT '微信支付平台证书路径',
                `notify_url` varchar(255) NOT NULL COMMENT '回调通知地址',
                `is_sandbox` tinyint(1) DEFAULT '0' COMMENT '是否启用沙箱环境',
                `sandbox_appid` varchar(64) DEFAULT NULL COMMENT '沙箱环境应用ID',
                `sandbox_mchid` varchar(32) DEFAULT NULL COMMENT '沙箱环境商户号',
                `sandbox_api_v3_key` varchar(128) DEFAULT NULL COMMENT '沙箱环境APIv3密钥',
                `sandbox_serial_no` varchar(64) DEFAULT NULL COMMENT '沙箱环境证书序列号',
                `sandbox_private_key_path` varchar(255) DEFAULT NULL COMMENT '沙箱环境私钥文件路径',
                `is_enabled` tinyint(1) DEFAULT '1' COMMENT '是否启用微信支付',
                `is_test_mode` tinyint(1) DEFAULT '0' COMMENT '是否测试模式',
                `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                `remark` text COMMENT '备注信息',
                PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='微信支付配置表';
            """
            
            cursor.execute(create_table_sql)
            connection.commit()
            
            print("✅ 微信支付配置表创建成功")
            
            # 插入默认配置
            default_config_sql = """
            INSERT INTO `wechat_pay_config` (
                `name`, `appid`, `mchid`, `api_v3_key`, `serial_no`, 
                `private_key_path`, `notify_url`, `is_enabled`
            ) VALUES (
                '早鸟论文微信支付', 'wxfb9c26d95fcd8b21', '1636691881', 
                'yx159357QWERasdfZXCVtgbYHNujmIKO', '56D0EF3D9FFBAF91F2E8C477C732449D503A2290',
                '/static/cert/privateKey.txt', 'https://blog.zaoniao.vip/api/pay/notify', 1
            )
            """
            
            cursor.execute(default_config_sql)
            connection.commit()
            
            print("✅ 默认微信支付配置插入成功")
        else:
            print("✅ 微信支付配置表已存在")
            
            # 检查是否有name字段
            cursor.execute("""
                SELECT COUNT(*) 
                FROM information_schema.columns 
                WHERE table_schema = %s AND table_name = 'wechat_pay_config' AND column_name = 'name'
            """, (database,))
            
            name_field_exists = cursor.fetchone()[0] > 0
            
            if not name_field_exists:
                print("📋 添加name字段到微信支付配置表...")
                cursor.execute("""
                    ALTER TABLE `wechat_pay_config` 
                    ADD COLUMN `name` varchar(100) NOT NULL DEFAULT '微信支付配置' COMMENT '配置名称' 
                    AFTER `id`
                """)
                connection.commit()
                print("✅ name字段添加成功")
        
        cursor.close()
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ 创建微信支付配置表失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 管理员权限修复工具")
    print("=" * 80)
    
    # 1. 检查管理员权限
    check_ok = check_admin_permissions()
    
    # 2. 修复管理员权限
    fix_ok = fix_admin_permissions()
    
    # 3. 创建微信支付配置表
    table_ok = create_wechat_pay_config_table()
    
    print("\n" + "=" * 80)
    print("🎯 修复结果:")
    print(f"  权限检查: {'✅ 成功' if check_ok else '❌ 失败'}")
    print(f"  权限修复: {'✅ 成功' if fix_ok else '❌ 失败'}")
    print(f"  表结构: {'✅ 成功' if table_ok else '❌ 失败'}")
    
    if check_ok and fix_ok and table_ok:
        print("\n🎉 所有修复完成！")
        print("现在管理员应该能够:")
        print("  • 正常访问系统设置")
        print("  • 编辑系统配置")
        print("  • 保存微信支付配置")
        print("  • 查看支付订单列表")
    else:
        print("\n⚠️ 部分修复失败，请检查错误信息")
    
    print("\n🔄 请重启服务器以使修复生效")

if __name__ == "__main__":
    main()
