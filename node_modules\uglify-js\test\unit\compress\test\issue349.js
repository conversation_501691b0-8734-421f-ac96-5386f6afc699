(function () {
  var cake
  if (noFreeCakes) return /* I would
                              insert something
                              there, but I'm sort
                              of lazy so whatever.
  */ cake = new FreeCake()
  return cake
})()

(function () {
  var cake
  if (noFreeCakes) return /* I would insert something there, */ /*
                             but I'm sort of lazy so
  */ /*                      whatever. */ cake = new FreeCake()
  return cake
})()

(function () {
  var cake
  if (noFreeCakes) return // I would insert something there, but I'm sort of lazy so whatever.
  cake = new FreeCake()
  return cake
})()

