from werkzeug.security import generate_password_hash, check_password_hash

from EarlyBird.common.libs.BaseModel import BaseModel
from sqlalchemy import (
    BigInteger,
    Integer,
    String,
    Boolean,
    Column,
    Enum,
    SmallInteger,
    DateTime,
    Table,
    BIGINT,
    ForeignKey,
    MetaData,
    Text,
)
import json
import math
import logging
from datetime import datetime, timedelta
from . import TABLE_PREFIX


class Setting(BaseModel):
    """系统设置模型"""
    __tablename__ = f"{TABLE_PREFIX}setting"
    __table_args__ = {"comment": "系统设置表"}

    category = Column(String(50), nullable=False, default='system', comment="设置分类")
    key = Column(String(100), nullable=False, comment="设置键")
    value = Column(String(500), nullable=True, comment="设置值")
    description = Column(String(200), nullable=True, comment="设置描述")
    
    # 兼容旧版本的字段名
    settingKey = Column(String(100), comment="旧版本设置键", unique=True)
    settingValue = Column(String(300), comment="旧版本设置值")

    def __str__(self):
        return json.dumps(self.to_dict())
        
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'category': self.category,
            'key': self.key,
            'value': self.value,
            'description': self.description,
            'create_time': self.create_time.strftime('%Y-%m-%d %H:%M:%S') if self.create_time else None,
            'update_time': self.update_time.strftime('%Y-%m-%d %H:%M:%S') if self.update_time else None
        }
