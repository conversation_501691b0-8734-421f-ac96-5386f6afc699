{"name": "uglify-js", "description": "JavaScript parser and compressor/beautifier toolkit", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://mihai.bazon.net/blog"}, "version": "1.3.5", "main": "./uglify-js.js", "bin": {"uglifyjs": "./bin/uglifyjs"}, "repository": {"type": "git", "url": "**************:mishoo/UglifyJS.git"}, "devDependencies": {"nodeunit": "0.7.x"}, "scripts": {"test": "$(which nodeunit || echo node_modules/nodeunit/bin/nodeunit) test/unit/scripts.js && test/testparser.js && test/testconsolidator.js"}}