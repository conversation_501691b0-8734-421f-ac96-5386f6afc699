#!/usr/bin/env python3
"""
测试Setting修复效果
"""

import sys
import os
import logging

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置环境变量
os.environ["FLASK_ENV"] = "production"

def test_setting_model():
    """测试Setting模型"""
    try:
        from EarlyBird.web_server import create_custom_app
        from EarlyBird.model.setting import Setting
        
        app = create_custom_app()
        if not app:
            print("❌ 无法创建Flask应用")
            return False
        
        with app.app_context():
            print("🔍 测试Setting模型访问...")
            
            # 测试查询modelName
            try:
                setting = Setting.query.filter_by(settingKey="modelName").first()
                if setting:
                    print(f"✅ modelName设置: {setting.settingValue}")
                else:
                    print("⚠️ modelName设置不存在，创建默认设置...")
                    # 创建默认设置
                    new_setting = Setting()
                    new_setting.settingKey = "modelName"
                    new_setting.settingValue = "qianwen"
                    new_setting.save()
                    print("✅ 已创建默认modelName设置")
                    
                return True
                    
            except Exception as e:
                print(f"❌ 查询modelName失败: {str(e)}")
                return False
                
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_home_service():
    """测试HomeService"""
    try:
        from EarlyBird.web_server import create_custom_app
        from EarlyBird.api.home.service import HomeService
        
        app = create_custom_app()
        if not app:
            print("❌ 无法创建Flask应用")
            return False
        
        with app.app_context():
            print("🔍 测试HomeService...")
            
            service = HomeService()
            
            # 测试getModelName
            try:
                model_name = service.getModelName()
                print(f"✅ getModelName: {model_name}")
            except Exception as e:
                print(f"❌ getModelName失败: {str(e)}")
                return False
            
            # 测试getSetting
            try:
                result = service.getSetting()
                if result.isSucc():
                    settings = result.data
                    print(f"✅ getSetting成功，包含 {len(settings)} 个配置项:")
                    for key, value in settings.items():
                        if "apikey" in key.lower() and value:
                            display_value = value[:10] + "..." if len(value) > 10 else value
                        else:
                            display_value = value or "(空)"
                        print(f"  {key}: {display_value}")
                else:
                    print(f"❌ getSetting失败: {result.message}")
                    return False
            except Exception as e:
                print(f"❌ getSetting异常: {str(e)}")
                return False
                
            return True
                
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_ai_adapter():
    """测试AI适配器"""
    try:
        from EarlyBird.common import ai
        
        print("🔍 测试AI适配器...")
        
        # 测试默认模型
        try:
            adapter = ai.getAdapter("qianwen")
            print(f"✅ qianwen适配器: {type(adapter).__name__}")
        except Exception as e:
            print(f"❌ qianwen适配器失败: {str(e)}")
            return False
        
        # 测试"默认模型"的容错处理
        try:
            adapter = ai.getAdapter("默认模型")
            print(f"✅ '默认模型'容错处理: {type(adapter).__name__}")
        except Exception as e:
            print(f"❌ '默认模型'容错处理失败: {str(e)}")
            return False
        
        # 测试未知模型的容错处理
        try:
            adapter = ai.getAdapter("unknown_model")
            print(f"✅ 未知模型容错处理: {type(adapter).__name__}")
        except Exception as e:
            print(f"❌ 未知模型容错处理失败: {str(e)}")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 Setting修复效果测试")
    print("=" * 50)
    
    success_count = 0
    total_tests = 3
    
    # 测试Setting模型
    if test_setting_model():
        success_count += 1
    
    print("\n" + "-" * 30)
    
    # 测试HomeService
    if test_home_service():
        success_count += 1
    
    print("\n" + "-" * 30)
    
    # 测试AI适配器
    if test_ai_adapter():
        success_count += 1
    
    print("\n" + "=" * 50)
    print(f"🎯 测试结果: {success_count}/{total_tests} 通过")
    
    if success_count == total_tests:
        print("🎉 所有测试通过！修复成功！")
        print("\n📋 下一步操作:")
        print("1. 重启服务器")
        print("2. 访问管理员设置页面配置API Key")
        print("3. 测试新用户注册和AI功能")
    else:
        print("⚠️ 部分测试失败，请检查错误信息")

if __name__ == "__main__":
    main()
