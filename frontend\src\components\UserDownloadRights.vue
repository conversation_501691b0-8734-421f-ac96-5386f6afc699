<template>
  <div class="user-download-rights">
    <div class="rights-header">
      <h3>📋 下载权限信息</h3>
    </div>
    
    <div class="rights-content" v-if="rightsData">
      <!-- 当前论文状态 -->
      <div class="rights-item current-thesis" v-if="rightsData.current_thesis_paid">
        <div class="status-icon success">✅</div>
        <div class="status-text">
          <strong>当前论文已支付</strong>
          <p>您已支付过此论文，可以随时免费下载</p>
        </div>
      </div>
      
      <!-- 免费下载状态 -->
      <div class="rights-item free-downloads" v-else-if="rightsData.remaining_free_downloads !== 0">
        <div class="status-icon free">🆓</div>
        <div class="status-text">
          <strong v-if="rightsData.remaining_free_downloads === -1">
            {{ rightsData.is_vip ? 'VIP用户无限下载' : '免费下载' }}
          </strong>
          <strong v-else>
            剩余免费下载：{{ rightsData.remaining_free_downloads }} 次
          </strong>
          <p v-if="rightsData.is_vip && rightsData.vip_free">
            作为VIP用户，您可以无限次免费下载论文
          </p>
          <p v-else-if="rightsData.first_free && rightsData.user_download_count === 0">
            首次下载免费，之后需要付费
          </p>
          <p v-else-if="!rightsData.payment_enabled">
            论文下载功能当前免费开放
          </p>
        </div>
      </div>
      
      <!-- 需要付费状态 -->
      <div class="rights-item payment-required" v-else>
        <div class="status-icon payment">💰</div>
        <div class="status-text">
          <strong>需要付费下载</strong>
          <p>下载此论文需要支付 <span class="price">¥{{ rightsData.download_price.toFixed(2) }}</span></p>
          <p class="payment-note">支付后可永久下载此论文</p>
        </div>
      </div>
      
      <!-- 用户状态信息 -->
      <div class="rights-item user-status">
        <div class="status-icon info">ℹ️</div>
        <div class="status-text">
          <div class="user-info-row">
            <span class="label">用户类型：</span>
            <span class="value" :class="{ 'vip': rightsData.is_vip }">
              {{ rightsData.is_vip ? 'VIP用户' : '普通用户' }}
            </span>
          </div>
          <div class="user-info-row">
            <span class="label">已下载论文：</span>
            <span class="value">{{ rightsData.user_download_count }} 篇</span>
          </div>
        </div>
      </div>
      
      <!-- 升级提示 -->
      <div class="rights-item upgrade-tip" v-if="!rightsData.is_vip && rightsData.payment_enabled">
        <div class="status-icon tip">💡</div>
        <div class="status-text">
          <strong>升级VIP享受更多权益</strong>
          <p>VIP用户可无限次免费下载论文，还有更多专属功能</p>
          <a href="/vip" class="upgrade-link">了解VIP特权 →</a>
        </div>
      </div>
    </div>
    
    <!-- 加载状态 -->
    <div class="rights-loading" v-else-if="loading">
      <div class="loading-spinner">⏳</div>
      <p>正在获取下载权限信息...</p>
    </div>
    
    <!-- 错误状态 -->
    <div class="rights-error" v-else-if="error">
      <div class="error-icon">❌</div>
      <p>{{ error }}</p>
      <button @click="fetchRights" class="retry-btn">重试</button>
    </div>
  </div>
</template>

<script>
import { getUserDownloadRights } from '@/api/thesis'

export default {
  name: 'UserDownloadRights',
  props: {
    thesisId: {
      type: Number,
      required: true
    }
  },
  data() {
    return {
      rightsData: null,
      loading: false,
      error: null
    }
  },
  mounted() {
    this.fetchRights()
  },
  watch: {
    thesisId(newId, oldId) {
      if (newId !== oldId) {
        this.fetchRights()
      }
    }
  },
  methods: {
    async fetchRights() {
      if (!this.thesisId) return
      
      this.loading = true
      this.error = null
      
      try {
        const response = await getUserDownloadRights({ thesisId: this.thesisId })
        
        if (response && response.is_success) {
          this.rightsData = response.data
          console.log('获取下载权限信息成功:', this.rightsData)
        } else {
          this.error = response?.message || '获取下载权限信息失败'
        }
      } catch (error) {
        console.error('获取下载权限信息失败:', error)
        this.error = '网络错误，请稍后重试'
      } finally {
        this.loading = false
      }
    },
    
    // 刷新权限信息（供父组件调用）
    refresh() {
      this.fetchRights()
    }
  }
}
</script>

<style scoped>
.user-download-rights {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 16px;
  margin: 16px 0;
}

.rights-header h3 {
  margin: 0 0 16px 0;
  color: #495057;
  font-size: 16px;
  font-weight: 600;
}

.rights-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 12px;
  padding: 12px;
  background: white;
  border-radius: 6px;
  border-left: 4px solid #dee2e6;
}

.rights-item.current-thesis {
  border-left-color: #28a745;
  background: #f8fff9;
}

.rights-item.free-downloads {
  border-left-color: #17a2b8;
  background: #f0fdff;
}

.rights-item.payment-required {
  border-left-color: #ffc107;
  background: #fffef0;
}

.rights-item.user-status {
  border-left-color: #6c757d;
}

.rights-item.upgrade-tip {
  border-left-color: #007bff;
  background: #f0f8ff;
}

.status-icon {
  font-size: 20px;
  margin-right: 12px;
  flex-shrink: 0;
}

.status-text {
  flex: 1;
}

.status-text strong {
  display: block;
  margin-bottom: 4px;
  color: #212529;
  font-size: 14px;
}

.status-text p {
  margin: 0;
  color: #6c757d;
  font-size: 13px;
  line-height: 1.4;
}

.price {
  color: #dc3545;
  font-weight: bold;
  font-size: 14px;
}

.payment-note {
  color: #28a745 !important;
  font-size: 12px !important;
}

.user-info-row {
  display: flex;
  margin-bottom: 4px;
}

.user-info-row .label {
  color: #6c757d;
  font-size: 13px;
  min-width: 80px;
}

.user-info-row .value {
  color: #212529;
  font-size: 13px;
  font-weight: 500;
}

.user-info-row .value.vip {
  color: #dc3545;
  font-weight: bold;
}

.upgrade-link {
  color: #007bff;
  text-decoration: none;
  font-size: 13px;
  font-weight: 500;
}

.upgrade-link:hover {
  text-decoration: underline;
}

.rights-loading, .rights-error {
  text-align: center;
  padding: 20px;
  color: #6c757d;
}

.loading-spinner {
  font-size: 24px;
  margin-bottom: 8px;
}

.error-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.retry-btn {
  background: #007bff;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  margin-top: 8px;
}

.retry-btn:hover {
  background: #0056b3;
}
</style>
