#!/usr/bin/env python3
"""
调试Setting模型问题
检查数据库表结构和模型定义的一致性
"""

import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置环境变量
os.environ["FLASK_ENV"] = "production"

def check_setting_table_structure():
    """检查设置表结构"""
    try:
        import pymysql
        from EarlyBird.config.config import AppConfig
        
        # 解析数据库连接信息
        db_uri = AppConfig.SQLALCHEMY_DATABASE_URI
        
        # 从URI中提取连接信息
        import re
        match = re.match(r'mysql\+pymysql://([^:]+):([^@]+)@([^:]+):(\d+)/([^?]+)', db_uri)
        if not match:
            print("❌ 无法解析数据库连接URI")
            return False
        
        username, password, host, port, database = match.groups()
        port = int(port)
        
        # 连接数据库
        connection = pymysql.connect(
            host=host,
            port=port,
            user=username,
            password=password,
            database=database,
            charset='utf8mb4'
        )
        
        cursor = connection.cursor()
        
        print("🔍 检查设置表结构...")
        
        # 检查表是否存在
        cursor.execute("""
            SELECT COUNT(*) 
            FROM information_schema.tables 
            WHERE table_schema = %s AND table_name = 'earlybird_paper_setting'
        """, (database,))
        
        table_exists = cursor.fetchone()[0] > 0
        
        if not table_exists:
            print("❌ 表 earlybird_paper_setting 不存在")
            return False
        
        print("✅ 表 earlybird_paper_setting 存在")
        
        # 查询表结构
        cursor.execute("""
            SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT, COLUMN_COMMENT
            FROM information_schema.columns 
            WHERE table_schema = %s AND table_name = 'earlybird_paper_setting'
            ORDER BY ORDINAL_POSITION
        """, (database,))
        
        columns = cursor.fetchall()
        
        print(f"📋 表结构 (总字段数: {len(columns)}):")
        for column in columns:
            column_name, data_type, is_nullable, column_default, column_comment = column
            nullable = "可空" if is_nullable == "YES" else "不可空"
            default = f"默认: {column_default}" if column_default else "无默认值"
            comment = f"注释: {column_comment}" if column_comment else "无注释"
            print(f"  {column_name}: {data_type}, {nullable}, {default}, {comment}")
        
        # 查询表中的数据
        cursor.execute("SELECT COUNT(*) FROM earlybird_paper_setting")
        row_count = cursor.fetchone()[0]
        print(f"\n📊 表中数据行数: {row_count}")
        
        if row_count > 0:
            cursor.execute("SELECT * FROM earlybird_paper_setting LIMIT 5")
            sample_data = cursor.fetchall()
            print(f"📋 示例数据 (前5行):")
            for i, row in enumerate(sample_data, 1):
                print(f"  行{i}: {row}")
        
        cursor.close()
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ 检查设置表结构失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def analyze_model_issues():
    """分析模型问题"""
    print("\n🔍 分析Setting模型问题...")
    
    print("📋 当前模型定义的字段:")
    print("  • settingKey: String(100)")
    print("  • settingValue: String(500)")
    
    print("\n📋 to_dict()方法引用的字段:")
    print("  • category (❌ 不存在)")
    print("  • key (❌ 不存在)")
    print("  • value (❌ 不存在)")
    print("  • description (❌ 不存在)")
    
    print("\n📋 admin/settings.py中使用的查询:")
    print("  • Setting.query.filter_by(category='system').all() (❌ category字段不存在)")
    
    print("\n🎯 问题总结:")
    print("  1. ❌ 模型字段定义与使用不一致")
    print("  2. ❌ 缺少category字段")
    print("  3. ❌ to_dict()方法引用不存在的字段")
    print("  4. ❌ API查询使用不存在的字段")

def propose_fix_solution():
    """提出修复方案"""
    print("\n🔧 修复方案:")
    
    print("\n方案1: 修复模型定义 (推荐)")
    print("  1. 添加缺少的字段到Setting模型:")
    print("     • category: String(50) - 设置分类")
    print("     • key: String(100) - 设置键 (替换settingKey)")
    print("     • value: Text - 设置值 (替换settingValue)")
    print("     • description: String(255) - 设置描述")
    print("  2. 修复to_dict()方法")
    print("  3. 创建数据库迁移脚本")
    
    print("\n方案2: 修复API查询 (临时方案)")
    print("  1. 修改admin/settings.py中的查询")
    print("  2. 使用现有的settingKey和settingValue字段")
    print("  3. 修复to_dict()方法")
    
    print("\n🎯 推荐使用方案1，因为:")
    print("  • 提供更好的数据结构")
    print("  • 支持设置分类管理")
    print("  • 与现有API设计一致")

def check_admin_auth_issues():
    """检查管理员认证问题"""
    print("\n🔍 检查管理员认证问题...")
    
    try:
        import pymysql
        from EarlyBird.config.config import AppConfig
        
        # 解析数据库连接信息
        db_uri = AppConfig.SQLALCHEMY_DATABASE_URI
        
        # 从URI中提取连接信息
        import re
        match = re.match(r'mysql\+pymysql://([^:]+):([^@]+)@([^:]+):(\d+)/([^?]+)', db_uri)
        if not match:
            print("❌ 无法解析数据库连接URI")
            return False
        
        username, password, host, port, database = match.groups()
        port = int(port)
        
        # 连接数据库
        connection = pymysql.connect(
            host=host,
            port=port,
            user=username,
            password=password,
            database=database,
            charset='utf8mb4'
        )
        
        cursor = connection.cursor()
        
        # 检查管理员表
        cursor.execute("""
            SELECT id, username, is_active, permissions
            FROM earlybird_paper_admin
            WHERE is_active = 1
        """)
        
        admins = cursor.fetchall()
        
        print(f"📋 活跃管理员 (总数: {len(admins)}):")
        for admin in admins:
            admin_id, username, is_active, permissions = admin
            print(f"  ID: {admin_id}, 用户名: {username}, 状态: {'✅ 激活' if is_active else '❌ 禁用'}")
            
            # 检查权限
            if permissions:
                try:
                    import json
                    perms = json.loads(permissions) if isinstance(permissions, str) else permissions
                    has_thesis_perm = 'thesis' in perms and 'view' in perms.get('thesis', [])
                    print(f"    论文查看权限: {'✅ 有' if has_thesis_perm else '❌ 无'}")
                except:
                    print(f"    权限解析失败: {permissions}")
        
        cursor.close()
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ 检查管理员认证失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🔧 Setting模型调试工具")
    print("=" * 80)
    
    # 1. 检查设置表结构
    table_ok = check_setting_table_structure()
    
    # 2. 分析模型问题
    analyze_model_issues()
    
    # 3. 提出修复方案
    propose_fix_solution()
    
    # 4. 检查管理员认证问题
    auth_ok = check_admin_auth_issues()
    
    print("\n" + "=" * 80)
    print("🎯 调试结果:")
    print(f"  表结构检查: {'✅ 成功' if table_ok else '❌ 失败'}")
    print(f"  认证检查: {'✅ 成功' if auth_ok else '❌ 失败'}")
    
    print("\n📋 下一步行动:")
    print("  1. 修复Setting模型定义")
    print("  2. 创建数据库迁移")
    print("  3. 修复API查询逻辑")
    print("  4. 测试管理员面板功能")

if __name__ == "__main__":
    main()
