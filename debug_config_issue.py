#!/usr/bin/env python3
"""
调试配置问题
检查为什么首次下载免费配置读取不正确
"""

import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置环境变量
os.environ["FLASK_ENV"] = "production"

def debug_config_issue():
    """调试配置问题"""
    try:
        import pymysql
        from EarlyBird.config.config import AppConfig
        
        # 解析数据库连接信息
        db_uri = AppConfig.SQLALCHEMY_DATABASE_URI
        
        # 从URI中提取连接信息
        import re
        match = re.match(r'mysql\+pymysql://([^:]+):([^@]+)@([^:]+):(\d+)/([^?]+)', db_uri)
        if not match:
            print("❌ 无法解析数据库连接URI")
            return False
        
        username, password, host, port, database = match.groups()
        port = int(port)
        
        # 连接数据库
        connection = pymysql.connect(
            host=host,
            port=port,
            user=username,
            password=password,
            database=database,
            charset='utf8mb4'
        )
        
        cursor = connection.cursor()
        
        print("🔍 调试配置读取问题...")
        
        # 1. 检查支付配置表
        cursor.execute("""
            SELECT config_key, config_value, name, is_deleted
            FROM earlybird_payment_config
            WHERE config_key = 'thesis.download.first_free'
        """)
        
        configs = cursor.fetchall()
        
        print(f"📋 thesis.download.first_free 配置:")
        if configs:
            for key, value, name, is_deleted in configs:
                print(f"  config_key: {key}")
                print(f"  config_value: {value}")
                print(f"  name: {name}")
                print(f"  is_deleted: {is_deleted}")
                print(f"  条件匹配: config_key='thesis.download.first_free' AND is_deleted=False")
                print(f"  匹配结果: {key == 'thesis.download.first_free' and is_deleted == 0}")
                print(f"  值判断: config_value == 'true' -> {value == 'true'}")
        else:
            print("  ❌ 未找到配置")
        
        # 2. 检查用户14的下载记录
        print(f"\n📝 检查用户14的下载记录:")
        cursor.execute("""
            SELECT id, thesis_id, uid, is_paid, payment_method, create_time
            FROM earlybird_paper_thesis_download_record 
            WHERE uid = 14
        """)
        
        records = cursor.fetchall()
        
        if records:
            print(f"  找到 {len(records)} 条记录:")
            for record in records:
                record_id, thesis_id, uid, is_paid, payment_method, create_time = record
                print(f"    记录ID: {record_id}, 论文ID: {thesis_id}, 用户ID: {uid}")
                print(f"    是否已支付: {is_paid}, 支付方式: {payment_method}")
                print(f"    创建时间: {create_time}")
        else:
            print("  ✅ 无下载记录")
        
        # 3. 使用Flask应用上下文测试配置读取
        print(f"\n🧪 使用Flask应用上下文测试配置读取:")
        
        try:
            from EarlyBird.web_server import create_custom_app
            from EarlyBird.model.payment import PaymentConfig
            
            app = create_custom_app()
            if app:
                with app.app_context():
                    # 测试配置读取
                    first_free_config = PaymentConfig.query.filter_by(
                        config_key='thesis.download.first_free', 
                        is_deleted=False
                    ).first()
                    
                    print(f"  PaymentConfig查询结果: {first_free_config}")
                    if first_free_config:
                        print(f"  config_value: {first_free_config.config_value}")
                        print(f"  config_value == 'true': {first_free_config.config_value == 'true'}")
                        print(f"  first_free 最终值: {first_free_config and first_free_config.config_value == 'true'}")
                    else:
                        print("  ❌ PaymentConfig查询返回None")
            else:
                print("  ❌ 无法创建Flask应用")
        except Exception as e:
            print(f"  ❌ Flask应用测试失败: {str(e)}")
        
        cursor.close()
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ 调试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def fix_config_issue():
    """修复配置问题"""
    try:
        import pymysql
        from EarlyBird.config.config import AppConfig
        
        # 解析数据库连接信息
        db_uri = AppConfig.SQLALCHEMY_DATABASE_URI
        
        # 从URI中提取连接信息
        import re
        match = re.match(r'mysql\+pymysql://([^:]+):([^@]+)@([^:]+):(\d+)/([^?]+)', db_uri)
        if not match:
            print("❌ 无法解析数据库连接URI")
            return False
        
        username, password, host, port, database = match.groups()
        port = int(port)
        
        # 连接数据库
        connection = pymysql.connect(
            host=host,
            port=port,
            user=username,
            password=password,
            database=database,
            charset='utf8mb4'
        )
        
        cursor = connection.cursor()
        
        print("🔧 修复配置问题...")
        
        # 确保首次下载免费配置正确
        cursor.execute("""
            UPDATE earlybird_payment_config
            SET config_value = 'true', is_deleted = 0, update_time = NOW()
            WHERE config_key = 'thesis.download.first_free'
        """)
        
        if cursor.rowcount > 0:
            print("✅ 已更新首次下载免费配置")
        else:
            # 如果没有找到，创建一个
            cursor.execute("""
                INSERT INTO earlybird_payment_config
                (config_key, config_value, name, description, create_time, update_time, is_deleted)
                VALUES ('thesis.download.first_free', 'true', '首次下载是否免费', '用户首次下载论文是否免费', NOW(), NOW(), 0)
                ON DUPLICATE KEY UPDATE
                config_value = 'true', is_deleted = 0, update_time = NOW()
            """)
            print("✅ 已创建/更新首次下载免费配置")
        
        # 再次清空用户14的下载记录
        cursor.execute("DELETE FROM earlybird_paper_thesis_download_record WHERE uid = 14")
        deleted_count = cursor.rowcount
        print(f"✅ 已删除用户14的 {deleted_count} 条下载记录")
        
        # 删除相关的支付记录
        cursor.execute("DELETE FROM earlybird_paper_payment WHERE user_id = 14")
        deleted_payment_count = cursor.rowcount
        print(f"✅ 已删除用户14的 {deleted_payment_count} 条支付记录")
        
        # 提交更改
        connection.commit()
        
        # 验证修复结果
        cursor.execute("SELECT config_value FROM earlybird_payment_config WHERE config_key = 'thesis.download.first_free' AND is_deleted = 0")
        result = cursor.fetchone()
        
        if result and result[0] == 'true':
            print("✅ 验证成功: thesis.download.first_free = true")
        else:
            print("❌ 验证失败: 配置未正确更新")
        
        cursor.close()
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ 修复失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 配置问题调试和修复工具")
    print("=" * 50)
    
    # 调试配置问题
    debug_config_issue()
    
    print("\n" + "=" * 50)
    
    # 修复配置问题
    if fix_config_issue():
        print("\n🎉 配置问题修复完成！")
        print("\n📋 重要提醒:")
        print("1. 请重启服务器以确保配置生效")
        print("2. 重启后测试论文下载功能")
        print("3. 应该看到首次下载免费")
    else:
        print("\n❌ 配置问题修复失败")

if __name__ == "__main__":
    main()
