#!/usr/bin/env python3
"""
修复Setting表结构
添加缺少的字段并迁移数据
"""

import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置环境变量
os.environ["FLASK_ENV"] = "production"

def check_and_fix_setting_table():
    """检查并修复设置表"""
    try:
        import pymysql
        from EarlyBird.config.config import AppConfig
        
        # 解析数据库连接信息
        db_uri = AppConfig.SQLALCHEMY_DATABASE_URI
        
        # 从URI中提取连接信息
        import re
        match = re.match(r'mysql\+pymysql://([^:]+):([^@]+)@([^:]+):(\d+)/([^?]+)', db_uri)
        if not match:
            print("❌ 无法解析数据库连接URI")
            return False
        
        username, password, host, port, database = match.groups()
        port = int(port)
        
        # 连接数据库
        connection = pymysql.connect(
            host=host,
            port=port,
            user=username,
            password=password,
            database=database,
            charset='utf8mb4'
        )
        
        cursor = connection.cursor()
        
        print("🔍 检查设置表结构...")
        
        # 检查表是否存在
        cursor.execute("""
            SELECT COUNT(*) 
            FROM information_schema.tables 
            WHERE table_schema = %s AND table_name = 'earlybird_paper_setting'
        """, (database,))
        
        table_exists = cursor.fetchone()[0] > 0
        
        if not table_exists:
            print("📋 创建设置表...")
            create_table_sql = """
            CREATE TABLE `earlybird_paper_setting` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `category` varchar(50) DEFAULT 'system' COMMENT '设置分类',
                `key` varchar(100) NOT NULL COMMENT '设置键',
                `value` text COMMENT '设置值',
                `description` varchar(255) DEFAULT '' COMMENT '设置描述',
                `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                PRIMARY KEY (`id`),
                UNIQUE KEY `unique_key` (`key`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统设置表';
            """
            
            cursor.execute(create_table_sql)
            connection.commit()
            print("✅ 设置表创建成功")
            
        else:
            print("✅ 设置表已存在，检查字段...")
            
            # 检查现有字段
            cursor.execute("""
                SELECT COLUMN_NAME
                FROM information_schema.columns 
                WHERE table_schema = %s AND table_name = 'earlybird_paper_setting'
            """, (database,))
            
            existing_columns = [row[0] for row in cursor.fetchall()]
            print(f"📋 现有字段: {existing_columns}")
            
            # 需要的字段
            required_fields = {
                'category': "varchar(50) DEFAULT 'system' COMMENT '设置分类'",
                'key': "varchar(100) NOT NULL COMMENT '设置键'",
                'value': "text COMMENT '设置值'",
                'description': "varchar(255) DEFAULT '' COMMENT '设置描述'"
            }
            
            # 检查并添加缺少的字段
            for field_name, field_definition in required_fields.items():
                if field_name not in existing_columns:
                    print(f"📋 添加字段: {field_name}")
                    
                    try:
                        cursor.execute(f"""
                            ALTER TABLE earlybird_paper_setting 
                            ADD COLUMN `{field_name}` {field_definition}
                        """)
                        connection.commit()
                        print(f"✅ 字段 {field_name} 添加成功")
                    except Exception as e:
                        print(f"⚠️ 添加字段 {field_name} 失败: {str(e)}")
                else:
                    print(f"✅ 字段 {field_name} 已存在")
            
            # 处理数据迁移
            if 'settingKey' in existing_columns and 'key' in required_fields:
                try:
                    cursor.execute("""
                        UPDATE earlybird_paper_setting 
                        SET `key` = settingKey 
                        WHERE settingKey IS NOT NULL AND (`key` IS NULL OR `key` = '')
                    """)
                    connection.commit()
                    print("✅ settingKey -> key 数据迁移成功")
                except Exception as e:
                    print(f"⚠️ settingKey 迁移失败: {str(e)}")
            
            if 'settingValue' in existing_columns and 'value' in required_fields:
                try:
                    cursor.execute("""
                        UPDATE earlybird_paper_setting 
                        SET `value` = settingValue 
                        WHERE settingValue IS NOT NULL AND (`value` IS NULL OR `value` = '')
                    """)
                    connection.commit()
                    print("✅ settingValue -> value 数据迁移成功")
                except Exception as e:
                    print(f"⚠️ settingValue 迁移失败: {str(e)}")
            
            # 设置默认的category值
            try:
                cursor.execute("""
                    UPDATE earlybird_paper_setting 
                    SET category = 'system' 
                    WHERE category IS NULL OR category = ''
                """)
                connection.commit()
                print("✅ 设置默认分类值")
            except Exception as e:
                print(f"⚠️ 设置默认分类失败: {str(e)}")
        
        # 验证表结构
        cursor.execute("""
            SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
            FROM information_schema.columns 
            WHERE table_schema = %s AND table_name = 'earlybird_paper_setting'
            ORDER BY ORDINAL_POSITION
        """, (database,))
        
        final_columns = cursor.fetchall()
        
        print(f"\n📋 最终表结构:")
        for column in final_columns:
            column_name, data_type, is_nullable, column_default = column
            nullable = "可空" if is_nullable == "YES" else "不可空"
            default = f"默认: {column_default}" if column_default else "无默认值"
            print(f"  {column_name}: {data_type}, {nullable}, {default}")
        
        cursor.close()
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ 修复设置表失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 Setting表结构修复工具")
    print("=" * 80)
    
    # 检查并修复设置表
    table_ok = check_and_fix_setting_table()
    
    print("\n" + "=" * 80)
    print("🎯 修复结果:")
    print(f"  表结构修复: {'✅ 成功' if table_ok else '❌ 失败'}")
    
    if table_ok:
        print("\n🎉 Setting表修复完成！")
        print("现在应该能够:")
        print("  • 正常加载管理员设置页面")
        print("  • 使用category字段进行查询")
        print("  • 保存和读取系统设置")
    else:
        print("\n⚠️ 修复失败，请检查错误信息")
    
    print("\n🔄 请重启服务器以使修复生效")

if __name__ == "__main__":
    main()
