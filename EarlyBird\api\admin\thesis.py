from flask import Blueprint, request, jsonify
from EarlyBird.common.libs.api_result import ApiResult
from EarlyBird.model.admin import Admin, AdminLog
from EarlyBird.model.thesis import Thesis
from EarlyBird.model.user import User
from EarlyBird.common.libs.BaseModel import db
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)

# 创建蓝图
admin_thesis_bp = Blueprint('admin_thesis', __name__)

# 导入认证装饰器
from .auth import admin_auth_required


@admin_thesis_bp.route('/list', methods=['GET'])
@admin_auth_required
def thesis_list():
    """获取论文列表"""
    try:
        admin = request.admin
        
        # 检查权限
        if not admin.has_permission('thesis', 'view'):
            return ApiResult.error("没有查看论文的权限")
        
        # 获取查询参数
        page = int(request.args.get('page', 1))
        size = int(request.args.get('size', 20))
        keyword = request.args.get('keyword', '')
        lang = request.args.get('lang', '')
        level = request.args.get('level', '')
        
        # 构建查询
        query = Thesis.query
        
        # 关键词搜索
        if keyword:
            query = query.filter(
                (Thesis.title.contains(keyword)) |
                (Thesis.author.contains(keyword))
            )
        
        # 语言筛选
        if lang:
            query = query.filter(Thesis.lang == lang)
        
        # 级别筛选
        if level:
            query = query.filter(Thesis.level == level)
        
        # 分页
        total = query.count()
        thesis_list = query.order_by(Thesis.create_time.desc()).offset((page - 1) * size).limit(size).all()
        
        # 格式化数据
        result_list = []
        for thesis in thesis_list:
            # 获取用户信息
            user = User.query.get(thesis.uid)
            user_info = user.get_info() if user else None
            
            thesis_info = {
                "id": thesis.id,
                "uid": thesis.uid,
                "lang": thesis.lang,
                "level": thesis.level,
                "length": thesis.length,
                "title": thesis.title,
                "author": thesis.author,
                "digest": thesis.digest,
                "keywords": thesis.keywords,
                "create_time": thesis.create_time.strftime("%Y-%m-%d %H:%M:%S") if thesis.create_time else "",
                "user": user_info
            }
            result_list.append(thesis_info)
        
        # 记录操作日志
        AdminLog.create_log(
            admin_id=admin.id,
            admin_username=admin.username,
            action="view",
            resource="thesis",
            description=f"查看论文列表，页码:{page}，关键词:{keyword}",
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent', '')
        )
        
        return ApiResult.success({
            "list": result_list,
            "total": total,
            "page": page,
            "size": size
        })
        
    except Exception as e:
        logger.error(f"获取论文列表失败: {str(e)}")
        return ApiResult.error("获取论文列表失败")


@admin_thesis_bp.route('/detail/<int:thesis_id>', methods=['GET'])
@admin_auth_required
def thesis_detail(thesis_id):
    """获取论文详情"""
    try:
        admin = request.admin
        
        # 检查权限
        if not admin.has_permission('thesis', 'view'):
            return ApiResult.error("没有查看论文的权限")
        
        # 查找论文
        thesis = Thesis.query.get(thesis_id)
        if not thesis:
            return ApiResult.error("论文不存在")
        
        # 获取用户信息
        user = User.query.get(thesis.uid)
        user_info = user.get_info() if user else None
        
        # 格式化论文信息
        thesis_info = {
            "id": thesis.id,
            "uid": thesis.uid,
            "lang": thesis.lang,
            "level": thesis.level,
            "length": thesis.length,
            "title": thesis.title,
            "author": thesis.author,
            "outline": thesis.outline,
            "digest": thesis.digest,
            "digestEn": thesis.digestEn,
            "keywords": thesis.keywords,
            "keywordsEn": thesis.keywordsEn,
            "references": thesis.references,
            "thanks": thesis.thanks,
            "create_time": thesis.create_time.strftime("%Y-%m-%d %H:%M:%S") if thesis.create_time else "",
            "user": user_info
        }
        
        # 记录操作日志
        AdminLog.create_log(
            admin_id=admin.id,
            admin_username=admin.username,
            action="view",
            resource="thesis",
            resource_id=thesis_id,
            description=f"查看论文详情: {thesis.title}",
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent', '')
        )
        
        return ApiResult.success(thesis_info)
        
    except Exception as e:
        logger.error(f"获取论文详情失败: {str(e)}")
        return ApiResult.error("获取论文详情失败")


@admin_thesis_bp.route('/delete/<int:thesis_id>', methods=['DELETE'])
@admin_auth_required
def thesis_delete(thesis_id):
    """删除论文"""
    try:
        admin = request.admin
        
        # 检查权限
        if not admin.has_permission('thesis', 'delete'):
            return ApiResult.error("没有删除论文的权限")
        
        # 查找论文
        thesis = Thesis.query.get(thesis_id)
        if not thesis:
            return ApiResult.error("论文不存在")
        
        title = thesis.title
        
        # 删除论文
        thesis.delete()
        
        # 记录操作日志
        AdminLog.create_log(
            admin_id=admin.id,
            admin_username=admin.username,
            action="delete",
            resource="thesis",
            resource_id=thesis_id,
            description=f"删除论文: {title}",
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent', '')
        )
        
        return ApiResult.success(None, "删除成功")
        
    except Exception as e:
        logger.error(f"删除论文失败: {str(e)}")
        return ApiResult.error("删除论文失败")


@admin_thesis_bp.route('/stats', methods=['GET'])
@admin_auth_required
def thesis_stats():
    """论文统计"""
    try:
        admin = request.admin
        
        # 检查权限
        if not admin.has_permission('thesis', 'stats'):
            return ApiResult.error("没有查看论文统计的权限")
        
        # 获取统计参数
        period = request.args.get('period', '7d')  # 7d, 30d, 90d
        
        # 计算时间范围
        now = datetime.now()
        if period == '7d':
            start_date = now - timedelta(days=7)
        elif period == '30d':
            start_date = now - timedelta(days=30)
        elif period == '90d':
            start_date = now - timedelta(days=90)
        else:
            start_date = now - timedelta(days=7)
        
        # 统计数据
        total_thesis = Thesis.query.count()
        new_thesis = Thesis.query.filter(Thesis.create_time >= start_date).count()
        
        # 按语言统计
        lang_stats = db.session.query(
            Thesis.lang,
            db.func.count(Thesis.id).label('count')
        ).group_by(Thesis.lang).all()
        
        # 按级别统计
        level_stats = db.session.query(
            Thesis.level,
            db.func.count(Thesis.id).label('count')
        ).group_by(Thesis.level).all()
        
        # 按用户统计（前10名）
        user_stats = db.session.query(
            Thesis.uid,
            db.func.count(Thesis.id).label('count')
        ).group_by(Thesis.uid).order_by(db.func.count(Thesis.id).desc()).limit(10).all()
        
        # 获取用户信息
        user_stats_with_info = []
        for uid, count in user_stats:
            user = User.query.get(uid)
            user_info = user.get_info() if user else {"id": uid, "username": "未知用户"}
            user_stats_with_info.append({
                "user": user_info,
                "count": count
            })
        
        # 记录操作日志
        AdminLog.create_log(
            admin_id=admin.id,
            admin_username=admin.username,
            action="stats",
            resource="thesis",
            description=f"查看论文统计，周期:{period}",
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent', '')
        )
        
        return ApiResult.success({
            "total_thesis": total_thesis,
            "new_thesis": new_thesis,
            "lang_stats": [{"lang": lang, "count": count} for lang, count in lang_stats],
            "level_stats": [{"level": level, "count": count} for level, count in level_stats],
            "user_stats": user_stats_with_info,
            "period": period
        })
        
    except Exception as e:
        logger.error(f"获取论文统计失败: {str(e)}")
        return ApiResult.error("获取论文统计失败")


@admin_thesis_bp.route('/user/<int:user_id>', methods=['GET'])
@admin_auth_required
def user_thesis_list(user_id):
    """获取指定用户的论文列表"""
    try:
        admin = request.admin
        
        # 检查权限
        if not admin.has_permission('thesis', 'view'):
            return ApiResult.error("没有查看论文的权限")
        
        # 检查用户是否存在
        user = User.query.get(user_id)
        if not user:
            return ApiResult.error("用户不存在")
        
        # 获取查询参数
        page = int(request.args.get('page', 1))
        size = int(request.args.get('size', 20))
        
        # 查询用户的论文
        query = Thesis.query.filter(Thesis.uid == user_id)
        total = query.count()
        thesis_list = query.order_by(Thesis.create_time.desc()).offset((page - 1) * size).limit(size).all()
        
        # 格式化数据
        result_list = []
        for thesis in thesis_list:
            thesis_info = {
                "id": thesis.id,
                "lang": thesis.lang,
                "level": thesis.level,
                "length": thesis.length,
                "title": thesis.title,
                "author": thesis.author,
                "create_time": thesis.create_time.strftime("%Y-%m-%d %H:%M:%S") if thesis.create_time else ""
            }
            result_list.append(thesis_info)
        
        # 记录操作日志
        AdminLog.create_log(
            admin_id=admin.id,
            admin_username=admin.username,
            action="view",
            resource="thesis",
            description=f"查看用户论文列表: {user.username}",
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent', '')
        )
        
        return ApiResult.success({
            "list": result_list,
            "total": total,
            "page": page,
            "size": size,
            "user": user.get_info()
        })
        
    except Exception as e:
        logger.error(f"获取用户论文列表失败: {str(e)}")
        return ApiResult.error("获取用户论文列表失败") 