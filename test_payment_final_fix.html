<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>支付功能最终修复测试</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            max-width: 800px; 
            margin: 0 auto; 
            padding: 20px; 
            background-color: #f5f5f5; 
        }
        .container { 
            background: white; 
            padding: 30px; 
            border-radius: 8px; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1); 
        }
        h1 { 
            color: #333; 
            text-align: center; 
            margin-bottom: 30px; 
        }
        .section { 
            margin-bottom: 30px; 
            padding: 20px; 
            border: 1px solid #ddd; 
            border-radius: 5px; 
        }
        .section h3 { 
            color: #666; 
            margin-top: 0; 
        }
        button { 
            background-color: #409EFF; 
            color: white; 
            border: none; 
            padding: 10px 20px; 
            border-radius: 4px; 
            cursor: pointer; 
            margin-right: 10px; 
            margin-bottom: 10px; 
        }
        button:hover { 
            background-color: #66b1ff; 
        }
        button.success { 
            background-color: #67c23a; 
        }
        button.success:hover { 
            background-color: #85ce61; 
        }
        .input-group { 
            margin: 10px 0; 
        }
        .input-group label { 
            display: inline-block; 
            width: 100px; 
            font-weight: bold; 
        }
        .input-group input { 
            padding: 8px; 
            border: 1px solid #ddd; 
            border-radius: 4px; 
            width: 200px; 
        }
        .result { 
            background-color: #f8f8f8; 
            border: 1px solid #ddd; 
            padding: 15px; 
            border-radius: 4px; 
            font-family: monospace; 
            font-size: 12px; 
            white-space: pre-wrap; 
            max-height: 400px; 
            overflow-y: auto; 
            margin-top: 10px; 
        }
        .status { 
            padding: 10px; 
            border-radius: 4px; 
            margin: 10px 0; 
            font-weight: bold; 
        }
        .status.success { 
            background-color: #f0f9ff; 
            border: 1px solid #67c23a; 
            color: #67c23a; 
        }
        .status.error { 
            background-color: #fef0f0; 
            border: 1px solid #f56c6c; 
            color: #f56c6c; 
        }
        .status.warning { 
            background-color: #fdf6ec; 
            border: 1px solid #e6a23c; 
            color: #e6a23c; 
        }
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }
        .modal-content {
            background-color: white;
            margin: 15% auto;
            padding: 20px;
            border-radius: 8px;
            width: 400px;
            text-align: center;
        }
        .qr-code {
            width: 200px;
            height: 200px;
            margin: 20px auto;
            border: 1px solid #ddd;
        }
        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }
        .close:hover {
            color: black;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 支付功能最终修复测试</h1>
        
        <div class="section">
            <h3>📝 测试参数</h3>
            <div class="input-group">
                <label>用户ID:</label>
                <input type="number" id="userId" value="14" />
            </div>
            <div class="input-group">
                <label>论文ID:</label>
                <input type="number" id="thesisId" value="34" />
            </div>
        </div>

        <div class="section">
            <h3>🔧 修复测试</h3>
            <button onclick="testSinglePayment()">单次支付测试</button>
            <button onclick="testPaymentFlow()">完整支付流程</button>
            <button onclick="simulatePayment()" class="success">模拟支付成功</button>
            <button onclick="clearResult()">清空结果</button>
            
            <div id="test-status" class="status warning">等待测试操作...</div>
        </div>

        <div class="section">
            <h3>📊 测试结果</h3>
            <div id="test-result" class="result">等待测试结果...\n</div>
        </div>
    </div>

    <!-- 支付弹窗 -->
    <div id="paymentModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal()">&times;</span>
            <h3>微信支付</h3>
            <div>支付金额: ¥<span id="payAmount">10.00</span></div>
            <img id="qrCode" class="qr-code" src="" alt="支付二维码" />
            <div>订单号: <span id="orderId"></span></div>
            <button onclick="checkPaymentStatus()">检查支付状态</button>
            <button onclick="closeModal()">关闭</button>
        </div>
    </div>

    <script>
        let currentOrderId = '';
        let paymentCheckInterval = null;

        function log(message, type = 'info') {
            const resultElement = document.getElementById('test-result');
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warning' ? '⚠️' : 'ℹ️';
            resultElement.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            resultElement.scrollTop = resultElement.scrollHeight;
        }

        function showStatus(message, type) {
            const statusElement = document.getElementById('test-status');
            statusElement.textContent = message;
            statusElement.className = `status ${type}`;
        }

        function clearResult() {
            document.getElementById('test-result').textContent = '日志已清空...\n';
            showStatus('等待测试操作...', 'warning');
        }

        function getParams() {
            return {
                userId: parseInt(document.getElementById('userId').value),
                thesisId: parseInt(document.getElementById('thesisId').value)
            };
        }

        async function testSinglePayment() {
            const params = getParams();
            log(`开始单次支付测试 - 用户: ${params.userId}, 论文: ${params.thesisId}`, 'info');
            
            try {
                showStatus('正在创建支付订单...', 'warning');
                
                const response = await fetch('/api/thesis/payDownload', {
                    method: 'POST',
                    headers: { 
                        'Content-Type': 'application/json',
                        'Cache-Control': 'no-cache'
                    },
                    credentials: 'include',
                    body: JSON.stringify({ 
                        thesisId: params.thesisId, 
                        paymentMethod: 'wechat' 
                    })
                });
                
                const result = await response.json();
                log(`支付API响应: ${JSON.stringify(result)}`, 'info');
                
                if (result.is_success && result.data) {
                    currentOrderId = result.data.order_id;
                    log(`支付订单创建成功: ${currentOrderId}`, 'success');
                    log(`支付金额: ${result.data.price} 元`, 'info');
                    
                    // 显示支付弹窗
                    showPaymentModal(result.data);
                    showStatus(`订单创建成功: ${currentOrderId}`, 'success');
                } else {
                    log(`创建失败: ${result.message}`, 'error');
                    showStatus(`创建失败: ${result.message}`, 'error');
                }
            } catch (error) {
                log(`请求失败: ${error.message}`, 'error');
                showStatus(`请求失败: ${error.message}`, 'error');
            }
        }

        function showPaymentModal(data) {
            document.getElementById('payAmount').textContent = data.price || '10.00';
            document.getElementById('orderId').textContent = data.order_id || '';
            document.getElementById('qrCode').src = data.qr_code_url || 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgZmlsbD0iI2Y1ZjVmNSIvPjx0ZXh0IHg9IjEwMCIgeT0iMTAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSIgZm9udC1mYW1pbHk9InNhbnMtc2VyaWYiIGZvbnQtc2l6ZT0iMTQiIGZpbGw9IiM5OTkiPuaUr+S7mOS6jOe7tOegge+8jOivt+aJq+eggTwvdGV4dD48L3N2Zz4=';
            
            document.getElementById('paymentModal').style.display = 'block';
            
            // 开始轮询支付状态
            startPaymentPolling();
        }

        function closeModal() {
            document.getElementById('paymentModal').style.display = 'none';
            stopPaymentPolling();
        }

        function startPaymentPolling() {
            stopPaymentPolling();
            paymentCheckInterval = setInterval(checkPaymentStatus, 3000);
        }

        function stopPaymentPolling() {
            if (paymentCheckInterval) {
                clearInterval(paymentCheckInterval);
                paymentCheckInterval = null;
            }
        }

        async function checkPaymentStatus() {
            if (!currentOrderId) {
                log('没有可查询的订单', 'warning');
                return;
            }

            const params = getParams();
            
            try {
                const response = await fetch('/api/thesis/paymentStatus', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    credentials: 'include',
                    body: JSON.stringify({ 
                        thesisId: params.thesisId, 
                        orderId: currentOrderId 
                    })
                });
                
                const result = await response.json();
                
                if (result.is_success && result.data) {
                    if (result.data.is_paid) {
                        log('支付成功！', 'success');
                        showStatus('支付成功', 'success');
                        stopPaymentPolling();
                        closeModal();
                    } else {
                        log('支付未完成', 'info');
                    }
                } else {
                    log(`查询失败: ${result.message}`, 'error');
                }
            } catch (error) {
                log(`查询失败: ${error.message}`, 'error');
            }
        }

        async function simulatePayment() {
            if (!currentOrderId) {
                log('请先创建支付订单', 'error');
                return;
            }
            
            try {
                const response = await fetch('/api/thesis/simulatePayment', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    credentials: 'include',
                    body: JSON.stringify({ order_id: currentOrderId })
                });
                
                const result = await response.json();
                
                if (result.is_success) {
                    log('模拟支付成功', 'success');
                    showStatus('模拟支付成功', 'success');
                } else {
                    log(`模拟支付失败: ${result.message}`, 'error');
                }
            } catch (error) {
                log(`模拟支付失败: ${error.message}`, 'error');
            }
        }

        async function testPaymentFlow() {
            log('开始完整支付流程测试', 'info');
            
            // 1. 创建支付订单
            await testSinglePayment();
            
            // 等待2秒
            setTimeout(async () => {
                if (currentOrderId) {
                    // 2. 模拟支付
                    await simulatePayment();
                    
                    // 等待1秒后检查状态
                    setTimeout(checkPaymentStatus, 1000);
                }
            }, 2000);
        }

        // 页面加载完成
        document.addEventListener('DOMContentLoaded', function() {
            log('🔧 支付功能修复测试工具已加载', 'success');
            log('📋 这个工具可以测试修复后的支付功能', 'info');
        });

        // 点击模态框外部关闭
        window.onclick = function(event) {
            const modal = document.getElementById('paymentModal');
            if (event.target === modal) {
                closeModal();
            }
        }
    </script>
</body>
</html>
