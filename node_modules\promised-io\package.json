{"name": "promised-io", "version": "0.3.6", "author": {"name": "<PERSON>"}, "description": "Promise-based IO", "licenses": [{"type": "AFLv2.1", "url": "http://trac.dojotoolkit.org/browser/dojo/trunk/LICENSE#L43"}, {"type": "BSD", "url": "http://trac.dojotoolkit.org/browser/dojo/trunk/LICENSE#L13"}], "repository": {"type": "git", "url": "http://github.com/kriszyp/promised-io"}, "contributors": ["<PERSON> <<EMAIL>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>"], "keywords": ["promise", "io"], "mappings": {"patr": "http://github.com/kriszyp/patr/zipball/v0.2.5"}, "directories": {"lib": "."}, "main": "./promise", "devDependencies": {"patr": ">=0.2.6", "promises-aplus-tests": "*"}, "scripts": {"test": "node tests/"}}