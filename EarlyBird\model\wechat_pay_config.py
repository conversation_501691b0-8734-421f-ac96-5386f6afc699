#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
作者名：EarlyBird
作者邮箱：<EMAIL>
官方网址：https://blog.zaoniao.vip

微信支付配置数据模型
用于在数据库中存储微信支付配置信息
"""

from datetime import datetime
from EarlyBird.common.database import db

class WeChatPayConfig(db.Model):
    """微信支付配置表"""
    __tablename__ = 'wechat_pay_config'
    
    id = db.Column(db.Integer, primary_key=True, autoincrement=True, comment='配置ID')
    
    # 基础配置
    appid = db.Column(db.String(64), nullable=False, comment='微信支付应用ID')
    mchid = db.Column(db.String(32), nullable=False, comment='微信支付商户号')
    api_v3_key = db.Column(db.String(128), nullable=False, comment='商户APIv3密钥')
    serial_no = db.Column(db.String(64), nullable=False, comment='商户证书序列号')
    
    # 证书文件路径
    private_key_path = db.Column(db.String(255), nullable=False, comment='商户私钥文件路径')
    platform_cert_path = db.Column(db.String(255), nullable=True, comment='微信支付平台证书路径')
    
    # 回调配置
    notify_url = db.Column(db.String(255), nullable=False, comment='回调通知地址')
    
    # 环境配置
    is_sandbox = db.Column(db.Boolean, default=False, comment='是否启用沙箱环境')
    
    # 沙箱环境配置
    sandbox_appid = db.Column(db.String(64), nullable=True, comment='沙箱环境应用ID')
    sandbox_mchid = db.Column(db.String(32), nullable=True, comment='沙箱环境商户号')
    sandbox_api_v3_key = db.Column(db.String(128), nullable=True, comment='沙箱环境APIv3密钥')
    sandbox_serial_no = db.Column(db.String(64), nullable=True, comment='沙箱环境证书序列号')
    sandbox_private_key_path = db.Column(db.String(255), nullable=True, comment='沙箱环境私钥文件路径')
    
    # 状态配置
    is_enabled = db.Column(db.Boolean, default=True, comment='是否启用微信支付')
    is_test_mode = db.Column(db.Boolean, default=False, comment='是否测试模式')
    
    # 时间戳
    created_at = db.Column(db.DateTime, default=datetime.now, comment='创建时间')
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now, comment='更新时间')
    
    # 备注
    remark = db.Column(db.Text, nullable=True, comment='备注信息')
    
    def __repr__(self):
        return f'<WeChatPayConfig {self.id}>'
    
    def to_dict(self):
        """转换为字典，提供前端期望的字段名格式"""
        return {
            'id': self.id,
            'name': getattr(self, 'name', '微信支付配置'),
            # 提供两种格式的字段名以兼容前端
            'appid': self.appid,
            'app_id': self.appid,   # 前端期望的字段名
            'mchid': self.mchid,
            'mch_id': self.mchid,   # 前端期望的字段名
            'api_v3_key': self.api_v3_key,
            'serial_no': self.serial_no,
            'private_key_path': self.private_key_path,
            'platform_cert_path': self.platform_cert_path,
            'notify_url': self.notify_url,
            'is_sandbox': self.is_sandbox,
            'sandbox_appid': self.sandbox_appid,
            'sandbox_mchid': self.sandbox_mchid,
            'sandbox_api_v3_key': self.sandbox_api_v3_key,
            'sandbox_serial_no': self.sandbox_serial_no,
            'sandbox_private_key_path': self.sandbox_private_key_path,
            'is_enabled': self.is_enabled,
            'is_test_mode': self.is_test_mode,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            'updated_at': self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None,
            'remark': self.remark
        }
    
    @classmethod
    def get_active_config(cls):
        """获取当前激活的配置"""
        return cls.query.filter_by(is_enabled=True).first()
    
    @classmethod
    def get_config_dict(cls):
        """获取配置字典，用于初始化微信支付"""
        config = cls.get_active_config()
        if not config:
            return None
        
        if config.is_sandbox:
            return {
                'appid': config.sandbox_appid,
                'mchid': config.sandbox_mchid,
                'api_v3_key': config.sandbox_api_v3_key,
                'serial_no': config.sandbox_serial_no,
                'private_key_path': config.sandbox_private_key_path,
                'platform_cert_path': config.platform_cert_path,
                'notify_url': config.notify_url,
                'sandbox': True,
                'is_test_mode': config.is_test_mode
            }
        else:
            return {
                'appid': config.appid,
                'mchid': config.mchid,
                'api_v3_key': config.api_v3_key,
                'serial_no': config.serial_no,
                'private_key_path': config.private_key_path,
                'platform_cert_path': config.platform_cert_path,
                'notify_url': config.notify_url,
                'sandbox': False,
                'is_test_mode': config.is_test_mode
            } 