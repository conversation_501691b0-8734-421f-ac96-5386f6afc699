!function() {
  'use strict';

  var 七,  // issue #222
      $１,  // issue #222
      ლ_ಠ益ಠ_ლ;  // issue #324
};

!function() {
  'use strict';

  // test if Unicode canonical equivalence is ignored
  var \u006E\u0303,
      \u00F1;
};

!function() {
  'use strict';

  var \uFF38,  // Ｘ Lu (third to last)
      \uFF58,  // ｘ Ll (third to last)
      \u1FBC,  // ᾼ Lt (third to last)
      \uFF70,  // ｰ Lm (third to last)
      \u4DB3,  // 䶳 Lo (third to last within a range)
      \u97CA,  // 韊 Lo (third to last within a range)
      \uD7A1,  // 힡 Lo (third to last within a range)
      \uFFDA,  // ￚ Lo (third to last)
      \uA6ED,  // ꛭ Nl (third to last)
      \u0024,  // $
      \u005F,  // _
      \u0024\uFF38,  // Ｘ Lu (third to last)
      \u0024\uFF58,  // ｘ Ll (third to last)
      \u0024\u1FBC,  // ᾼ Lt (third to last)
      \u0024\uFF70,  // ｰ Lm (third to last)
      \u0024\u4DB3,  // 䶳 Lo (third to last within a range)
      \u0024\u97CA,  // 韊 Lo (third to last within a range)
      \u0024\uD7A1,  // 힡 Lo (third to last within a range)
      \u0024\uFFDA,  // ￚ Lo (third to last)
      \u0024\uA6ED,  // ꛭ Nl (third to last)
      \u0024\uFE24,  // ︤  Mn (third to last)
      \u0024\uABE9,  // ꯩ Mc (third to last)
      \u0024\uFF17,  // ７ Nd (third to last)
      \u0024\uFE4E,  // ﹎ Pc (third to last)
      \u0024\u200C,  // ZERO WIDTH NON-JOINER
      \u0024\u200D,  // ZERO WIDTH JOINER
      \u0024\u0024,  // $
      \u0024\u005F;  // _
};

!function() {
  'use strict';

  var Ｘ,
      ｘ,
      ᾼ,
      ｰ,
      䶳,
      韊,
      힡,
      ￚ,
      ꛭ,
      $,
      _,
      $Ｘ,
      $ｘ,
      $ᾼ,
      $ｰ,
      $䶳,
      $韊,
      $힡,
      $ￚ,
      $ꛭ,
      $︤,
      $ꯩ,
      $７,
      $﹎,
      $‌,
      $‍,
      $$,
      $_;
};

