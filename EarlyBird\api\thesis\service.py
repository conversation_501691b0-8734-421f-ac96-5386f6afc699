from uuid import uuid4
from EarlyBird.ExtendRegister.db_register import db
from EarlyBird.ExtendRegister.model_register import Thesis, Paragraph, GenerateTask
import json
import logging
import shortuuid
from typing import List
from EarlyBird.common import Result, ApiResponse, TaskStatus, countCharacters
from EarlyBird.common.docx import ThesisDocx
from sqlalchemy import desc
from datetime import datetime

from EarlyBird.api.talk2ai.utils import travelOutlineDict
from EarlyBird.api.thesis.schema import (
    ParamSaveSingleParagraph,
    ParamSaveDigest,
    ParamThesisId,
    MovePara,
    SaveNewPara,
    DeleteParagraph,
    SaveThesisProperty,
    ParamGenerateOutline,
    ParamPayDownload,
    ParamPaymentStatus,
    ParamConfirmPayment
)

from EarlyBird.common.outline import OutlineKit

logger = logging.getLogger(__name__)
LOGGER = logging.getLogger(__name__)


class ThesisServie:

    def deletePara(self, body: DeleteParagraph) -> Result:
        try:
            # 检查
            thesis: Thesis = Thesis.query.filter(Thesis.id == body.thesisId).first()
            if thesis is None:
                return Result().error("未查询到此论文")
            if thesis.uid != body.userId:
                return Result().error("没有查询到此论文")

            para: Paragraph = (
                Paragraph.query.filter(Paragraph.thesisId == body.thesisId)
                .filter(Paragraph.paraId == body.paraId)
                .first()
            )

            if para is None:
                return Result().error(f"没有查询到此段落{body.paraId}")

            # 从tree中删除
            outlineKit = OutlineKit.restoreFromDict(thesis.outline)
            nodeWaitDeleted = outlineKit.findNodeByParaId(body.paraId)
            if nodeWaitDeleted is None:
                return Result().error(f"提纲中没有此段 {body.paraId}")
            allParaIdWaitDeleted = nodeWaitDeleted.getAllChildrenId()
            allParaIdWaitDeleted.append(body.paraId)
            LOGGER.info(f"以下段落将被删除 {allParaIdWaitDeleted}")

            res: Result = outlineKit.deletePara(body.paraId)
            # outlineKit.debugPrint()
            if not res.is_success():
                return res

            # 保存提纲
            thesis.outline = outlineKit.dumpsToDict()
            thesis.save()

            # 删除段落
            Paragraph.query.filter(Paragraph.thesisId == body.thesisId).filter(
                Paragraph.paraId.in_(allParaIdWaitDeleted)
            ).delete()
            db.session.commit()

            return Result()

        except Exception as e:
            LOGGER.exception(e)
        return Result()

    def saveNewPara(self, body: SaveNewPara) -> Result:
        try:
            thesis: Thesis = Thesis.query.filter(Thesis.id == body.thesisId).first()
            if thesis is None:
                return Result().error("未查询到此论文")
            if thesis.uid != body.userId:
                return Result().error("没有查询到此论文")

            outlineKit = OutlineKit.restoreFromDict(thesis.outline)
            if body.addType == "next":
                res: Result = outlineKit.addParaAfter(
                    body.baseParaId, body.newTitle, body.newContent
                )
            else:
                res: Result = outlineKit.addChildPara(
                    body.baseParaId, body.newTitle, body.newContent
                )

            # outlineKit.debugPrint()
            if not res.is_success():
                return res

            newParaId = res.data
        except Exception as e:
            LOGGER.exception(e)
            return Result().error(str(e))

        try:
            Paragraph(
                uid=body.userId,
                thesisId=body.thesisId,
                paraId=newParaId,
                title=body.newTitle,
                paragraph=body.newContent,
            ).save()

            thesis: Thesis = Thesis.query.filter(Thesis.id == body.thesisId).first()
            thesis.outline = outlineKit.dumpsToDict()
            thesis.save()

            return Result()

        except Exception as e:
            LOGGER.exception(e)
            return Result().error(str(e))

    def movelPara(self, body: MovePara) -> Result:
        try:
            thesis: Thesis = Thesis.query.filter(Thesis.id == body.thesisId).first()

            if thesis is None:
                return Result().error("未查询到此论文")

            if thesis.uid != body.userId:
                return ApiResponse().error("没有未查询到此论文").json()

            outlineKit = OutlineKit.restoreFromDict(thesis.outline)

            res: Result = outlineKit.movePara(body.moveType, body.paraId)
            if not res.is_success():
                return res

            thesis: Thesis = Thesis.query.filter(Thesis.id == body.thesisId).first()
            thesis.outline = outlineKit.dumpsToDict()
            thesis.save()

            return Result()
        except Exception as e:
            LOGGER.exception(e)

            return Result().error(str(e))

    def getOutlineProgressById(self, param: ParamGenerateOutline) -> Result:
        thesis: Thesis = Thesis.query.filter(Thesis.id == param.thesisId).first()

        if thesis is None:
            return Result().error("未查询到此论文")

        if thesis.uid != param.userId:
            return ApiResponse().error("未查询到此论文").json()

        outline = thesis.outline

        allParasMap = {}
        paraList: List[Paragraph] = Paragraph.query.filter(
            Paragraph.thesisId == param.thesisId
        ).all()
        for p in paraList:
            allParasMap[p.paraId] = p

        # print(allParasMap.keys())

        wrap = {}
        wrap["totalWait"] = 0

        def _cb(node):
            paraId = node["id"]
            if paraId not in allParasMap:
                # LOGGER.error(f"{paraId} 不在 allParasMap 中")
                node["status"] = "-"
                return

            p: Paragraph = allParasMap[paraId]
            node["status"] = p.status
            node["title"] = p.title

            if p.status == TaskStatus.INIT or p.status == TaskStatus.RUNING:
                wrap["totalWait"] += 1

        outlineWithState = travelOutlineDict(outline, _cb)

        return Result().setData(
            {
                "outline": outlineWithState,
                "totalWait": wrap["totalWait"],
                "thesisStatus": thesis.status,
            }
        )

    def getProgress(self, param: ParamThesisId) -> Result:
        paras: list[Paragraph] = Paragraph.query.filter(
            Paragraph.thesisId == param.thesisId
        ).all()
        result = []
        for para in paras:
            result.append({"title": para.title, "paraId": para.paraId, "state": "s"})
        return Result().setData(result)

    def saveThesisProperty(self, param: SaveThesisProperty) -> Result:
        try:
            thesis: Thesis = Thesis.query.get(param.thesisId)
            if thesis is None:
                return Result().error("thesis not found")

            if thesis.uid != param.userId:
                return Result().error("thesis not found in your list")

            updateDict = {}
            if param.propName == "thanks":
                updateDict["thanks"] = param.content
            elif param.propName == "references":
                updateDict["references"] = param.content
            else:
                return Result().error("propName invalid")

            Thesis.query.filter(Thesis.id == param.thesisId).update(updateDict)
            db.session.commit()
            return Result()
        except Exception as e:
            return Result().error(str(e))

    def saveDigest(self, param: ParamSaveDigest) -> Result:
        try:
            thesis: Thesis = Thesis.query.get(param.thesisId)
            if thesis is None:
                return Result().error("thesis not found")

            if thesis.uid != param.userId:
                return Result().error("thesis not found in your list")

            Thesis.query.filter(Thesis.id == param.thesisId).update(
                {
                    "digest": param.digest,
                    "digestEn": param.digestEn,
                    "keywords": param.keywords,
                    "keywordsEn": param.keywordsEn,
                }
            )
            db.session.commit()
            return Result()
        except Exception as e:
            return Result().error(str(e))

    def saveSingleParagraph(self, param: ParamSaveSingleParagraph) -> Result:
        try:
            thesis: Thesis = Thesis.query.get(param.thesisId)
            if thesis is None:
                return Result().error("thesis not found")

            if thesis.uid != param.userId:
                return Result().error("thesis not found in your list")

            para: Paragraph = (
                Paragraph.query.filter(Paragraph.paraId == param.paragraphId)
                .filter(Paragraph.thesisId == param.thesisId)
                .first()
            )

            if para is None:
                return Result().error(f"paragraph not found {param.paragraphId}")
            Paragraph.query.filter(Paragraph.id == para.id).update(
                {"paragraph": param.text, "title": param.title}
            )
            db.session.commit()
            return Result()
        except Exception as e:
            return Result().error(str(e))

    def exportThesis(self, body: ParamThesisId) -> Result:
        try:
            thesis: Thesis = Thesis.query.get(body.thesisId)
            if thesis is None:
                return Result().error("thesis not found")
            if thesis.uid != body.userId:
                return Result().error("thesis not found in your list")

            # 检查下载收费配置
            from EarlyBird.model.payment import PaymentConfig, Payment, ThesisDownloadRecord
            from EarlyBird.model.user import User
            import logging
            
            logger = logging.getLogger(__name__)
            logger.info(f"用户 {body.userId} 请求导出论文 {body.thesisId}")
            
            # 获取用户信息
            user = User.query.get(body.userId)
            if not user or not hasattr(user, 'isVip'):
                logger.error(f"用户 {body.userId} 信息异常")
                return Result().error("用户信息异常，请联系管理员。")
            
            # 获取用户VIP状态
            is_vip = user.isVip
            logger.info(f"用户 {body.userId} VIP状态: {is_vip}")
            
            # 检查是否启用收费
            payment_enabled_config = PaymentConfig.query.filter_by(config_key='thesis.download.is_active', is_deleted=False).first()
            payment_enabled = payment_enabled_config and payment_enabled_config.config_value == 'true'
            logger.info(f"论文下载收费功能是否启用: {payment_enabled}")
            
            # 如果未启用收费，直接允许下载
            if not payment_enabled:
                logger.info(f"论文下载收费功能未启用，用户 {body.userId} 可以免费下载论文 {body.thesisId}")
                # 创建免费下载记录
                self._ensureDownloadRecord(body.userId, body.thesisId, "free_disabled", 0.0)
                return Result().setData({
                    "file": self._exportThesisToDocx(thesis),
                    "free_reason": "disabled",
                    "message": "论文下载功能免费"
                })
            
            # 检查是否是VIP用户
            vip_free_config = PaymentConfig.query.filter_by(config_key='thesis.download.vip_free', is_deleted=False).first()
            vip_free = vip_free_config and vip_free_config.config_value == 'true'
            logger.info(f"VIP用户是否免费下载: {vip_free}")
            
            # 如果是VIP用户且VIP可以免费下载，则跳过收费检查
            if is_vip and vip_free:
                logger.info(f"用户 {body.userId} 是VIP用户，免费下载论文 {body.thesisId}")
                # 确保有下载记录
                self._ensureDownloadRecord(body.userId, body.thesisId, "vip_free", 0.0)
                # 添加一个提示信息
                return Result().setData({
                    "file": self._exportThesisToDocx(thesis),
                    "free_reason": "vip_free",
                    "message": "您是VIP用户，可以免费下载论文"
                })
            
            # 检查是否首次下载免费
            first_free_config = PaymentConfig.query.filter_by(config_key='thesis.download.first_free', is_deleted=False).first()
            first_free = first_free_config and first_free_config.config_value == 'true'
            logger.info(f"首次下载是否免费: {first_free}")
            
            # 检查用户是否有任何下载记录（包括免费和付费）
            any_download_record = ThesisDownloadRecord.query.filter_by(uid=body.userId).first()

            logger.info(f"用户 {body.userId} 是否有任何下载记录: {any_download_record is not None}")

            # 如果没有任何下载记录且首次下载免费
            if first_free and not any_download_record:
                logger.info(f"用户 {body.userId} 首次下载论文 {body.thesisId}，免费")
                # 创建下载记录
                self._ensureDownloadRecord(body.userId, body.thesisId, "free_first_time", 0.0)
                
                # 允许下载
                return Result().setData({
                    "file": self._exportThesisToDocx(thesis),
                    "free_reason": "first_free",
                    "message": "首次下载免费，已为您创建下载记录"
                })
            
            # 1. 检查当前论文的下载记录是否已标记为已支付
            download_record = ThesisDownloadRecord.query.filter_by(uid=body.userId, thesis_id=body.thesisId).first()

            # 只有当下载记录存在且已支付，并且不是免费下载的记录时，才视为已支付
            if download_record and download_record.is_paid:
                if download_record.payment_method not in ['free_first_time', 'vip_free', 'free_disabled']:
                    # 对于付费记录，需要验证支付订单是否真的成功
                    if download_record.payment_order_id:
                        from EarlyBird.model.payment import Payment
                        payment = Payment.query.filter_by(out_trade_no=download_record.payment_order_id).first()
                        if payment and payment.status == 1:
                            logger.info(f"用户 {body.userId} 已有已支付的下载记录，可直接下载论文 {body.thesisId}")
                            return Result().setData({
                                "file": self._exportThesisToDocx(thesis),
                                "free_reason": "already_paid",
                                "message": "您已支付过此论文，可以免费下载"
                            })
                        else:
                            logger.warning(f"用户 {body.userId} 的下载记录标记为已支付，但支付订单状态异常")
                            # 重置下载记录状态
                            download_record.is_paid = False
                            download_record.save()
                    else:
                        logger.warning(f"用户 {body.userId} 的下载记录标记为已支付，但没有支付订单号")
                        # 重置下载记录状态
                        download_record.is_paid = False
                        download_record.save()
                else:
                    # 免费记录也允许下载
                    logger.info(f"用户 {body.userId} 有免费下载记录，可直接下载论文 {body.thesisId}")
                    return Result().setData({
                        "file": self._exportThesisToDocx(thesis),
                        "free_reason": download_record.payment_method,
                        "message": "您可以免费下载此论文"
                    })
            
            # 2. 检查支付记录 - 精确匹配特定论文ID的支付记录
            payment_record = Payment.query.filter_by(
                user_id=body.userId,
                product_id=f"thesis_download_{body.thesisId}",
                status=1  # 已支付
            ).first()
            
            # 3. 如果没有精确匹配，尝试查找通用论文下载支付记录
            if not payment_record:
                payment_records = Payment.query.filter_by(
                    user_id=body.userId,
                    product_id="thesis_download",
                    status=1  # 已支付
                ).all()
                
                if payment_records:
                    logger.info(f"找到用户 {body.userId} 的通用论文下载支付记录，数量: {len(payment_records)}")
                    # 检查这些记录是否已经被使用过
                    unused_payment = None
                    for payment in payment_records:
                        # 查找是否有使用此订单号的下载记录
                        used_record = ThesisDownloadRecord.query.filter_by(
                            payment_order_id=payment.out_trade_no,
                            is_paid=True
                        ).first()
                        
                        if not used_record:
                            unused_payment = payment
                            logger.info(f"找到未使用的通用支付记录，订单号: {payment.out_trade_no}")
                            break
                    
                    if unused_payment:
                        payment_record = unused_payment
            
            # 如果找到了有效的支付记录
            if payment_record:
                logger.info(f"用户 {body.userId} 已支付过论文，订单号: {payment_record.out_trade_no}")
                
                # 创建或更新下载记录，确保标记为已支付
                self._ensureDownloadRecord(
                    body.userId, 
                    body.thesisId,
                    payment_record.pay_type,
                    float(payment_record.amount),
                    payment_record.out_trade_no
                )
                
                # 允许下载
                return Result().setData({
                    "file": self._exportThesisToDocx(thesis),
                    "free_reason": "payment_found",
                    "message": "已找到您的支付记录，可以下载论文"
                })
            
            # 需要支付 - 从配置中读取价格
            try:
                price_config = PaymentConfig.query.filter_by(config_key='thesis.download.price', is_deleted=False).first()
                if price_config and price_config.config_value:
                    price = float(price_config.config_value)
                    logger.info(f"从配置中读取到论文下载价格: {price}")
                else:
                    price = 10.0  # 默认价格
                    logger.warning(f"未找到价格配置或价格配置为空，使用默认价格: {price}")
            except Exception as e:
                price = 10.0  # 出错时使用默认价格
                logger.error(f"读取价格配置时出错: {str(e)}，使用默认价格: {price}")
            
            logger.info(f"用户 {body.userId} 需要支付才能下载论文 {body.thesisId}，价格: {price}")
            
            # 返回需要支付的信息
            return Result().error("需要支付").setData({
                "need_payment": True,
                "price": price,
                "thesis_id": body.thesisId,
                "user_id": body.userId,
                "message": "需要支付才能下载此论文"
            })
            
        except Exception as e:
            LOGGER.exception(e)
            return Result().error(f"导出论文失败: {str(e)}")
    
    def _ensureDownloadRecord(self, user_id, thesis_id, payment_method, price, order_id=None):
        """确保存在下载记录，如果不存在则创建，如果存在则更新"""
        from EarlyBird.model.payment import ThesisDownloadRecord
        from datetime import datetime
        
        record = ThesisDownloadRecord.query.filter_by(uid=user_id, thesis_id=thesis_id).first()
        
        if record:
            # 更新记录
            record.is_paid = True
            record.price = price
            record.payment_method = payment_method
            record.payment_time = datetime.now()
            if order_id:
                record.payment_order_id = order_id
            record.save()
        else:
            # 创建新记录
            new_record = ThesisDownloadRecord(
                uid=user_id,
                thesis_id=thesis_id,
                is_paid=True,
                price=price,
                payment_method=payment_method,
                payment_time=datetime.now(),
                payment_order_id=order_id
            )
            db.session.add(new_record)
            db.session.commit()

    def confirmPaymentAndDownload(self, body: ParamConfirmPayment) -> Result:
        """确认支付并下载论文"""
        try:
            from EarlyBird.model.payment import Payment, ThesisDownloadRecord
            from datetime import datetime

            LOGGER.info(f"确认支付并下载论文 - 用户: {body.userId}, 论文: {body.thesisId}, 订单: {body.orderId}")

            # 检查论文是否存在
            thesis: Thesis = Thesis.query.get(body.thesisId)
            if thesis is None:
                LOGGER.error(f"论文不存在: {body.thesisId}")
                return Result().error("论文不存在")
            if thesis.uid != body.userId:
                LOGGER.error(f"用户 {body.userId} 没有权限下载论文 {body.thesisId}")
                return Result().error("您没有权限下载此论文")

            # 查询支付记录
            payment = Payment.query.filter_by(out_trade_no=body.orderId).first()
            if not payment:
                LOGGER.error(f"支付订单不存在: {body.orderId}")
                return Result().error("支付订单不存在")

            LOGGER.info(f"找到支付记录 - 订单: {body.orderId}, 状态: {payment.status}, 金额: {payment.amount}")

            if payment.status != 1:
                LOGGER.warning(f"支付未完成 - 订单: {body.orderId}, 当前状态: {payment.status}")
                return Result().error("支付未完成，请先完成支付")
            
            # 创建或更新下载记录
            download_record = ThesisDownloadRecord.query.filter_by(uid=body.userId, thesis_id=body.thesisId).first()
            
            if download_record:
                # 更新现有记录
                download_record.is_paid = True
                download_record.price = float(payment.amount)
                download_record.payment_method = payment.pay_type
                download_record.payment_time = datetime.now()
                download_record.payment_order_id = body.orderId
                download_record.save()
            else:
                # 创建新记录
                new_record = ThesisDownloadRecord(
                    uid=body.userId,
                    thesis_id=body.thesisId,
                    is_paid=True,
                    price=float(payment.amount),
                    payment_method=payment.pay_type,
                    payment_time=datetime.now(),
                    payment_order_id=body.orderId
                )
                db.session.add(new_record)
                db.session.commit()
            
            # 导出论文
            return Result().setData({
                "file": self._exportThesisToDocx(thesis),
                "payment_confirmed": True,
                "message": "支付确认成功，正在为您下载论文"
            })
            
        except Exception as e:
            LOGGER.exception(e)
            return Result().error(f"确认支付失败: {str(e)}")

    def deleteThesis(self, body: ParamThesisId) -> Result:
        try:
            thesis: Thesis = Thesis.query.get(body.thesisId)
            if thesis is None:
                return Result().error("thesis not found")
            if thesis.uid != body.userId:
                return Result().error("thesis not found in your list")

            Thesis.query.filter(Thesis.id == body.thesisId).delete()
            Paragraph.query.filter(Paragraph.thesisId == body.thesisId).delete()
            db.session.commit()
            return Result()
        except Exception as e:
            LOGGER.exception(e)
            return Result().error(str(e))

    def getListByUid(self, userid: int) -> Result:
        try:
            logger.info(f"查询论文列表，用户ID: {userid}")
            thesisList = (
                Thesis.query.filter(Thesis.uid == userid)
                .order_by(desc(Thesis.create_time))
                .all()
            )
            logger.info(f"查询到 {len(thesisList)} 篇论文")
            
            res = []
            for th in thesisList:
                thJson = th.to_json()
                thJson["create_time"] = datetime.strftime(
                    th.create_time, "%Y-%m-%d %H:%M"
                )
                del thJson["outline"]
                res.append(thJson)
                logger.info(f"论文ID: {th.id}, 标题: {th.title}, 用户ID: {th.uid}")

            logger.info(f"返回论文列表，共 {len(res)} 篇")
            return Result().setData(res)

        except Exception as e:
            LOGGER.exception(e)
            return Result().error(str(e))

    def getThesisById(self, thesisId: int) -> Result:
        try:
            thesis: Thesis = Thesis.query.filter(Thesis.id == thesisId).first()
            if thesis is None:
                return Result().error("未查询到此论文")

            return Result().setData(thesis)
        except Exception as e:
            LOGGER.exception(e)
            return Result().error(f"查询遇到错误{str(e)}")

    def getDetailById(self, body: ParamThesisId) -> Result:
        try:
            thesis: Thesis = Thesis.query.filter(Thesis.id == body.thesisId).first()
            if thesis is None:
                return Result().error("未查询到此论文")

            if thesis.uid != body.userId:
                return Result().error("未查询到此论文2").json()

            allParas = {}
            paragraphs = Paragraph.query.filter(Paragraph.thesisId == thesis.id).all()
            for p in paragraphs:
                allParas[p.paraId] = {"text": p.paragraph, "title": p.title}

            # 把生成的内容放在outline的树结构中
            def _attachParaText(paraDict):
                if paraDict["id"] in allParas:
                    paraDict["text"] = allParas[paraDict["id"]]["text"]
                    paraDict["length"] = countCharacters(paraDict["text"])

                    # 如果用户修改了标题，优先使用，否则使用生成的title
                    user_define_title = allParas[paraDict["id"]]["title"]
                    if user_define_title is not None and user_define_title != "":
                        paraDict["title"] = user_define_title

            thesis.outline = travelOutlineDict(thesis.outline, _attachParaText)
            return Result().setData(thesis)
        except Exception as e:
            LOGGER.exception(e)
            return Result().error(f"查询遇到错误{str(e)}")

    def payDownload(self, body: ParamPayDownload) -> Result:
        """处理论文下载支付"""
        try:
            from EarlyBird.model.payment import ThesisDownloadRecord, PaymentConfig
            from datetime import datetime
            import uuid
            
            # 检查论文是否存在
            thesis: Thesis = Thesis.query.get(body.thesisId)
            if thesis is None:
                return Result().error("论文不存在")
            if thesis.uid != body.userId:
                return Result().error("您没有权限下载此论文")
                
            # 获取收费金额
            price_config = PaymentConfig.query.filter_by(config_key='thesis.download.price', is_deleted=False).first()
            price = float(price_config.config_value) if price_config else 10.0
            
            # 生成订单号
            order_id = f"thesis_download_{uuid.uuid4().hex[:12]}"
            
            # 创建或更新下载记录（但不标记为已支付，等待实际支付完成）
            download_record = ThesisDownloadRecord.query.filter_by(uid=body.userId, thesis_id=body.thesisId).first()

            if download_record:
                # 更新现有记录，但不标记为已支付
                download_record.is_paid = False  # ❌ 修复：不要提前标记为已支付
                download_record.price = price
                download_record.payment_method = body.paymentMethod
                download_record.payment_time = None  # 支付完成后再设置
                download_record.payment_order_id = order_id
                download_record.save()
            else:
                # 创建新记录，但不标记为已支付
                new_record = ThesisDownloadRecord(
                    uid=body.userId,
                    thesis_id=body.thesisId,
                    is_paid=False,  # ❌ 修复：不要提前标记为已支付
                    price=price,
                    payment_method=body.paymentMethod,
                    payment_time=None,  # 支付完成后再设置
                    payment_order_id=order_id
                )
                db.session.add(new_record)
                db.session.commit()
            
            # 创建支付记录
            from EarlyBird.model.payment import Payment

            # 检查是否已存在支付记录
            existing_payment = Payment.query.filter_by(out_trade_no=order_id).first()
            if not existing_payment:
                payment_record = Payment(
                    user_id=body.userId,
                    product_id=f"thesis_download_{body.thesisId}",
                    amount=price,
                    out_trade_no=order_id,
                    status=0,  # 未支付状态
                    pay_type=body.paymentMethod
                )
                db.session.add(payment_record)
                db.session.commit()
                LOGGER.info(f"创建支付记录成功 - 订单: {order_id}, 金额: {price}")

            # 这里应该调用实际的支付接口来生成二维码
            # 为了测试，我们返回一个模拟的二维码URL
            qr_code_url = f"https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=pay_{order_id}"

            return Result().setData({
                "order_id": order_id,
                "price": price,
                "payment_method": body.paymentMethod,
                "qr_code_url": qr_code_url,
                "status": "created"
            })
        except Exception as e:
            LOGGER.exception(e)
            return Result().error(f"支付处理失败: {str(e)}")
            
    def getPaymentStatus(self, body: ParamPaymentStatus) -> Result:
        """获取论文下载支付状态"""
        try:
            from EarlyBird.model.payment import ThesisDownloadRecord, Payment

            LOGGER.info(f"查询支付状态 - 用户: {body.userId}, 论文: {body.thesisId}, 订单: {body.orderId}")

            # 检查论文是否存在
            thesis: Thesis = Thesis.query.get(body.thesisId)
            if thesis is None:
                return Result().error("论文不存在")
            if thesis.uid != body.userId:
                return Result().error("您没有权限查询此论文的支付状态")

            # 查询下载记录
            query = ThesisDownloadRecord.query.filter_by(uid=body.userId, thesis_id=body.thesisId)

            # 如果提供了订单号，则按订单号查询
            if body.orderId:
                query = query.filter_by(payment_order_id=body.orderId)

            download_record = query.first()

            if not download_record:
                LOGGER.warning(f"未找到下载记录 - 用户: {body.userId}, 论文: {body.thesisId}")
                return Result().setData({
                    "is_paid": False,
                    "payment_status": "unpaid",
                    "message": "未找到支付记录"
                })

            # 如果下载记录已标记为已支付，直接返回
            if download_record.is_paid:
                LOGGER.info(f"下载记录已标记为已支付 - 订单: {download_record.payment_order_id}")
                return Result().setData({
                    "is_paid": True,
                    "payment_status": "paid",
                    "payment_method": download_record.payment_method,
                    "payment_time": download_record.payment_time.strftime("%Y-%m-%d %H:%M:%S") if download_record.payment_time else None,
                    "price": download_record.price,
                    "order_id": download_record.payment_order_id
                })

            # 如果下载记录未标记为已支付，但有订单号，检查真实的支付状态
            if download_record.payment_order_id:
                payment = Payment.query.filter_by(out_trade_no=download_record.payment_order_id).first()
                if payment:
                    LOGGER.info(f"找到支付记录 - 订单: {payment.out_trade_no}, 状态: {payment.status}")

                    # 如果支付记录显示已支付，但下载记录未更新，则更新下载记录
                    if payment.status == 1 and not download_record.is_paid:
                        LOGGER.info(f"同步支付状态 - 订单: {payment.out_trade_no}")
                        download_record.is_paid = True
                        download_record.payment_time = payment.pay_time
                        download_record.save()

                        return Result().setData({
                            "is_paid": True,
                            "payment_status": "paid",
                            "payment_method": download_record.payment_method,
                            "payment_time": payment.pay_time,
                            "price": download_record.price,
                            "order_id": download_record.payment_order_id
                        })
                    elif payment.status == 1:
                        return Result().setData({
                            "is_paid": True,
                            "payment_status": "paid",
                            "payment_method": download_record.payment_method,
                            "payment_time": payment.pay_time,
                            "price": download_record.price,
                            "order_id": download_record.payment_order_id
                        })
                    else:
                        return Result().setData({
                            "is_paid": False,
                            "payment_status": "unpaid",
                            "payment_method": download_record.payment_method,
                            "price": download_record.price,
                            "order_id": download_record.payment_order_id,
                            "message": f"支付状态: {payment.status}"
                        })
                else:
                    LOGGER.warning(f"未找到对应的支付记录 - 订单: {download_record.payment_order_id}")

            # 返回未支付状态
            return Result().setData({
                "is_paid": False,
                "payment_status": "unpaid",
                "payment_method": download_record.payment_method if download_record else None,
                "price": download_record.price if download_record else None,
                "order_id": download_record.payment_order_id if download_record else None,
                "message": "支付未完成"
            })
        except Exception as e:
            LOGGER.exception(e)
            return Result().error(f"查询支付状态失败: {str(e)}")

    def simulatePaymentSuccess(self, order_id: str) -> Result:
        """模拟支付成功（仅用于测试）"""
        try:
            from EarlyBird.model.payment import Payment, ThesisDownloadRecord
            from datetime import datetime

            LOGGER.info(f"模拟支付成功 - 订单: {order_id}")

            # 查找支付记录
            payment = Payment.query.filter_by(out_trade_no=order_id).first()
            if not payment:
                return Result().error("支付订单不存在")

            # 更新支付状态为成功
            payment.status = 1  # 已支付
            payment.pay_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            payment.trade_no = f"mock_trade_{order_id}"
            db.session.commit()

            # 更新下载记录
            download_record = ThesisDownloadRecord.query.filter_by(payment_order_id=order_id).first()
            if download_record:
                download_record.is_paid = True
                download_record.payment_time = datetime.now()
                download_record.save()

            LOGGER.info(f"模拟支付成功完成 - 订单: {order_id}")

            return Result().setData({
                "order_id": order_id,
                "status": "success",
                "message": "支付成功"
            })

        except Exception as e:
            LOGGER.exception(e)
            return Result().error(f"模拟支付失败: {str(e)}")

    # 添加一个辅助方法，用于导出论文到Word文档
    def _exportThesisToDocx(self, thesis):
        docx = ThesisDocx()
        # 封面
        docx.addCover(thesis.title)
        docx.addPageBreak()
        # 摘要
        docx.addDigest(
            digest=thesis.digest,
            digestEn=thesis.digestEn,
            keywords=thesis.keywords,
            keywordsEn=thesis.keywordsEn,
        )
        docx.addPageBreak()
        # 目录
        docx.addToc()
        docx.addPageBreak()

        def _travel(outline: dict, level: int = 1):
            if "title" in outline and outline["title"] != None:
                docx.addContentParagraph(outline["title"], outline["text"], level)
                LOGGER.info(f"add Para:{outline['title']}")
            if "subtitle" in outline:
                for subOutline in outline["subtitle"]:
                    _travel(subOutline, level + 1)

        # 正文
        _travel(thesis.outline, 0)
        # 参考文件，附录，致谢
        docx.addTail(thesis.references, thesis.thanks)

        # Export with base directory
        import os
        base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        export_result = docx.export(thesis.title, base_dir)
        
        return export_result.data["file"]
