#!/usr/bin/env python3
"""
修复Setting表结构和初始化默认配置
"""

import sys
import os
import logging

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def fix_setting_table():
    """修复Setting表并初始化默认配置"""
    try:
        import pymysql
        from EarlyBird.config.config import AppConfig
        
        # 解析数据库连接信息
        db_uri = AppConfig.SQLALCHEMY_DATABASE_URI
        print(f"数据库连接URI: {db_uri}")
        
        # 从URI中提取连接信息
        # mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4
        import re
        match = re.match(r'mysql\+pymysql://([^:]+):([^@]+)@([^:]+):(\d+)/([^?]+)', db_uri)
        if not match:
            print("❌ 无法解析数据库连接URI")
            return
        
        username, password, host, port, database = match.groups()
        port = int(port)
        
        print(f"连接信息: {username}@{host}:{port}/{database}")
        
        # 连接数据库
        connection = pymysql.connect(
            host=host,
            port=port,
            user=username,
            password=password,
            database=database,
            charset='utf8mb4'
        )
        
        cursor = connection.cursor()
        
        print("🔧 开始检查和修复Setting表...")
        
        # 检查表是否存在
        cursor.execute("SHOW TABLES LIKE 'earlybird_paper_setting'")
        if not cursor.fetchone():
            print("❌ earlybird_paper_setting表不存在")
            return
        
        # 检查表结构
        cursor.execute("DESCRIBE earlybird_paper_setting")
        columns = cursor.fetchall()
        
        print("📋 当前表结构:")
        column_names = []
        for column in columns:
            column_name = column[0]
            column_names.append(column_name)
            print(f"  {column[0]} - {column[1]} - {column[2]} - {column[3]}")
        
        # 检查是否需要添加description字段
        if 'description' not in column_names:
            print("🔧 添加description字段...")
            cursor.execute("ALTER TABLE earlybird_paper_setting ADD COLUMN description VARCHAR(200) DEFAULT NULL COMMENT '设置描述'")
            print("✅ description字段添加成功")
        
        # 初始化默认设置
        default_settings = [
            ("modelName", "qianwen", "默认AI模型"),
            ("apikeyQianwen", "", "千问API Key"),
            ("apikeyDeepSeekR1", "", "DeepSeek R1 API Key"),
            ("apikeyKimi", "", "Kimi API Key"),
            ("apikeyDoubao", "", "豆包API Key"),
            ("apikeyOpenai", "", "OpenAI API Key")
        ]
        
        print("\n🔧 初始化默认设置...")
        for setting_key, default_value, desc in default_settings:
            # 检查是否已存在
            cursor.execute("SELECT id FROM earlybird_paper_setting WHERE settingKey = %s", (setting_key,))
            existing = cursor.fetchone()
            
            if existing:
                # 更新描述
                cursor.execute("UPDATE earlybird_paper_setting SET description = %s WHERE settingKey = %s", (desc, setting_key))
                print(f"📋 设置项已存在: {setting_key}")
            else:
                # 创建新设置项
                cursor.execute("""
                    INSERT INTO earlybird_paper_setting 
                    (settingKey, settingValue, description, create_time, update_time, is_deleted, status) 
                    VALUES (%s, %s, %s, NOW(), NOW(), 0, 1)
                """, (setting_key, default_value, desc))
                print(f"✅ 创建设置项: {setting_key} = {default_value}")
        
        # 提交更改
        connection.commit()
        
        # 验证设置
        print("\n🔍 验证设置结果:")
        cursor.execute("SELECT settingKey, settingValue, description FROM earlybird_paper_setting WHERE settingKey IN ('modelName', 'apikeyQianwen', 'apikeyDeepSeekR1', 'apikeyKimi', 'apikeyDoubao', 'apikeyOpenai')")
        settings = cursor.fetchall()
        
        for key, value, desc in settings:
            display_value = value if value else "(空)"
            if "apikey" in key.lower() and value:
                display_value = value[:10] + "..." if len(value) > 10 else value
            print(f"  {key}: {display_value} ({desc})")
        
        cursor.close()
        connection.close()
        print("\n🎉 Setting表修复和初始化完成！")
        
    except Exception as e:
        print(f"❌ 修复失败: {str(e)}")
        import traceback
        traceback.print_exc()

def test_setting_access():
    """测试Setting访问"""
    try:
        from EarlyBird.web_server import create_custom_app
        from EarlyBird.model.setting import Setting
        
        app = create_custom_app()
        if not app:
            print("❌ 无法创建Flask应用")
            return
        
        with app.app_context():
            print("🔍 测试Setting模型访问...")
            
            # 测试查询modelName
            try:
                setting = Setting.query.filter_by(settingKey="modelName").first()
                if setting:
                    print(f"✅ modelName设置: {setting.settingValue}")
                else:
                    print("⚠️ modelName设置不存在")
            except Exception as e:
                print(f"❌ 查询modelName失败: {str(e)}")
            
            # 测试查询API Key
            try:
                setting = Setting.query.filter_by(settingKey="apikeyQianwen").first()
                if setting:
                    display_value = setting.settingValue[:10] + "..." if setting.settingValue and len(setting.settingValue) > 10 else setting.settingValue or "(空)"
                    print(f"✅ apikeyQianwen设置: {display_value}")
                else:
                    print("⚠️ apikeyQianwen设置不存在")
            except Exception as e:
                print(f"❌ 查询apikeyQianwen失败: {str(e)}")
                
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("🔧 Setting表修复工具")
    print("=" * 50)
    
    # 修复表结构和初始化数据
    fix_setting_table()
    
    print("\n" + "=" * 50)
    
    # 测试访问
    print("🔍 测试修复结果...")
    test_setting_access()

if __name__ == "__main__":
    main()
