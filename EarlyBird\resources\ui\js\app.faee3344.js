(()=>{var e={116:(e,t,s)=>{"use strict";e.exports=s.p+"img/log.b0af181b.svg"},119:(e,t,s)=>{"use strict";e.exports=s.p+"img/github.e9dd8a06.svg"},596:(e,t,s)=>{"use strict";e.exports=s.p+"img/wechat.1ebed109.svg"},663:(e,t,s)=>{"use strict";s.d(t,{P:()=>i,k:()=>r});var a=s(5545);function r(e){return(0,a["default"])({url:"/api/home/<USER>",method:"post",data:e,timeout:3e4})}function i(e){return(0,a["default"])({url:"/api/home/<USER>",method:"post",data:e,timeout:3e4})}},744:(e,t,s)=>{"use strict";e.exports=s.p+"img/web.6dd3a966.svg"},980:(e,t,s)=>{"use strict";e.exports=s.p+"img/Steve-Jobs.6f30a5c1.svg"},1031:(e,t,s)=>{"use strict";e.exports=s.p+"img/fwb.9a239a74.svg"},1042:(e,t,s)=>{"use strict";e.exports=s.p+"img/oil.1127456b.svg"},1048:(e,t,s)=>{"use strict";e.exports=s.p+"img/anq.48fc0ebd.svg"},1394:(e,t,s)=>{"use strict";e.exports=s.p+"img/role.30119597.svg"},1447:(e,t,s)=>{"use strict";e.exports=s.p+"img/people.7a231801.svg"},1534:(e,t,s)=>{"use strict";e.exports=s.p+"img/chart.b03ce158.svg"},1576:(e,t,s)=>{"use strict";e.exports=s.p+"img/codeConsole.9c11607a.svg"},1718:(e,t,s)=>{"use strict";e.exports=s.p+"img/list.6197de7f.svg"},1960:(e,t,s)=>{"use strict";e.exports=s.p+"img/alipay.33cad82f.svg"},2055:(e,t,s)=>{"use strict";e.exports=s.p+"img/tab.ed2d5142.svg"},2144:(e,t,s)=>{"use strict";e.exports=s.p+"img/timing.f2f66292.svg"},2255:(e,t,s)=>{"use strict";e.exports=s.p+"img/dept.db0af068.svg"},2495:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>u});var a=function(){var e=this,t=e._self._c;return t("div",{staticClass:"register-container"},[t("div",{staticClass:"register-box"},[e._m(0),t("el-form",{ref:"registerForm",staticClass:"register-form",attrs:{model:e.registerForm,rules:e.registerRules,"label-width":"0"},nativeOn:{submit:function(t){return t.preventDefault(),e.handleRegister.apply(null,arguments)}}},[t("el-form-item",{attrs:{prop:"username"}},[t("el-input",{attrs:{placeholder:"请输入用户名（3-20个字符）","prefix-icon":"el-icon-user",size:"large",clearable:""},model:{value:e.registerForm.username,callback:function(t){e.$set(e.registerForm,"username",t)},expression:"registerForm.username"}})],1),t("el-form-item",{attrs:{prop:"password"}},[t("el-input",{attrs:{type:"password",placeholder:"请输入密码（6-20个字符）","prefix-icon":"el-icon-lock",size:"large","show-password":""},model:{value:e.registerForm.password,callback:function(t){e.$set(e.registerForm,"password",t)},expression:"registerForm.password"}})],1),t("el-form-item",{attrs:{prop:"confirm_password"}},[t("el-input",{attrs:{type:"password",placeholder:"请确认密码","prefix-icon":"el-icon-lock",size:"large","show-password":""},model:{value:e.registerForm.confirm_password,callback:function(t){e.$set(e.registerForm,"confirm_password",t)},expression:"registerForm.confirm_password"}})],1),t("el-form-item",{attrs:{prop:"nickname"}},[t("el-input",{attrs:{placeholder:"请输入昵称（可选）","prefix-icon":"el-icon-user",size:"large",clearable:""},model:{value:e.registerForm.nickname,callback:function(t){e.$set(e.registerForm,"nickname",t)},expression:"registerForm.nickname"}})],1),t("el-form-item",{attrs:{prop:"email"}},[t("el-input",{attrs:{placeholder:"请输入邮箱（可选）","prefix-icon":"el-icon-message",size:"large",clearable:""},model:{value:e.registerForm.email,callback:function(t){e.$set(e.registerForm,"email",t)},expression:"registerForm.email"}})],1),t("el-form-item",[t("el-button",{staticClass:"register-button",attrs:{type:"primary",loading:e.loading,size:"large"},on:{click:e.handleRegister}},[e._v(" "+e._s(e.loading?"注册中...":"立即注册")+" ")])],1)],1),t("div",{staticClass:"register-footer"},[t("p",{staticClass:"login-link"},[e._v(" 已有账号？ "),t("el-link",{attrs:{type:"primary"},on:{click:e.goToLogin}},[e._v("立即登录")])],1)])],1)])},r=[function(){var e=this,t=e._self._c;return t("div",{staticClass:"register-header"},[t("h2",{staticClass:"register-title"},[e._v("用户注册")]),t("p",{staticClass:"register-subtitle"},[e._v("创建您的论文生成系统账号")])])}],i=s(5353);const o={name:"UserRegister",data(){const e=(e,t,s)=>{""===t?s(new Error("请再次输入密码")):t!==this.registerForm.password?s(new Error("两次输入密码不一致")):s()},t=(e,t,s)=>{if(t&&""!==t){const e=/^[^\s@]+@[^\s@]+\.[^\s@]+$/;e.test(t)?s():s(new Error("请输入正确的邮箱格式"))}else s()};return{loading:!1,registerForm:{username:"",password:"",confirm_password:"",nickname:"",email:""},registerRules:{username:[{required:!0,message:"请输入用户名",trigger:"blur"},{min:3,max:20,message:"用户名长度在 3 到 20 个字符",trigger:"blur"},{pattern:/^[a-zA-Z0-9_]+$/,message:"用户名只能包含字母、数字和下划线",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:6,max:20,message:"密码长度在 6 到 20 个字符",trigger:"blur"}],confirm_password:[{required:!0,validator:e,trigger:"blur"}],nickname:[{max:20,message:"昵称长度不能超过 20 个字符",trigger:"blur"}],email:[{validator:t,trigger:"blur"}]}}},methods:{...(0,i.i0)("user",["register"]),async handleRegister(){try{const e=await this.$refs.registerForm.validate();if(!e)return;this.loading=!0;const t={username:this.registerForm.username,password:this.registerForm.password,confirm_password:this.registerForm.confirm_password,nickname:this.registerForm.nickname,email:this.registerForm.email};await this.register(t),this.$message.success("注册成功，已自动登录"),this.$router.push("/")}catch(e){console.error("注册失败:",e),this.$message.error(e.message||"注册失败，请稍后重试")}finally{this.loading=!1}},goToLogin(){this.$router.push("/user/login")}}},n=o;var c=s(1656),l=(0,c.A)(n,a,r,!1,null,"a17a3c0e",null);const u=l.exports},2499:(e,t,s)=>{"use strict";e.exports=s.p+"img/lock.b9bad559.svg"},2522:(e,t,s)=>{"use strict";e.exports=s.p+"img/phone.031c06f0.svg"},2672:(e,t,s)=>{"use strict";e.exports=s.p+"img/car.bf3afa00.svg"},2806:(e,t,s)=>{"use strict";e.exports=s.p+"img/logo.ce3a5bfd.png"},2878:(e,t,s)=>{"use strict";e.exports=s.p+"img/edit.faa16dba.svg"},2886:(e,t,s)=>{"use strict";e.exports=s.p+"img/peoples.3c1ef57b.svg"},2889:(e,t,s)=>{"use strict";e.exports=s.p+"img/fullscreen.8b6452b2.svg"},2895:(e,t,s)=>{"use strict";e.exports=s.p+"img/redis.afacf564.svg"},2949:(e,t,s)=>{"use strict";e.exports=s.p+"img/tree-table.50c32d10.svg"},2956:(e,t,s)=>{"use strict";e.exports=s.p+"img/education.7d9460b1.svg"},2992:(e,t,s)=>{"use strict";e.exports=s.p+"img/doc.abcedfda.svg"},3079:(e,t,s)=>{"use strict";e.exports=s.p+"img/message.737bdd44.svg"},3085:(e,t,s)=>{"use strict";e.exports=s.p+"img/size.99acb4db.svg"},3093:(e,t,s)=>{"use strict";e.exports=s.p+"img/theme.2e7d5780.svg"},3120:(e,t,s)=>{"use strict";e.exports=s.p+"img/user1.36c77b60.svg"},3219:(e,t,s)=>{"use strict";e.exports=s.p+"img/zujian.e1a4e27f.svg"},3281:(e,t,s)=>{"use strict";e.exports=s.p+"img/database.b2e6b737.svg"},3288:(e,t,s)=>{"use strict";e.exports=s.p+"img/gonggao.74def451.svg"},3414:(e,t,s)=>{"use strict";e.exports=s.p+"img/icon.d9d3fda4.svg"},3465:(e,t,s)=>{"use strict";s.d(t,{A:()=>L});var a=s(5471),r=s(5353);const i={outlineTreeSelectedPara:e=>e.thesis.outlineTreeSelectedPara,adminToken:e=>e.admin.adminToken,adminInfo:e=>e.admin.adminInfo,isAdminLoggedIn:e=>!!e.admin.adminToken&&!!e.admin.adminInfo,userToken:e=>e.user.token,userInfo:e=>e.user.userInfo,isUserLoggedIn:e=>!!e.user.token&&!!e.user.userInfo},o=i,n={outlineTreeSelectPara:{}},c={SET_SELECTED_PARAGRAPH:(e,t)=>{e.outlineTreeSelectPara=t}},l={namespaced:!0,state:n,mutations:c};var u=s(5545);function m(e){return(0,u["default"])({url:"/api/user/login",method:"post",data:e})}function d(e){return(0,u["default"])({url:"/api/user/register",method:"post",data:e})}function g(){return(0,u["default"])({url:"/api/user/logout",method:"post"})}function p(){return(0,u["default"])({url:"/api/user/info",method:"post"})}function h(e,t=null){if(!e||"undefined"===e||"null"===e)return t;try{return JSON.parse(e)}catch(s){return console.warn("JSON解析失败:",s,"原始值:",e),t}}const v={userInfo:null,token:null,isLoggedIn:!1},f={SET_USER_INFO:(e,t)=>{e.userInfo=t,e.isLoggedIn=!!t},SET_TOKEN:(e,t)=>{e.token=t},CLEAR_USER:e=>{e.userInfo=null,e.token=null,e.isLoggedIn=!1}},_={async login({commit:e},t){try{const s=await m(t);if(s.is_success)return e("SET_USER_INFO",s.data),localStorage.setItem("userInfo",JSON.stringify(s.data)),localStorage.setItem("loginTime",(new Date).toISOString()),s;throw new Error(s.message||"登录失败")}catch(s){throw s}},async register({commit:e},t){try{const s=await d(t);if(s.is_success)return e("SET_USER_INFO",s.data),localStorage.setItem("userInfo",JSON.stringify(s.data)),localStorage.setItem("loginTime",(new Date).toISOString()),s;throw new Error(s.message||"注册失败")}catch(s){throw s}},async logout({commit:e}){try{return await g(),e("CLEAR_USER"),localStorage.removeItem("userInfo"),localStorage.removeItem("loginTime"),localStorage.removeItem("lastTitleForm"),localStorage.removeItem("lastTitleResult"),localStorage.removeItem("lastTitle"),localStorage.removeItem("lastOutline"),localStorage.removeItem("report_history"),{success:!0}}catch(t){throw e("CLEAR_USER"),localStorage.removeItem("userInfo"),localStorage.removeItem("loginTime"),localStorage.removeItem("lastTitleForm"),localStorage.removeItem("lastTitleResult"),localStorage.removeItem("lastTitle"),localStorage.removeItem("lastOutline"),localStorage.removeItem("report_history"),t}},async getUserInfo({commit:e}){try{const t=await p();if(t.is_success){const s=t.data.user,a={userId:s.id,username:s.username,nickname:s.nickname,isVip:s.isVip,headimg:s.headimg,email:s.email,create_time:s.create_time,last_login_time:s.last_login_time,vip_level:s.vip_level,vip_balance_days:s.vip_balance_days,vip_expire_at:s.vip_expire_at,vip_start_at:s.vip_start_at};return e("SET_USER_INFO",a),localStorage.setItem("userInfo",JSON.stringify(a)),t.data}throw new Error(t.message||"获取用户信息失败")}catch(t){throw t}},async restoreUserState({commit:e}){try{const s=localStorage.getItem("userInfo"),a=localStorage.getItem("loginTime");if(s&&a){const r=h(s);if(!r)return console.warn("用户信息解析失败，清除本地状态"),localStorage.removeItem("userInfo"),localStorage.removeItem("loginTime"),!1;const i=new Date(a),o=new Date,n=(o-i)/864e5;if(!(n<=7))return console.warn("⚠️ 本地登录状态已过期，清除本地状态"),e("CLEAR_USER"),localStorage.removeItem("userInfo"),localStorage.removeItem("loginTime"),this.dispatch("user/clearAllCache"),!1;try{await this.dispatch("user/getUserInfo");return console.log("✅ 用户状态恢复成功，后端session有效"),!0}catch(t){return console.warn("⚠️ 后端session可能已过期，清除本地状态"),e("CLEAR_USER"),localStorage.removeItem("userInfo"),localStorage.removeItem("loginTime"),this.dispatch("user/clearAllCache"),!1}}return!1}catch(t){return console.error("恢复用户状态失败:",t),localStorage.removeItem("userInfo"),localStorage.removeItem("loginTime"),this.dispatch("user/clearAllCache"),!1}},clearUserState({commit:e}){e("CLEAR_USER"),localStorage.removeItem("userInfo"),localStorage.removeItem("loginTime")},clearAllCache({commit:e}){e("CLEAR_USER"),localStorage.removeItem("userInfo"),localStorage.removeItem("loginTime"),localStorage.removeItem("lastTitleForm"),localStorage.removeItem("lastTitleResult"),localStorage.removeItem("lastTitle"),localStorage.removeItem("lastOutline"),localStorage.removeItem("report_history"),localStorage.removeItem("thesisData"),localStorage.removeItem("outlineData"),localStorage.removeItem("contentData"),localStorage.removeItem("chatHistory"),localStorage.removeItem("userSettings"),sessionStorage.clear(),console.log("🧹 已清除所有缓存数据")}},y={userInfo:e=>e.userInfo,isLoggedIn:e=>e.isLoggedIn,isVip:e=>e.userInfo&&e.userInfo.isVip,username:e=>e.userInfo?e.userInfo.username:"",nickname:e=>e.userInfo?e.userInfo.nickname:"",userId:e=>e.userInfo?e.userInfo.userId:null},b={namespaced:!0,state:v,mutations:f,actions:_,getters:y};var w=s(9192),C=s(8987);const S={adminInfo:JSON.parse(localStorage.getItem("admin_info"))||null,adminToken:C.A.get("admin_token")||localStorage.getItem("admin_token")||"",userList:[],userTotal:0,userLoading:!1,thesisList:[],thesisTotal:0,thesisLoading:!1,overviewData:null,userStats:null,thesisStats:null,chatStats:null,systemSettings:null,settingsLoading:!1,accountList:[],accountTotal:0,accountLoading:!1},I={SET_ADMIN_INFO(e,t){e.adminInfo=t,t?localStorage.setItem("admin_info",JSON.stringify(t)):localStorage.removeItem("admin_info")},SET_ADMIN_TOKEN(e,t){e.adminToken=t,t?(C.A.set("admin_token",t,{expires:7}),localStorage.setItem("admin_token",t)):(C.A.remove("admin_token"),localStorage.removeItem("admin_token"))},SET_USER_LIST(e,{list:t,total:s}){e.userList=t,e.userTotal=s},SET_USER_LOADING(e,t){e.userLoading=t},SET_THESIS_LIST(e,{list:t,total:s}){e.thesisList=t,e.thesisTotal=s},SET_THESIS_LOADING(e,t){e.thesisLoading=t},SET_OVERVIEW_DATA(e,t){e.overviewData=t},SET_USER_STATS(e,t){e.userStats=t},SET_THESIS_STATS(e,t){e.thesisStats=t},SET_CHAT_STATS(e,t){e.chatStats=t},SET_SYSTEM_SETTINGS(e,t){e.systemSettings=t},SET_SETTINGS_LOADING(e,t){e.settingsLoading=t},SET_ACCOUNT_LIST(e,{list:t,total:s}){e.accountList=t,e.accountTotal=s},SET_ACCOUNT_LOADING(e,t){e.accountLoading=t}},A={async adminLogin({commit:e},t){try{const a=await w.Qk.login(t);if(a.success){const{token:t,admin_info:r}=a.data;return e("SET_ADMIN_TOKEN",t),e("SET_ADMIN_INFO",r),Promise.resolve().then(s.bind(s,5545)).then((e=>{const s=e.default;s&&s.defaults&&(s.defaults.headers.common["Authorization"]=`Bearer ${t}`)})),{success:!0,data:a.data}}return{success:!1,message:a.message}}catch(a){return{success:!1,message:a.message||"登录失败"}}},async adminLogout({commit:e}){try{console.log("开始管理员登出流程"),e("SET_ADMIN_TOKEN",""),e("SET_ADMIN_INFO",null),C.A.remove("admin_token"),C.A.remove("admin_token",{path:"/"}),C.A.remove("admin_token",{path:"/admin"}),localStorage.removeItem("admin_token");try{const e=await Promise.resolve().then(s.bind(s,5545));e.default&&e.default.defaults&&delete e.default.defaults.headers.common["Authorization"]}catch(t){console.warn("清除axios header失败:",t)}try{await w.Qk.logout(),console.log("登出API调用成功")}catch(t){console.warn("登出API调用失败，但本地状态已清理:",t.message)}return console.log("管理员登出流程完成"),{success:!0,message:"登出成功"}}catch(t){return console.error("管理员登出过程中发生错误:",t),{success:!0,message:"已退出登录"}}},async getAdminProfile({commit:e,state:t}){try{if(!t.adminToken)return{success:!1,message:"未登录"};const s=await w.Qk.getProfile();return s.success?(e("SET_ADMIN_INFO",s.data),{success:!0,data:s.data}):{success:!1,message:s.message}}catch(s){return console.error("获取管理员信息失败:",s),s.response&&401===s.response.status&&(e("SET_ADMIN_TOKEN",""),e("SET_ADMIN_INFO",null)),{success:!1,message:s.message||"获取信息失败"}}},async changePassword({commit:e},t){try{const e=await w.Qk.changePassword(t);return e.success?{success:!0,message:e.message}:{success:!1,message:e.message}}catch(s){return{success:!1,message:s.message||"修改密码失败"}}},async getUserList({commit:e},t){e("SET_USER_LOADING",!0);try{const s=await w.BF.getList(t);if(s.success){const{list:t,total:a}=s.data;return e("SET_USER_LIST",{list:t,total:a}),{success:!0,data:s.data}}return{success:!1,message:s.message}}catch(s){return{success:!1,message:s.message||"获取用户列表失败"}}finally{e("SET_USER_LOADING",!1)}},async getUserDetail({commit:e},t){try{const e=await w.BF.getDetail(t);return e.success?{success:!0,data:e.data}:{success:!1,message:e.message}}catch(s){return{success:!1,message:s.message||"获取用户详情失败"}}},async createUser({commit:e},t){try{const e=await w.BF.create(t);return e.success?{success:!0,message:e.message,data:e.data}:{success:!1,message:e.message}}catch(s){return{success:!1,message:s.message||"创建用户失败"}}},async updateUser({commit:e},{userId:t,data:s}){try{const e=await w.BF.update(t,s);return e.success?{success:!0,message:e.message}:{success:!1,message:e.message}}catch(a){return{success:!1,message:a.message||"更新用户信息失败"}}},async deleteUser({commit:e},t){try{const e=await w.BF.delete(t);return e.success?{success:!0,message:e.message}:{success:!1,message:e.message}}catch(s){return{success:!1,message:s.message||"删除用户失败"}}},async toggleUserLock({commit:e},{userId:t,isLock:s}){try{const e=await w.BF.toggleLock(t,{is_lock:s});return e.success?{success:!0,message:e.message}:{success:!1,message:e.message}}catch(a){return{success:!1,message:a.message||"锁定/解锁用户失败"}}},async vipManage({commit:e},{userId:t,data:s}){try{const e=await w.BF.vipManage(t,s);return e.success?{success:!0,message:e.message}:{success:!1,message:e.message}}catch(a){return{success:!1,message:a.message||"VIP管理失败"}}},async batchDeleteUsers({commit:e},t){try{const e=await w.BF.batchDelete({user_ids:t});return e.success?{success:!0,message:e.message,data:e.data}:{success:!1,message:e.message}}catch(s){return{success:!1,message:s.message||"批量删除用户失败"}}},async batchLockUsers({commit:e},{userIds:t,isLock:s}){try{const e=await w.BF.batchLock({user_ids:t,is_lock:s});return e.success?{success:!0,message:e.message,data:e.data}:{success:!1,message:e.message}}catch(a){return{success:!1,message:a.message||"批量锁定/解锁用户失败"}}},async batchVipUsers({commit:e},{userIds:t,vipLevel:s,days:a,action:r}){try{const e=await w.BF.batchVip({user_ids:t,vip_level:s,days:a,action:r});return e.success?{success:!0,message:e.message,data:e.data}:{success:!1,message:e.message}}catch(i){return{success:!1,message:i.message||"批量VIP管理失败"}}},async getThesisList({commit:e},t){e("SET_THESIS_LOADING",!0);try{const s=await w.cF.getList(t);if(s.success){const{list:t,total:a}=s.data;return e("SET_THESIS_LIST",{list:t,total:a}),{success:!0,data:s.data}}return{success:!1,message:s.message}}catch(s){return{success:!1,message:s.message||"获取论文列表失败"}}finally{e("SET_THESIS_LOADING",!1)}},async getThesisDetail({commit:e},t){try{const e=await w.cF.getDetail(t);return e.success?{success:!0,data:e.data}:{success:!1,message:e.message}}catch(s){return{success:!1,message:s.message||"获取论文详情失败"}}},async deleteThesis({commit:e},t){try{const e=await w.cF.delete(t);return e.success?{success:!0,message:e.message}:{success:!1,message:e.message}}catch(s){return{success:!1,message:s.message||"删除论文失败"}}},async getOverview({commit:e}){try{const t=await w.bk.getOverview();return t.success?(e("SET_OVERVIEW_DATA",t.data),{success:!0,data:t.data}):{success:!1,message:t.message}}catch(t){return{success:!1,message:t.message||"获取数据概览失败"}}},async getUserStats({commit:e},t){try{const s=await w.bk.getUserStats(t);return s.success?(e("SET_USER_STATS",s.data),{success:!0,data:s.data}):{success:!1,message:s.message}}catch(s){return{success:!1,message:s.message||"获取用户统计失败"}}},async getThesisStats({commit:e},t){try{const s=await w.bk.getThesisStats(t);return s.success?(e("SET_THESIS_STATS",s.data),{success:!0,data:s.data}):{success:!1,message:s.message}}catch(s){return{success:!1,message:s.message||"获取论文统计失败"}}},async getChatStats({commit:e},t){try{const s=await w.bk.getChatStats(t);return s.success?(e("SET_CHAT_STATS",s.data),{success:!0,data:s.data}):{success:!1,message:s.message}}catch(s){return{success:!1,message:s.message||"获取聊天统计失败"}}},async resetUserPassword({commit:e},{userId:t,newPassword:s}){try{const e=await w.BF.resetPassword(t,{new_password:s});return e.success?{success:!0,message:e.message}:{success:!1,message:e.message}}catch(a){return{success:!1,message:a.message||"重置密码失败"}}},async getSystemSettings({commit:e}){e("SET_SETTINGS_LOADING",!0);try{const t=await w.XN.getSettings();return t.success?(e("SET_SYSTEM_SETTINGS",t.data),{success:!0,data:t.data}):{success:!1,message:t.message}}catch(t){return t&&4001===t.code&&t.message&&t.message.includes("wallet")?(console.warn("钱包错误被忽略:",t.message),{success:!0,data:{}}):{success:!1,message:t.message||"获取系统设置失败"}}finally{e("SET_SETTINGS_LOADING",!1)}},async saveSystemSettings({commit:e},t){try{const s=await w.XN.saveSettings(t);return s.success?(e("SET_SYSTEM_SETTINGS",t),{success:!0,message:s.message}):{success:!1,message:s.message}}catch(s){return{success:!1,message:s.message||"保存系统设置失败"}}},async testApiConnection({commit:e},t){try{const e=await w.XN.testApiConnection(t);return e.success?{success:!0,message:e.message}:{success:!1,message:e.message}}catch(s){return{success:!1,message:s.message||"API连接测试失败"}}},async resetSystemSettings({commit:e}){try{const t=await w.XN.resetSettings();return t.success?(e("SET_SYSTEM_SETTINGS",null),{success:!0,message:t.message}):{success:!1,message:t.message}}catch(t){return{success:!1,message:t.message||"重置系统设置失败"}}},async getAccountList({commit:e},t){e("SET_ACCOUNT_LOADING",!0);try{const s=await w.jC.getList(t);if(s.success){const{list:t,total:a}=s.data;return e("SET_ACCOUNT_LIST",{list:t,total:a}),{success:!0,data:s.data}}return{success:!1,message:s.message}}catch(s){return{success:!1,message:s.message||"获取管理员列表失败"}}finally{e("SET_ACCOUNT_LOADING",!1)}},async getAccountDetail({commit:e},t){try{const e=await w.jC.getDetail(t);return e.success?{success:!0,data:e.data}:{success:!1,message:e.message}}catch(s){return{success:!1,message:s.message||"获取管理员详情失败"}}},async createAccount({commit:e},t){try{const e=await w.jC.create(t);return e.success?{success:!0,message:e.message,data:e.data}:{success:!1,message:e.message}}catch(s){return{success:!1,message:s.message||"创建管理员失败"}}},async updateAccount({commit:e},{adminId:t,data:s}){try{const e=await w.jC.update(t,s);return e.success?{success:!0,message:e.message}:{success:!1,message:e.message}}catch(a){return{success:!1,message:a.message||"更新管理员失败"}}},async deleteAccount({commit:e},t){try{const e=await w.jC.delete(t);return e.success?{success:!0,message:e.message}:{success:!1,message:e.message}}catch(s){return{success:!1,message:s.message||"删除管理员失败"}}},async resetAccountPassword({commit:e},{adminId:t,newPassword:s}){try{const e=await w.jC.resetPassword(t,{new_password:s});return e.success?{success:!0,message:e.message}:{success:!1,message:e.message}}catch(a){return{success:!1,message:a.message||"重置密码失败"}}}},k={isAdminLoggedIn:e=>!!e.adminToken,adminToken:e=>e.adminToken,adminInfo:e=>e.adminInfo,userList:e=>e.userList,userTotal:e=>e.userTotal,userLoading:e=>e.userLoading,thesisList:e=>e.thesisList,thesisTotal:e=>e.thesisTotal,thesisLoading:e=>e.thesisLoading,overviewData:e=>e.overviewData,userStats:e=>e.userStats,thesisStats:e=>e.thesisStats,chatStats:e=>e.chatStats,vipUserCount:e=>{if(!e.userList||0===e.userList.length)return 0;const t=new Date;return e.userList.filter((e=>e.vip_expire_at&&new Date(e.vip_expire_at)>t)).length},activeUserCount:e=>{if(!e.userList||0===e.userList.length)return 0;const t=new Date;return t.setDate(t.getDate()-30),e.userList.filter((e=>e.last_login_time&&new Date(e.last_login_time)>t)).length},lockedUserCount:e=>e.userList&&0!==e.userList.length?e.userList.filter((e=>e.is_lock)).length:0,systemSettings:e=>e.systemSettings,settingsLoading:e=>e.settingsLoading,accountList:e=>e.accountList,accountTotal:e=>e.accountTotal,accountLoading:e=>e.accountLoading},x={namespaced:!0,state:S,mutations:I,actions:A,getters:k};a["default"].use(r.Ay);const T=new r.Ay.Store({modules:{thesis:l,user:b,admin:x},getters:o}),L=T},3483:(e,t,s)=>{"use strict";e.exports=s.p+"img/user.e73fc9da.svg"},3530:(e,t,s)=>{"use strict";e.exports=s.p+"img/money.b6629736.svg"},3567:(e,t,s)=>{"use strict";e.exports=s.p+"img/dev.d50194e1.svg"},3688:(e,t,s)=>{"use strict";s.d(t,{A:()=>me});var a=s(5471),r=s(173),i=function(){var e=this,t=e._self._c;return t("div",{staticClass:"page-container"},[t("div",{staticClass:"page-body"},[t("div",{staticClass:"page-body-aside"},[t("Navbar")],1),t("div",{staticClass:"page-body-main"},[t("router-view")],1)])])},o=[],n=function(){var e=this,t=e._self._c;return t("section",{staticClass:"my-navbar"},[t("div",{staticClass:"my-top"},[t("div",{staticClass:"logo",on:{click:e.handlerHomePage}},[t("img",{staticClass:"sidebar-logo",attrs:{src:e.logo}}),t("span",[e._v(e._s(e.$t("menu.slogan")))])]),t("div",{staticClass:"menu"},e._l(e.navLinks,(function(s){return t("div",{key:s.path},[s.children?t("div",{staticClass:"menu-item parent-menu",class:{"has-active-child":e.hasActiveChild(s)},on:{click:function(t){return e.toggleSubMenu(s)}}},[t("i",{class:s.icon}),e._v(" "+e._s(s.title)+" "),t("i",{staticClass:"el-icon-arrow-down submenu-icon",class:{"is-active":e.openSubMenus.includes(s.path)}})]):e._e(),s.children&&e.openSubMenus.includes(s.path)?t("div",{staticClass:"submenu"},e._l(s.children,(function(s){return t("NavLink",{key:s.path,staticClass:"submenu-item",attrs:{index:s.path}},[t("i",{class:s.icon}),e._v(" "+e._s(s.title)+" ")])})),1):e._e(),s.children?e._e():t("NavLink",{staticClass:"menu-item",attrs:{index:s.path}},[t("i",{class:s.icon}),e._v(" "+e._s(s.title)+" ")])],1)})),0),t("div",{staticClass:"setting"},[t("div",{staticClass:"user-section"},[t("UserInfo")],1),t("div",{staticClass:"setting-item"},[t("i",{staticClass:"el-icon-message"}),e._v(" 语言: "+e._s(e.displayLanguage)+" ")]),e.setting.modelName?t("div",{staticClass:"setting-item"},[t("i",{staticClass:"el-icon-cpu"}),e._v(" 当前模型: "+e._s(e.setting.modelName)+" ")]):e._e()])])])},c=[],l=s(2806),u=function(){var e=this,t=e._self._c;return t("div",{class:["menu-item",e.isSelf?"selected":""],attrs:{index:e.index},on:{click:e.handlerPage}},[e._t("default")],2)},m=[];const d={props:{index:{type:String,default:""}},data(){return{isSelf:!1}},watch:{$route(e,t){e.path==this.index?this.isSelf=!0:this.isSelf=!1}},created(){this.$route.path==this.index&&(this.isSelf=!0)},methods:{handlerPage(){this.$router.push({path:this.index})}}},g=d;var p=s(1656),h=(0,p.A)(g,u,m,!1,null,"78327e0d",null);const v=h.exports;var f=function(){var e=this,t=e._self._c;return t("div",{staticClass:"user-info"},[e.isLoggedIn?t("el-dropdown",{attrs:{trigger:"click"},on:{command:e.handleCommand}},[t("div",{staticClass:"user-avatar"},[t("el-avatar",{attrs:{size:36,src:e.avatarSrc}},[t("img",{attrs:{src:s(2806),alt:"默认头像"}})]),t("div",{staticClass:"user-details"},[t("div",{staticClass:"username-row"},[t("span",{staticClass:"username",attrs:{title:e.displayName}},[e._v(e._s(e.displayName))]),e.isVip?t("div",{staticClass:"vip-badge"},[t("i",{staticClass:"el-icon-star-on"}),t("span",[e._v("VIP")])]):t("div",{staticClass:"free-badge"},[t("i",{staticClass:"el-icon-star-off"}),t("span",[e._v("免费")])])]),e.isVip?t("div",{staticClass:"vip-status"},[e.userInfo.vip_balance_days?t("span",{staticClass:"vip-expire"},[e._v(" 剩余"+e._s(e.userInfo.vip_balance_days)+"天 ")]):e._e()]):t("div",{staticClass:"free-status"},[t("span",{staticClass:"upgrade-hint",on:{click:e.showUpgradeInfo}},[e._v("升级VIP")])])]),t("i",{staticClass:"el-icon-arrow-down el-icon--right"})],1),t("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[t("el-dropdown-item",{attrs:{command:"profile"}},[t("i",{staticClass:"el-icon-user"}),e._v(" 个人资料 ")]),e.isVip?t("el-dropdown-item",{attrs:{command:"vip-info"}},[t("i",{staticClass:"el-icon-star-on"}),e._v(" VIP权益 ")]):t("el-dropdown-item",{attrs:{command:"upgrade"}},[t("i",{staticClass:"el-icon-star-on"}),e._v(" 升级VIP ")]),t("el-dropdown-item",{attrs:{command:"logout",divided:""}},[t("i",{staticClass:"el-icon-switch-button"}),e._v(" 退出登录 ")])],1)],1):t("div",{staticClass:"login-actions"},[t("el-button",{staticClass:"login-btn",attrs:{type:"text"},on:{click:e.goToLogin}},[e._v("登录")]),t("el-button",{staticClass:"register-btn",attrs:{type:"primary",size:"small"},on:{click:e.goToRegister}},[e._v("注册")])],1)],1)},_=[],y=s(5353);const b={name:"UserInfo",computed:{...(0,y.L8)("user",["userInfo","isLoggedIn","username","nickname","isVip"]),displayName(){const e=this.nickname||this.username||"用户";return e.length>10?e.slice(0,10)+"…":e},avatarSrc(){return this.userInfo.headimg||null}},methods:{...(0,y.i0)("user",["logout"]),async handleCommand(e){switch(e){case"profile":this.goToProfile();break;case"vip-info":this.showVipInfo();break;case"upgrade":this.showUpgradeInfo();break;case"logout":await this.handleLogout();break}},goToProfile(){this.$router.push("/user/profile")},showVipInfo(){this.$alert(`\n        <div style="text-align: left;">\n          <h3 style="color: #E6A23C; margin-bottom: 15px;">🎉 VIP会员权益</h3>\n          <div style="margin-bottom: 10px;">\n            <strong>📝 论文生成：</strong>最多可生成5篇论文（免费用户仅1篇）\n          </div>\n          <div style="margin-bottom: 10px;">\n            <strong>🚀 生成速度：</strong>优先处理，更快的生成速度\n          </div>\n          <div style="margin-bottom: 10px;">\n            <strong>💎 高级功能：</strong>解锁所有高级AI功能\n          </div>\n          <div style="margin-bottom: 10px;">\n            <strong>🎯 专属服务：</strong>优先客服支持\n          </div>\n          <div style="color: #67C23A; font-weight: bold;">\n            剩余时间：${this.userInfo.vip_balance_days||"未知"}\n          </div>\n        </div>\n      `,"VIP会员权益",{dangerouslyUseHTMLString:!0,confirmButtonText:"知道了",type:"success"})},showUpgradeInfo(){this.$alert('\n        <div style="text-align: left;">\n          <h3 style="color: #E6A23C; margin-bottom: 15px;">🌟 升级VIP会员</h3>\n          <div style="margin-bottom: 10px;">\n            <strong>📝 论文数量：</strong>从1篇提升至5篇\n          </div>\n          <div style="margin-bottom: 10px;">\n            <strong>🚀 生成速度：</strong>享受优先处理\n          </div>\n          <div style="margin-bottom: 10px;">\n            <strong>💎 高级功能：</strong>解锁所有AI功能\n          </div>\n          <div style="margin-bottom: 10px;">\n            <strong>🎯 专属服务：</strong>优先客服支持\n          </div>\n          <div style="color: #F56C6C; font-weight: bold;">\n            立即升级，享受更多权益！\n          </div>\n        </div>\n      ',"升级VIP",{dangerouslyUseHTMLString:!0,confirmButtonText:"立即升级",cancelButtonText:"稍后再说",type:"warning"}).then((()=>{this.$message.info("升级功能开发中...")})).catch((()=>{}))},async handleLogout(){try{await this.$confirm("确定要退出登录吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await this.logout(),this.$message.success("已退出登录"),this.$router.push("/user/login")}catch(e){"cancel"!==e&&(console.error("退出登录失败:",e),this.$message.error("退出登录失败，请稍后重试"))}},goToLogin(){this.$router.push("/user/login")},goToRegister(){this.$router.push("/user/register")}}},w=b;var C=(0,p.A)(w,f,_,!1,null,"6c1de80d",null);const S=C.exports;s(8227);var I=s(6916);const A=[{category:I.A.t("menu.categoryPaper"),title:"程序公告",icon:"el-icon-bell",path:"/help/guide",desc:"激发你的灵感，从一个完美的标题开始！"},{category:I.A.t("menu.categoryPaper"),title:I.A.t("menu.smartInteraction"),icon:"el-icon-message",path:"/paper/chat",desc:"与AI助手交流，获取写作帮助和建议"},{category:I.A.t("menu.categoryPaper"),title:I.A.t("menu.paperGeneration"),icon:"el-icon-document",path:"/paper/title",desc:"一站式论文生成工具",children:[{title:I.A.t("menu.title"),icon:"el-icon-edit-outline",path:"/paper/title",desc:"激发你的灵感，从一个完美的标题开始！"},{title:I.A.t("menu.outline"),icon:"el-icon-notebook-2",path:"/paper/outline",desc:"打造你的论文结构，迈向成功的第一步！"},{title:I.A.t("menu.content"),icon:"el-icon-tickets",path:"/paper/content",desc:"释放你的创造力，写出令人惊叹的内容！"}]},{category:I.A.t("menu.categoryPaper"),title:I.A.t("menu.reportGeneration"),icon:"el-icon-s-order",path:"/report/index",desc:"快速生成各类工作报告"},{category:I.A.t("menu.categoryPaper"),title:I.A.t("menu.aicgApp"),icon:"el-icon-monitor",path:"/aicg/index",desc:"提供一站式AI服务"},{category:I.A.t("menu.categoryPaper"),title:"使用教程",icon:"el-icon-reading",path:"/help/guide",desc:"释放你的创造力，写出令人惊叹的内容！"}];var k=s(663);const x={data(){return{logo:l,navLinks:A,setting:{},currentLang:"",openSubMenus:[]}},components:{NavLink:v,UserInfo:S},created(){this.handleGetSetting(),this.currentLang=I.A.locale,this.initOpenSubMenus()},computed:{displayLanguage(){return"zh"===this.currentLang?"中文":"英文"}},methods:{hasActiveChild(e){return!!e.children&&e.children.some((e=>this.$route.path===e.path))},initOpenSubMenus(){const e=this.$route.path;this.navLinks.forEach((t=>{if(t.children){const s=t.children.some((t=>t.path===e));s&&!this.openSubMenus.includes(t.path)&&this.openSubMenus.push(t.path)}}))},toggleSubMenu(e){const t=this.openSubMenus.indexOf(e.path);t>-1?this.openSubMenus.splice(t,1):this.openSubMenus.push(e.path)},handleGetSetting(){(0,k.P)({}).then((e=>{this.setting={uiLang:e.data.uiLang,modelName:e.data.modelName},this.currentLang=e.data.uiLang||"zh"}))},handlerHomePage(){this.$router.push({path:"/"})}},watch:{$route(e){this.navLinks.forEach((t=>{if(t.children){const s=t.children.some((t=>t.path===e.path)),a=this.openSubMenus.includes(t.path);s&&!a&&this.openSubMenus.push(t.path)}}))}}},T=x;var L=(0,p.A)(T,n,c,!1,null,null,null);const P=L.exports;var E=function(){var e=this;e._self._c;return e._m(0)},$=[function(){var e=this,t=e._self._c;return t("div",{staticClass:"footer"},[t("a",{attrs:{href:""}},[e._v("关于我们")]),t("a",{attrs:{href:""}},[e._v("业务联系")])])}],F={},O=(0,p.A)(F,E,$,!1,null,null,null);const N=O.exports;var D=function(){var e=this,t=e._self._c;return!e.isProductionEnv&&e.isShow?t("div",{staticClass:"debug"},[e._l(e.debugInfo,(function(s,a){return t("div",{key:a},[t("el-tag",[e._v(e._s(a)+":"+e._s(s))])],1)})),t("el-button",{attrs:{small:""},on:{click:function(t){e.isShow=!1}}},[e._v("关闭debug")])],2):e._e()},U=[];const R={name:"Debug",data(){return{isShow:!0}},computed:{...(0,y.L8)(["user","token","device"]),debugInfo(){return{user:this.user,token:this.token,device:this.device,env:"production"}},isProductionEnv(){return!0}},created(){console.log(this.user),console.log(this.debugInfo)}},V=R;var M=(0,p.A)(V,D,U,!1,null,"2c926414",null);const j=M.exports,q={components:{Navbar:P,Footer:N,Debug:j},computed:{},methods:{handlerCloseWindow(){}}},B=q;var z=(0,p.A)(B,i,o,!1,null,"f40c8344",null);const G=z.exports;var H=function(){var e=this,t=e._self._c;return t("div",{staticClass:"admin-layout"},[t("el-aside",{staticClass:"sidebar",attrs:{width:e.isCollapse?"64px":"240px"}},[t("div",{staticClass:"sidebar-header"},[t("img",{staticClass:"logo",attrs:{src:s(2806),alt:"Logo"}}),t("span",{directives:[{name:"show",rawName:"v-show",value:!e.isCollapse,expression:"!isCollapse"}],staticClass:"title"},[e._v("早鸟论文管理")])]),t("el-menu",{staticClass:"sidebar-menu",attrs:{"default-active":e.$route.path,collapse:e.isCollapse,"unique-opened":!0,"background-color":"#001529","text-color":"#a6adb4","active-text-color":"#1890ff",router:""}},[t("el-menu-item",{staticClass:"menu-item",attrs:{index:"/admin/dashboard"}},[t("i",{staticClass:"el-icon-s-home"}),t("span",{attrs:{slot:"title"},slot:"title"},[e._v("数据概览")])]),t("el-submenu",{staticClass:"submenu",attrs:{index:"user"}},[t("template",{slot:"title"},[t("i",{staticClass:"el-icon-user"}),t("span",[e._v("用户管理")])]),t("el-menu-item",{staticClass:"submenu-item",attrs:{index:"/admin/users"}},[e._v("用户列表")]),e.isSuperAdmin?t("el-menu-item",{staticClass:"submenu-item",attrs:{index:"/admin/users/accounts"}},[e._v("账号管理")]):e._e()],2),t("el-submenu",{staticClass:"submenu",attrs:{index:"thesis"}},[t("template",{slot:"title"},[t("i",{staticClass:"el-icon-document"}),t("span",[e._v("论文管理")])]),t("el-menu-item",{staticClass:"submenu-item",attrs:{index:"/admin/thesis/list"}},[e._v("论文列表")])],2),t("el-submenu",{staticClass:"submenu",attrs:{index:"stats"}},[t("template",{slot:"title"},[t("i",{staticClass:"el-icon-s-data"}),t("span",[e._v("系统统计")])]),t("el-menu-item",{staticClass:"submenu-item",attrs:{index:"/admin/stats/overview"}},[e._v("数据概览")]),t("el-menu-item",{staticClass:"submenu-item",attrs:{index:"/admin/stats/users"}},[e._v("用户统计")]),t("el-menu-item",{staticClass:"submenu-item",attrs:{index:"/admin/stats/thesis"}},[e._v("论文统计")]),t("el-menu-item",{staticClass:"submenu-item",attrs:{index:"/admin/stats/chat"}},[e._v("聊天统计")])],2),t("el-submenu",{staticClass:"submenu",attrs:{index:"settings"}},[t("template",{slot:"title"},[t("i",{staticClass:"el-icon-setting"}),t("span",[e._v("系统设置")])]),t("el-menu-item",{attrs:{index:"/admin/settings"}},[e._v("基础设置")]),t("el-menu-item",{attrs:{index:"/admin/settings/payment"}},[e._v("支付配置")]),t("el-menu-item",{attrs:{index:"/admin/settings/model"}},[e._v("大模型配置")]),t("el-menu-item",{attrs:{index:"/admin/settings/orders"}},[e._v("订单管理")])],2)],1)],1),t("el-container",{staticClass:"main-container"},[t("el-header",{staticClass:"header"},[t("div",{staticClass:"header-left"},[t("el-button",{staticClass:"collapse-btn",attrs:{type:"text"},on:{click:e.toggleCollapse}},[t("i",{class:e.isCollapse?"el-icon-s-unfold":"el-icon-s-fold"})]),t("el-breadcrumb",{staticClass:"breadcrumb",attrs:{separator:"/"}},[t("el-breadcrumb-item",{attrs:{to:{path:"/admin/dashboard"}}},[e._v("首页")]),e.$route.meta.title?t("el-breadcrumb-item",[e._v(e._s(e.$route.meta.title))]):e._e()],1)],1),t("div",{staticClass:"header-right"},[t("el-dropdown",{attrs:{trigger:"click"},on:{command:e.handleCommand}},[t("div",{staticClass:"user-info"},[t("el-avatar",{attrs:{size:"small",icon:"el-icon-user"}}),t("span",{staticClass:"username"},[e._v("管理员")]),t("i",{staticClass:"el-icon-arrow-down el-icon--right"})],1),t("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[t("el-dropdown-item",{attrs:{command:"profile"}},[t("i",{staticClass:"el-icon-user"}),e._v(" 个人资料 ")]),t("el-dropdown-item",{attrs:{command:"logout",divided:""}},[t("i",{staticClass:"el-icon-switch-button"}),e._v(" 退出登录 ")])],1)],1)],1)]),t("el-main",{staticClass:"main-content"},[t("router-view")],1)],1),t("el-dialog",{attrs:{title:"修改密码",visible:e.passwordDialogVisible,width:"400px"},on:{"update:visible":function(t){e.passwordDialogVisible=t},close:e.resetPasswordForm}},[t("el-form",{ref:"passwordForm",attrs:{model:e.passwordForm,rules:e.passwordRules,"label-width":"100px"}},[t("el-form-item",{attrs:{label:"旧密码",prop:"old_password"}},[t("el-input",{attrs:{type:"password","show-password":"",placeholder:"请输入旧密码"},model:{value:e.passwordForm.old_password,callback:function(t){e.$set(e.passwordForm,"old_password",t)},expression:"passwordForm.old_password"}})],1),t("el-form-item",{attrs:{label:"新密码",prop:"new_password"}},[t("el-input",{attrs:{type:"password","show-password":"",placeholder:"请输入新密码"},model:{value:e.passwordForm.new_password,callback:function(t){e.$set(e.passwordForm,"new_password",t)},expression:"passwordForm.new_password"}})],1)],1),t("div",{attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.passwordDialogVisible=!1}}},[e._v("取消")]),t("el-button",{attrs:{type:"primary",loading:e.passwordLoading},on:{click:e.handleChangePassword}},[e._v(" 确认修改 ")])],1)],1)],1)},W=[],K=s(8987);const J={name:"AdminLayout",data(){return{isCollapse:!1,passwordDialogVisible:!1,passwordLoading:!1,passwordForm:{old_password:"",new_password:""},passwordRules:{old_password:[{required:!0,message:"请输入旧密码",trigger:"blur"}],new_password:[{required:!0,message:"请输入新密码",trigger:"blur"},{min:6,message:"密码长度不能少于6位",trigger:"blur"}]}}},computed:{...(0,y.L8)("admin",["adminInfo"]),currentPage(){const e={"/admin/dashboard":"数据概览","/admin/users":"用户管理","/admin/users/stats":"用户统计","/admin/users/accounts":"账号管理","/admin/users/accounts/create":"创建账号","/admin/users/accounts/edit":"编辑账号","/admin/thesis":"论文管理","/admin/thesis/stats":"论文统计","/admin/stats/overview":"数据概览","/admin/stats/users":"用户统计","/admin/stats/thesis":"论文统计","/admin/stats/chat":"聊天统计","/admin/settings":"系统设置"};return e[this.$route.path]||""},isSuperAdmin(){return this.adminInfo&&this.adminInfo.is_superadmin}},mounted(){this.getAdminProfile()},methods:{...(0,y.i0)("admin",["adminLogout","changePassword","getAdminProfile"]),toggleSidebar(){this.isCollapse=!this.isCollapse},async handleCommand(e){switch(e){case"profile":this.$message.info("个人资料功能开发中...");break;case"logout":await this.handleLogout();break}},async handleLogout(){try{await this.$confirm("确定要退出登录吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const t=this.$loading({lock:!0,text:"正在退出...",spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"});try{const e=this.$store.getters["admin/adminToken"]||K.A.get("admin_token")||localStorage.getItem("admin_token");e&&(this.$axios.defaults.headers.common["Authorization"]=`Bearer ${e}`);const t=await this.adminLogout();this.$message.success(t.message||"退出成功")}catch(e){console.error("退出时发生错误:",e),this.$message.info("已退出登录")}finally{t.close(),this.$router.push("/admin/login")}}catch(e){"cancel"!==e&&(console.error("退出失败:",e),this.$message.error("退出失败，请稍后重试"))}},async handleChangePassword(){try{const e=await this.$refs.passwordForm.validate();if(!e)return;this.passwordLoading=!0;const t=await this.changePassword(this.passwordForm);t.success?(this.$message.success(t.message||"密码修改成功"),this.passwordDialogVisible=!1,this.resetPasswordForm()):this.$message.error(t.message||"密码修改失败")}catch(e){console.error("修改密码失败:",e),this.$message.error("修改密码失败，请稍后重试")}finally{this.passwordLoading=!1}},resetPasswordForm(){this.passwordForm={old_password:"",new_password:""},this.$refs.passwordForm&&this.$refs.passwordForm.resetFields()},toggleCollapse(){this.isCollapse=!this.isCollapse}}},Y=J;var X=(0,p.A)(Y,H,W,!1,null,"3c71693d",null);const Q=X.exports;var Z=s(4981),ee=s(2495),te=s(3970),se=s(8418);a["default"].use(r.A);const ae=[{path:"/user/login",name:"UserLogin",component:Z["default"],meta:{title:"用户登录"}},{path:"/user/register",name:"UserRegister",component:ee["default"],meta:{title:"用户注册"}},{path:"/user/profile",name:"UserProfile",component:te["default"],meta:{title:"个人资料"}},{path:"/admin/login",name:"AdminLogin",component:se["default"],meta:{title:"管理员登录"}},{path:"/admin",component:Q,redirect:"/admin/dashboard",children:[{path:"dashboard",name:"AdminDashboard",component:()=>s.e(318).then(s.bind(s,9318)),meta:{title:"数据概览",requiresAuth:!0}},{path:"users",name:"AdminUsers",component:()=>Promise.all([s.e(22),s.e(4)]).then(s.bind(s,8022)),meta:{title:"用户管理",requiresAuth:!0}},{path:"users/accounts",name:"AdminAccountsList",component:()=>s.e(632).then(s.bind(s,6632)),meta:{title:"账号管理",requiresAuth:!0,superAdmin:!0}},{path:"users/accounts/create",name:"AdminAccountsCreate",component:()=>s.e(773).then(s.bind(s,6154)),meta:{title:"创建账号",requiresAuth:!0,superAdmin:!0,hidden:!0}},{path:"users/accounts/edit/:id",name:"AdminAccountsEdit",component:()=>s.e(773).then(s.bind(s,6154)),meta:{title:"编辑账号",requiresAuth:!0,superAdmin:!0,hidden:!0}},{path:"thesis",name:"AdminThesis",component:()=>Promise.all([s.e(897),s.e(739)]).then(s.bind(s,897)),meta:{title:"论文管理",requiresAuth:!0}},{path:"thesis/list",name:"AdminThesisList",component:()=>Promise.all([s.e(897),s.e(739)]).then(s.bind(s,897)),meta:{title:"论文列表",requiresAuth:!0,parent:"AdminThesis"}},{path:"settings/orders",name:"AdminPaymentOrders",component:()=>s.e(595).then(s.bind(s,7595)),meta:{title:"订单管理",requiresAuth:!0,parent:"AdminSettings"}},{path:"thesis/order/:id",name:"AdminPaymentOrderDetail",component:()=>s.e(503).then(s.bind(s,9503)),meta:{title:"订单详情",requiresAuth:!0}},{path:"stats/overview",name:"AdminStatsOverview",component:()=>s.e(344).then(s.bind(s,5344)),meta:{title:"数据概览",requiresAuth:!0}},{path:"stats/users",name:"AdminStatsUsers",component:()=>s.e(795).then(s.bind(s,4795)),meta:{title:"用户统计",requiresAuth:!0}},{path:"stats/thesis",name:"AdminStatsThesis",component:()=>s.e(345).then(s.bind(s,345)),meta:{title:"论文统计",requiresAuth:!0}},{path:"stats/chat",name:"AdminStatsChat",component:()=>s.e(917).then(s.bind(s,3917)),meta:{title:"聊天统计",requiresAuth:!0}},{path:"settings",name:"AdminSettings",component:()=>s.e(586).then(s.bind(s,7586)),meta:{title:"系统设置",requiresAuth:!0}},{path:"settings/payment",name:"AdminPaymentConfig",component:()=>s.e(12).then(s.bind(s,12)),meta:{title:"支付配置",requiresAuth:!0}},{path:"settings/model",name:"AdminModelConfig",component:()=>s.e(8).then(s.bind(s,5008)),meta:{title:"大模型配置",requiresAuth:!0}}]}],re=[];function ie(e){e.forEach((e=>{e.path&&re.push({path:e.path,component:t=>Promise.all([s.e(22),s.e(897),s.e(389)]).then(function(){var a=[s(2366)("./views"+e.path)];t.apply(null,a)}.bind(this))["catch"](s.oe)}),e.children&&e.children.length>0&&ie(e.children)}))}ie(A),ae.push({path:"/",component:G,redirect:"/help/guide",children:re});const oe=new r.A({mode:"history",scrollBehavior:()=>({y:0}),routes:ae});var ne=s(3465),ce=s(1052);const le=["/help/guide","/paper/help","/aicg/index","/user/login","/user/register","/admin/login"];let ue=!1;oe.beforeEach((async(e,t,s)=>{if(le.some((t=>e.path.startsWith(t))))s();else if(e.path.startsWith("/admin"))try{const t=ne.A.getters["admin/isAdminLoggedIn"];t?s():"/admin/login"!==e.path?(ce.Message.warning("请先登录管理员账号"),s("/admin/login")):s()}catch(a){console.error("管理员路由守卫检查失败:",a),"/admin/login"!==e.path?(ce.Message.error("检查管理员状态失败，请重新登录"),s("/admin/login")):s()}else try{if(!ue){console.log("🔄 正在初始化用户状态...");const e=await ne.A.dispatch("user/restoreUserState");ue=!0,e?console.log("✅ 用户状态初始化完成"):console.log("⚠️ 用户状态初始化失败，用户未登录")}const t=ne.A.getters["user/isLoggedIn"];t?s():"/user/login"!==e.path?(ce.Message.warning("请先登录"),s("/user/login")):s()}catch(a){console.error("路由守卫检查用户状态失败:",a);try{await ne.A.dispatch("user/clearAllCache")}catch(r){console.error("清除缓存失败:",r)}"/user/login"!==e.path?(ce.Message.error("检查用户状态失败，请重新登录"),s("/user/login")):s()}}));const me=oe},3723:(e,t,s)=>{"use strict";e.exports=s.p+"img/tools.7b04c850.svg"},3773:(e,t,s)=>{"use strict";e.exports=s.p+"img/system.86e682ae.svg"},3937:(e,t,s)=>{"use strict";e.exports=s.p+"img/password.ab492b57.svg"},3970:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>m});var a=function(){var e=this,t=e._self._c;return t("div",{staticClass:"profile-container"},[t("el-tabs",{attrs:{type:"card"},model:{value:e.activeTab,callback:function(t){e.activeTab=t},expression:"activeTab"}},[t("el-tab-pane",{attrs:{label:"个人资料",name:"profile"}},[t("el-card",{staticClass:"profile-card"},[t("div",{staticClass:"card-header",attrs:{slot:"header"},slot:"header"},[t("span",[e._v("个人资料")])]),t("el-form",{ref:"profileForm",staticClass:"profile-form",attrs:{model:e.profileForm,rules:e.profileRules,"label-width":"100px"}},[t("el-form-item",{attrs:{label:"用户名",prop:"username"}},[t("el-input",{attrs:{disabled:""},model:{value:e.profileForm.username,callback:function(t){e.$set(e.profileForm,"username",t)},expression:"profileForm.username"}})],1),t("el-form-item",{attrs:{label:"昵称",prop:"nickname"}},[t("el-input",{attrs:{placeholder:"请输入昵称"},model:{value:e.profileForm.nickname,callback:function(t){e.$set(e.profileForm,"nickname",t)},expression:"profileForm.nickname"}})],1),t("el-form-item",{attrs:{label:"邮箱",prop:"email"}},[t("el-input",{attrs:{placeholder:"请输入邮箱"},model:{value:e.profileForm.email,callback:function(t){e.$set(e.profileForm,"email",t)},expression:"profileForm.email"}})],1),t("el-form-item",{attrs:{label:"用户类型"}},[t("div",{staticClass:"vip-status-display"},[t("el-tag",{attrs:{type:e.userInfo.isVip?"success":"info",size:"medium"}},[t("i",{class:e.userInfo.isVip?"el-icon-star-on":"el-icon-star-off"}),e._v(" "+e._s(e.userInfo.isVip?"VIP用户":"普通用户")+" ")]),e.userInfo.isVip?t("div",{staticClass:"vip-details"},[t("div",{staticClass:"vip-level"},[t("span",{staticClass:"level-label"},[e._v("VIP等级：")]),t("span",{staticClass:"level-value"},[e._v(e._s(e.userInfo.vip_level||1)+"级")])]),e.userInfo.vip_balance_days?t("div",{staticClass:"vip-expire"},[t("span",{staticClass:"expire-label"},[e._v("剩余时间：")]),t("span",{staticClass:"expire-value"},[e._v(e._s(e.userInfo.vip_balance_days))])]):e._e(),e.userInfo.vip_expire_at?t("div",{staticClass:"vip-date"},[t("span",{staticClass:"date-label"},[e._v("到期时间：")]),t("span",{staticClass:"date-value"},[e._v(e._s(e.userInfo.vip_expire_at))])]):e._e()]):t("div",{staticClass:"free-details"},[t("div",{staticClass:"upgrade-prompt"},[t("span",{staticClass:"prompt-text"},[e._v("升级VIP享受更多权益")]),t("el-button",{attrs:{type:"warning",size:"mini"},on:{click:function(t){e.activeTab="vip"}}},[e._v(" 立即升级 ")])],1)])],1)]),t("el-form-item",{attrs:{label:"注册时间"}},[t("span",[e._v(e._s(e.formatDate(e.userInfo.create_time)))])]),t("el-form-item",{attrs:{label:"最后登录"}},[t("span",[e._v(e._s(e.formatDate(e.userInfo.last_login_time)))])]),t("el-form-item",[t("el-button",{attrs:{type:"primary",loading:e.loading},on:{click:e.handleUpdateProfile}},[e._v(" 更新资料 ")]),t("el-button",{on:{click:e.goBack}},[e._v("返回")])],1)],1)],1)],1),t("el-tab-pane",{attrs:{label:"会员购买",name:"vip"}},[t("el-card",{staticClass:"vip-status-card"},[t("div",{staticClass:"card-header",attrs:{slot:"header"},slot:"header"},[t("span",[e._v("会员状态")])]),t("div",{staticClass:"vip-status-content"},[t("div",{staticClass:"vip-status-info"},[t("div",{staticClass:"vip-icon"},[t("i",{class:e.userInfo.isVip?"el-icon-star-on":"el-icon-star-off",style:{color:e.userInfo.isVip?"#E6A23C":"#909399",fontSize:"48px"}})]),t("div",{staticClass:"vip-text"},[t("h3",[e._v(e._s(e.userInfo.isVip?"VIP会员":"普通用户"))]),e.userInfo.isVip?t("p",[e._v(" VIP等级: "+e._s(e.userInfo.vip_level||1)+"级 | 到期时间: "+e._s(e.userInfo.vip_expire_at||"未知")+" | 剩余: "+e._s(e.userInfo.vip_balance_days||"未知")+" ")]):t("p",[e._v("升级VIP会员，享受更多权益")])])])])]),t("el-card",{staticClass:"vip-packages-card"},[t("div",{staticClass:"card-header",attrs:{slot:"header"},slot:"header"},[t("span",[e._v("会员套餐")]),t("el-button",{staticStyle:{float:"right",padding:"3px 0"},attrs:{type:"text"},on:{click:e.refreshProducts}},[e._v(" 刷新 ")])],1),t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.productsLoading,expression:"productsLoading"}],staticClass:"packages-container"},[t("el-row",{attrs:{gutter:20}},e._l(e.products,(function(s){return t("el-col",{key:s.id,attrs:{span:8}},[t("div",{staticClass:"package-item",class:{"package-active":e.userInfo.isVip&&e.userInfo.vip_level>=s.vip_level}},[t("div",{staticClass:"package-header"},[t("h3",[e._v(e._s(s.name))]),t("div",{staticClass:"package-price"},[t("span",{staticClass:"price"},[e._v("¥"+e._s(s.discount_price||s.price))]),s.discount_price?t("span",{staticClass:"original-price"},[e._v("¥"+e._s(s.price))]):e._e()])]),t("div",{staticClass:"package-body"},[t("p",[e._v(e._s(s.description||`${s.vip_days}天VIP会员`))]),t("p",[e._v("VIP等级: "+e._s(s.vip_level||1))]),t("p",[e._v("会员天数: "+e._s(s.vip_days)+"天")])]),t("div",{staticClass:"package-footer"},[t("el-button",{attrs:{type:"primary",disabled:e.userInfo.isVip&&e.userInfo.vip_level>=s.vip_level},on:{click:function(t){return e.buyProduct(s)}}},[e._v(" "+e._s(e.userInfo.isVip&&e.userInfo.vip_level>=s.vip_level?"已开通":"立即购买")+" ")])],1)])])})),1),0===e.products.length?t("div",{staticClass:"empty-products"},[t("el-empty",{attrs:{description:"暂无可购买的会员套餐"}})],1):e._e()],1)]),t("el-card",{staticClass:"vip-comparison-card"},[t("div",{staticClass:"comparison-header",attrs:{slot:"header"},slot:"header"},[t("span",[e._v("VIP权益对比")])]),t("div",{staticClass:"comparison-table"},[t("div",{staticClass:"comparison-row header"},[t("div",{staticClass:"feature"},[e._v("功能特性")]),t("div",{staticClass:"free-user"},[e._v("免费用户")]),t("div",{staticClass:"vip-user"},[e._v("VIP用户")])]),t("div",{staticClass:"comparison-row"},[t("div",{staticClass:"feature"},[e._v("论文生成数量")]),t("div",{staticClass:"free-user"},[t("i",{staticClass:"el-icon-close"}),t("span",[e._v("1篇")])]),t("div",{staticClass:"vip-user"},[t("i",{staticClass:"el-icon-check"}),t("span",[e._v("5篇")])])]),t("div",{staticClass:"comparison-row"},[t("div",{staticClass:"feature"},[e._v("生成速度")]),t("div",{staticClass:"free-user"},[t("i",{staticClass:"el-icon-close"}),t("span",[e._v("普通")])]),t("div",{staticClass:"vip-user"},[t("i",{staticClass:"el-icon-check"}),t("span",[e._v("优先")])])]),t("div",{staticClass:"comparison-row"},[t("div",{staticClass:"feature"},[e._v("高级AI功能")]),t("div",{staticClass:"free-user"},[t("i",{staticClass:"el-icon-close"}),t("span",[e._v("部分")])]),t("div",{staticClass:"vip-user"},[t("i",{staticClass:"el-icon-check"}),t("span",[e._v("全部")])])]),t("div",{staticClass:"comparison-row"},[t("div",{staticClass:"feature"},[e._v("客服支持")]),t("div",{staticClass:"free-user"},[t("i",{staticClass:"el-icon-close"}),t("span",[e._v("普通")])]),t("div",{staticClass:"vip-user"},[t("i",{staticClass:"el-icon-check"}),t("span",[e._v("优先")])])]),t("div",{staticClass:"comparison-row"},[t("div",{staticClass:"feature"},[e._v("数据导出")]),t("div",{staticClass:"free-user"},[t("i",{staticClass:"el-icon-close"}),t("span",[e._v("基础")])]),t("div",{staticClass:"vip-user"},[t("i",{staticClass:"el-icon-check"}),t("span",[e._v("完整")])])])])])],1),t("el-tab-pane",{attrs:{label:"订单记录",name:"orders"}},[t("el-card",{staticClass:"orders-card"},[t("div",{staticClass:"card-header",attrs:{slot:"header"},slot:"header"},[t("span",[e._v("订单记录")]),t("el-button",{staticStyle:{float:"right",padding:"3px 0"},attrs:{type:"text"},on:{click:e.loadOrders}},[e._v(" 刷新 ")])],1),t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.ordersLoading,expression:"ordersLoading"}],staticClass:"orders-content"},[t("el-table",{staticStyle:{width:"100%"},attrs:{data:e.orders,border:""}},[t("el-table-column",{attrs:{prop:"out_trade_no",label:"订单号",width:"180"}}),t("el-table-column",{attrs:{prop:"product.name",label:"商品名称",width:"150"}}),t("el-table-column",{attrs:{prop:"amount",label:"金额",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" ¥"+e._s(t.row.amount)+" ")]}}])}),t("el-table-column",{attrs:{prop:"status",label:"状态",width:"100"},scopedSlots:e._u([{key:"default",fn:function(s){return[t("el-tag",{attrs:{type:1===s.row.status?"success":"info"}},[e._v(" "+e._s(1===s.row.status?"已支付":"未支付")+" ")])]}}])}),t("el-table-column",{attrs:{prop:"create_time",label:"创建时间",width:"180"}}),t("el-table-column",{attrs:{prop:"pay_time",label:"支付时间",width:"180"}}),t("el-table-column",{attrs:{label:"操作"},scopedSlots:e._u([{key:"default",fn:function(s){return[0===s.row.status?t("el-button",{attrs:{size:"mini"},on:{click:function(t){return e.checkOrder(s.row)}}},[e._v(" 查询 ")]):e._e(),0===s.row.status?t("el-button",{attrs:{size:"mini",type:"primary"},on:{click:function(t){return e.continuePayment(s.row)}}},[e._v(" 继续支付 ")]):e._e()]}}])})],1),0===e.orders.length?t("div",{staticClass:"empty-orders"},[t("el-empty",{attrs:{description:"暂无订单记录"}})],1):e._e()],1)])],1)],1),t("el-dialog",{attrs:{title:"订单支付",visible:e.paymentDialogVisible,width:"500px"},on:{"update:visible":function(t){e.paymentDialogVisible=t}}},[t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.paymentLoading,expression:"paymentLoading"}],staticClass:"payment-dialog"},[t("div",{staticClass:"payment-info"},[t("p",[t("strong",[e._v("商品名称：")]),e._v(e._s(e.currentOrder.product?e.currentOrder.product.name:""))]),t("p",[t("strong",[e._v("订单金额：")]),e._v("¥"+e._s(e.currentOrder.amount))]),t("p",[t("strong",[e._v("订单编号：")]),e._v(e._s(e.currentOrder.out_trade_no))])]),e.currentOrder.pay_url?t("div",{staticClass:"payment-qrcode"},[t("p",[e._v("请使用支付宝扫码支付")]),t("div",{staticClass:"qrcode-container"},[t("img",{attrs:{src:`https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(e.currentOrder.pay_url)}`,alt:"支付二维码"}})])]):e._e(),t("div",{staticClass:"payment-actions"},[t("el-button",{on:{click:function(t){e.paymentDialogVisible=!1}}},[e._v("关闭")]),t("el-button",{attrs:{type:"primary"},on:{click:e.openPayUrl}},[e._v("打开支付页面")]),t("el-button",{on:{click:e.checkPaymentStatus}},[e._v("检查支付状态")])],1)])])],1)},r=[],i=s(5353),o=s(5597);const n={name:"UserProfile",data(){return{activeTab:"profile",loading:!1,profileForm:{username:"",nickname:"",email:""},profileRules:{nickname:[{max:20,message:"昵称长度不能超过 20 个字符",trigger:"blur"}],email:[{pattern:/^[^\s@]+@[^\s@]+\.[^\s@]+$/,message:"请输入正确的邮箱格式",trigger:"blur"}]},products:[],productsLoading:!1,orders:[],ordersLoading:!1,paymentDialogVisible:!1,paymentLoading:!1,currentOrder:{}}},computed:{...(0,i.L8)("user",["userInfo"])},created(){this.initProfileForm(),this.loadProducts(),this.loadOrders()},methods:{initProfileForm(){this.userInfo&&(this.profileForm.username=this.userInfo.username||"",this.profileForm.nickname=this.userInfo.nickname||"",this.profileForm.email=this.userInfo.email||"")},formatDate(e){if(!e)return"未知";try{const t=new Date(e);return t.toLocaleString("zh-CN")}catch(t){return e}},async handleUpdateProfile(){try{const e=await this.$refs.profileForm.validate();if(!e)return;this.loading=!0,this.$message.success("个人资料更新功能开发中...")}catch(e){console.error("更新个人资料失败:",e),this.$message.error("更新失败，请稍后重试")}finally{this.loading=!1}},goBack(){this.$router.go(-1)},async loadProducts(){this.productsLoading=!0;try{const e=await(0,o.Ls)();0===e.code?this.products=e.data:this.$message.error(e.msg||"获取会员套餐失败")}catch(e){console.error("获取会员套餐失败",e),this.$message.error("获取会员套餐失败")}finally{this.productsLoading=!1}},refreshProducts(){this.loadProducts()},async buyProduct(e){try{const t=await(0,o.Lb)({user_id:this.userInfo.id,product_id:e.product_id,pay_type:"alipay"});0===t.code?(this.currentOrder=t.data,this.paymentDialogVisible=!0,this.loadOrders()):this.$message.error(t.msg||"创建订单失败")}catch(t){console.error("创建订单失败",t),this.$message.error("创建订单失败")}},async loadOrders(){this.ordersLoading=!0;try{this.orders=[],setTimeout((()=>{this.ordersLoading=!1}),500)}catch(e){console.error("获取订单记录失败",e),this.$message.error("获取订单记录失败"),this.ordersLoading=!1}},async checkOrder(e){try{const t=await(0,o.j6)({out_trade_no:e.out_trade_no});if(0===t.code){const s=this.orders.findIndex((t=>t.out_trade_no===e.out_trade_no));-1!==s&&(this.orders[s]=t.data),1===t.data.status?(this.$message.success("订单已支付"),this.$store.dispatch("user/getUserInfo")):this.$message.info("订单未支付")}else this.$message.error(t.msg||"查询订单失败")}catch(t){console.error("查询订单失败",t),this.$message.error("查询订单失败")}},continuePayment(e){this.currentOrder=e,this.paymentDialogVisible=!0},openPayUrl(){this.currentOrder.pay_url&&window.open(this.currentOrder.pay_url,"_blank")},async checkPaymentStatus(){this.paymentLoading=!0;try{const e=await(0,o.j6)({out_trade_no:this.currentOrder.out_trade_no});0===e.code?1===e.data.status?(this.$message.success("订单已支付"),this.paymentDialogVisible=!1,this.$store.dispatch("user/getUserInfo"),this.loadOrders()):this.$message.info("订单未支付"):this.$message.error(e.msg||"查询支付状态失败")}catch(e){console.error("查询支付状态失败",e),this.$message.error("查询支付状态失败")}finally{this.paymentLoading=!1}}}},c=n;var l=s(1656),u=(0,l.A)(c,a,r,!1,null,"5b4aa30e",null);const m=u.exports},4178:(e,t,s)=>{"use strict";e.exports=s.p+"img/download.7d96fe7b.svg"},4470:(e,t,s)=>{"use strict";e.exports=s.p+"img/swagger.d8038fe1.svg"},4594:(e,t,s)=>{"use strict";e.exports=s.p+"img/link.66aaa180.svg"},4981:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>u});var a=function(){var e=this,t=e._self._c;return t("div",{staticClass:"login-container"},[t("div",{staticClass:"login-box"},[e._m(0),t("el-form",{ref:"loginForm",staticClass:"login-form",attrs:{model:e.loginForm,rules:e.loginRules},nativeOn:{submit:function(t){return t.preventDefault(),e.handleLogin.apply(null,arguments)}}},[t("el-form-item",{attrs:{prop:"username"}},[t("el-input",{attrs:{placeholder:"请输入用户名","prefix-icon":"el-icon-user",size:"large",clearable:""},model:{value:e.loginForm.username,callback:function(t){e.$set(e.loginForm,"username",t)},expression:"loginForm.username"}})],1),t("el-form-item",{attrs:{prop:"password"}},[t("el-input",{attrs:{type:"password",placeholder:"请输入密码","prefix-icon":"el-icon-lock",size:"large","show-password":""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleLogin.apply(null,arguments)}},model:{value:e.loginForm.password,callback:function(t){e.$set(e.loginForm,"password",t)},expression:"loginForm.password"}})],1),t("el-form-item",[t("el-button",{staticClass:"login-button",attrs:{type:"primary",loading:e.loading,size:"large"},on:{click:e.handleLogin}},[e._v(" "+e._s(e.loading?"登录中...":"登录")+" ")])],1)],1),t("div",{staticClass:"login-footer"},[t("p",{staticClass:"register-link"},[e._v(" 还没有账号？ "),t("el-link",{attrs:{type:"primary"},on:{click:e.goToRegister}},[e._v("立即注册")])],1)])],1)])},r=[function(){var e=this,t=e._self._c;return t("div",{staticClass:"login-header"},[t("h2",{staticClass:"login-title"},[e._v("论文生成系统")]),t("p",{staticClass:"login-subtitle"},[e._v("欢迎使用AI论文生成工具")])])}],i=s(5353);const o={name:"UserLogin",data(){return{loading:!1,loginForm:{username:"",password:""},loginRules:{username:[{required:!0,message:"请输入用户名",trigger:"blur"},{min:3,max:20,message:"用户名长度在 3 到 20 个字符",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:6,max:20,message:"密码长度在 6 到 20 个字符",trigger:"blur"}]}}},methods:{...(0,i.i0)("user",["login"]),async handleLogin(){try{const e=await this.$refs.loginForm.validate();if(!e)return;this.loading=!0,await this.login(this.loginForm),this.$message.success("登录成功"),this.$router.push("/")}catch(e){console.error("登录失败:",e),this.$message.error(e.message||"登录失败，请稍后重试")}finally{this.loading=!1}},goToRegister(){this.$router.push("/user/register")},goToActivate(){this.$router.push("/paper/activate")}}},n=o;var c=s(1656),l=(0,c.A)(n,a,r,!1,null,"03563727",null);const u=l.exports},5196:(e,t,s)=>{"use strict";e.exports=s.p+"img/exit-fullscreen.7ef479d3.svg"},5545:(e,t,s)=>{"use strict";s.r(t),s.d(t,{cancelRequest:()=>x,default:()=>T});var a=s(4335),r=(s(1052),s(8987)),i=s(8227),o=s.n(i);const n=o().TokenKey;function c(){return r.A.get(n)}var l=s(6916),u=s(3465),m=s(3688);const d=l.A.messages[l.A.locale]["errorMsg"],g=["/api/user/login","/api/user/register","/api/home/<USER>","/api/home/<USER>","/api/help/","/api/guide/","/api/agreement/","/api/admin/auth/login","/api/admin/auth/logout","/api/generate/tableContent","/api/pay/"],p=["/api/admin/auth/logout","/api/user/logout"],h=a.A.create({baseURL:{NODE_ENV:"production",VUE_APP_ENV:"production",VUE_APP_SITE_TITLE:"神笔365 https://365d.ink",BASE_URL:"/"}.VUE_APP_BASE_API,withCredentials:!0,timeout:12e4,headers:{"Cache-Control":"no-cache",Pragma:"no-cache"}}),v=new Map,f=e=>{const{url:t,method:s,params:a,data:r}=e;return[t,s,JSON.stringify(a),JSON.stringify(r)].join("&")},_=e=>{const t=f(e);e.cancelToken=e.cancelToken||new a.A.CancelToken((e=>{v.has(t)||v.set(t,e)}))},y=e=>{const t=f(e);if(v.has(t)){const e=v.get(t);e("请求被取消"),v.delete(t)}};function b(e,t=!1,s=null){const a=e.method?e.method.toUpperCase():"UNKNOWN",r=e.url||"UNKNOWN URL",i=r.includes("/api/admin/"),o=i?"[管理员API]":"[用户API]";t?console.error(`${o} ${a} ${r} 请求失败:`,s):console.log(`${o} ${a} ${r} 请求发送`)}function w(e,t=!1,s=null){const a=e.config||{},r=a.method?a.method.toUpperCase():"UNKNOWN",i=a.url||"UNKNOWN URL",o=e.status||"UNKNOWN STATUS",n=i.includes("/api/admin/"),c=n?"[管理员API]":"[用户API]";t?console.error(`${c} ${r} ${i} 响应失败 (${o}):`,s):console.log(`${c} ${r} ${i} 响应成功 (${o})`)}function C(){return u.A.state.admin&&u.A.state.admin.adminToken||r.A.get("admin_token")||localStorage.getItem("admin_token")}function S(e){e&&(r.A.set("admin_token",e,{expires:7}),localStorage.setItem("admin_token",e),u.A.state.admin&&u.A.commit("admin/SET_TOKEN",e))}function I(e){return g.some((t=>e.includes(t)))}function A(e){return p.some((t=>e.includes(t)))}const k=async(e,t)=>{if(!e||!e.retry)return Promise.reject(t);if(e.__retryCount=e.__retryCount||0,e.__retryCount>=e.retry)return Promise.reject(t);if(e.__retryCount+=1,e.url&&e.url.includes("/api/admin")){console.log("尝试刷新管理员token...");const t=C();t&&(e.headers["Authorization"]=`Bearer ${t}`,console.log("已刷新管理员token"))}const s=new Promise((t=>{setTimeout((()=>{console.log(`重试请求 (${e.__retryCount}/${e.retry}): ${e.url}`),t()}),e.retryDelay||1e3)}));return s.then((()=>h(e)))};h.interceptors.request.use((e=>{const t=e.url.includes("/api/thesis/payDownload")||e.url.includes("/api/thesis/paymentStatus")||e.url.includes("/api/thesis/confirmPayment")||e.url.includes("/api/thesis/simulatePayment")||e.url.includes("/api/admin/wechat_pay_config/");t?console.log("支付API跳过请求取消机制:",e.url):e.cancelToken||(y(e),_(e));const s=e.url.includes("/api/admin/"),a=I(e.url);if(s){const t=C();t?(e.headers["Authorization"]=`Bearer ${t}`,S(t)):a||console.warn("未找到管理员Token，请求可能会失败:",e.url)}else{const t=c();t&&(e.headers["Authorization"]=`Bearer ${t}`)}return b(e),e}),(e=>(console.error("请求拦截器错误:",e),Promise.reject(e)))),h.interceptors.response.use((e=>{y(e.config),w(e);let t=e.data;if("string"===typeof t&&t.trim().startsWith("<!doctype html>")){console.warn("接收到HTML响应而不是JSON:",e.config.url);const t=e.config.url&&e.config.url.includes("/api/admin");return t?(console.error("管理员API认证失败，返回了HTML页面"),e.config.retry&&(!e.config.__retryCount||e.config.__retryCount<e.config.retry)?(console.log("尝试重试请求..."),k(e.config,new Error("接收到HTML响应而不是JSON"))):(u.A.dispatch("admin/adminLogout"),"/admin/login"!==m.A.currentRoute.path&&m.A.push("/admin/login"),Promise.reject(new Error("登录已过期，请重新登录")))):Promise.reject(new Error("服务器返回了无效的响应格式"))}if(t&&void 0!==t.success){if(!0===t.success)return t;{const s=t.message||d["unknown"];if(console.error("API返回错误:",s,t),401===t.code||403===t.code||s.includes("登录")||s.includes("认证")||s.includes("token")){console.log("检测到认证错误");const t=A(e.config.url);if(t)return console.log("跳过自动登出，避免无限循环:",e.config.url),Promise.reject(new Error(s));console.log("执行登出操作");const a=e.config.url&&e.config.url.includes("/api/admin");a?(u.A.dispatch("admin/adminLogout"),"/admin/login"!==m.A.currentRoute.path&&m.A.push("/admin/login")):(u.A.dispatch("user/logout"),"/login"!==m.A.currentRoute.path&&m.A.push("/login"))}return Promise.reject(new Error(s))}}if(t&&void 0!==t.code&&0!==t.code&&200!==t.code){if("需要支付"===t.message&&t.data&&t.data.need_payment)return console.log("检测到支付需求，保留完整响应数据"),t;const s=t.message||d["unknown"];if(console.error("API返回错误:",s,t),401===t.code||403===t.code||s.includes("登录")||s.includes("认证")||s.includes("token")){console.log("检测到认证错误（兼容模式）");const t=A(e.config.url);if(t)return console.log("跳过自动登出，避免无限循环:",e.config.url),Promise.reject(new Error(s));console.log("执行登出操作");const a=e.config.url&&e.config.url.includes("/api/admin");a?(u.A.dispatch("admin/adminLogout"),"/admin/login"!==m.A.currentRoute.path&&m.A.push("/admin/login")):(u.A.dispatch("user/logout"),"/login"!==m.A.currentRoute.path&&m.A.push("/login"))}return Promise.reject(new Error(s))}return t}),(e=>{if(e.config&&(y(e.config),w(e.response||{config:e.config},!0,e)),a.A.isCancel(e))return console.log("请求已取消:",e.message),Promise.reject(new Error("请求已取消"));if(!e.response)return e.request?(console.error("请求超时或网络错误:",e.request),Promise.reject(new Error(d["network"]))):(console.error("请求设置错误:",e.message),Promise.reject(e));{const{status:t,data:s}=e.response;if(s&&"需要支付"===s.message&&s.data&&s.data.need_payment)return console.log("检测到支付需求，保留完整响应数据"),s;switch(t){case 401:case 403:console.log("检测到认证错误");const t=e.config&&A(e.config.url);if(t)return console.log("跳过自动登出，避免无限循环:",e.config.url),Promise.reject(new Error(d["auth"]));console.log("执行登出操作");const a=e.config&&e.config.url&&e.config.url.includes("/api/admin");return a?(u.A.dispatch("admin/adminLogout"),"/admin/login"!==m.A.currentRoute.path&&m.A.push("/admin/login")):(u.A.dispatch("user/logout"),"/login"!==m.A.currentRoute.path&&m.A.push("/login")),Promise.reject(new Error(d["auth"]));case 404:return Promise.reject(new Error(d["notFound"]));case 500:return Promise.reject(new Error(d["serverError"]));default:return Promise.reject(new Error(s&&s.message?s.message:d["unknown"]))}}}));const x=e=>{for(const[t,s]of v.entries())t.includes(e)&&(s("手动取消请求"),v.delete(t))},T=h},5597:(e,t,s)=>{"use strict";s.d(t,{Ay:()=>y,Lb:()=>i,Ls:()=>r,Mh:()=>m,YN:()=>u,j6:()=>o,jp:()=>n});var a=s(5545);function r(){return(0,a["default"])({url:"/api/pay/products",method:"get"})}function i(e){return(0,a["default"])({url:"/api/pay/create",method:"post",data:e})}function o(e){return(0,a["default"])({url:"/api/pay/query",method:"post",data:e})}function n(){return(0,a["default"])({url:"/api/admin/wechat_pay_config/current",method:"get"})}function c(){return(0,a["default"])({url:"/api/admin/wechat_pay_config/current",method:"get",retry:0,timeout:1e4,cancelToken:void 0}).catch((e=>{if(console.error("获取微信支付配置失败:",e),e.response){const t=e.response.data;if("string"===typeof t&&t.includes("<!doctype html>"))return{success:!1,code:404,message:"API端点未找到，可能是路由配置问题",data:null}}return{success:!1,code:e.response?e.response.status:500,message:e.message||"获取微信支付配置失败，请稍后重试",data:null}}))}function l(e){return(0,a["default"])({url:"/api/admin/wechat_pay_config/current",method:"post",data:e,timeout:1e4,retry:0}).catch((e=>(console.error("保存微信支付配置失败:",e),{success:!1,code:e.response?e.response.status:500,message:e.message||"保存微信支付配置失败，请稍后重试",data:null})))}function u(e){return(0,a["default"])({url:"/api/admin/wechat_pay_config/update",method:"post",data:e})}function m(e){return e.id?(0,a["default"])({url:`/api/admin/wechat_pay_config/update/${e.id}`,method:"put",data:e,retry:1,retryDelay:1e3}).catch((e=>({success:!1,code:e.response?e.response.status:500,message:e.message||"更新微信支付配置失败，请稍后重试",data:null}))):(0,a["default"])({url:"/api/admin/wechat_pay_config/create",method:"post",data:e,retry:1,retryDelay:1e3}).catch((e=>({success:!1,code:e.response?e.response.status:500,message:e.message||"创建微信支付配置失败，请稍后重试",data:null})))}function d(e){return(0,a["default"])({url:"/api/admin/wechat_pay_config/products",method:"get",params:e,headers:{"Cache-Control":"no-cache",Pragma:"no-cache"},retry:1,retryDelay:1e3}).catch((e=>({success:!1,code:e.response?e.response.status:500,message:e.message||"获取支付商品列表失败，请稍后重试",data:{list:[],total:0}})))}function g(e){return(0,a["default"])({url:"/api/admin/wechat_pay_config/products",method:"post",data:e,retry:1,retryDelay:1e3}).catch((e=>({success:!1,code:e.response?e.response.status:500,message:e.message||"创建支付商品失败，请稍后重试",data:null})))}function p(e,t){return(0,a["default"])({url:`/api/admin/wechat_pay_config/products/${e}`,method:"put",data:t,retry:1,retryDelay:1e3}).catch((e=>({success:!1,code:e.response?e.response.status:500,message:e.message||"更新支付商品失败，请稍后重试",data:null})))}function h(e){return(0,a["default"])({url:`/api/admin/wechat_pay_config/products/${e}`,method:"delete",retry:1,retryDelay:1e3}).catch((e=>({success:!1,code:e.response?e.response.status:500,message:e.message||"删除支付商品失败，请稍后重试",data:null})))}function v(){return(0,a["default"])({url:"/api/admin/wechat_pay_config/test",method:"post",retry:1,retryDelay:1e3}).catch((e=>({success:!1,code:e.response?e.response.status:500,message:e.message||"测试支付接口连接失败，请稍后重试",data:null})))}function f(e){return(0,a["default"])({url:"/api/pay/test",method:"post",data:e,retry:1,retryDelay:1e3,timeout:3e4}).catch((e=>({success:!1,code:e.response?e.response.status:500,message:e.message||"测试微信支付接口连接失败，请稍后重试",data:null})))}const _={getWeChatPayConfig:c,saveWeChatPayConfig:l,getPaymentConfig:n,updatePaymentConfig:u,savePaymentConfig:m,getPaymentProducts:r,createPayment:i,queryPayment:o,getAdminPaymentProducts:d,createPaymentProduct:g,updatePaymentProduct:p,deletePaymentProduct:h,testPaymentConnection:v,testWeChatPayConnection:f},y=_},5682:(e,t,s)=>{"use strict";e.exports=s.p+"img/blog.0b5ed3f8.svg"},5889:(e,t,s)=>{"use strict";e.exports=s.p+"img/develop.3b1659cb.svg"},5977:(e,t,s)=>{"use strict";e.exports=s.p+"img/login.bc85cfb0.svg"},5979:(e,t,s)=>{"use strict";e.exports=s.p+"img/menu.7b20bbc3.svg"},6081:(e,t,s)=>{"use strict";e.exports=s.p+"img/mnt.90ebbd44.svg"},6234:(e,t,s)=>{"use strict";e.exports=s.p+"img/search.24ee517d.svg"},6486:(e,t,s)=>{"use strict";e.exports=s.p+"img/email.7a5cd380.svg"},6624:(e,t,s)=>{"use strict";e.exports=s.p+"img/weixin.4870b4a4.svg"},6726:(e,t,s)=>{"use strict";e.exports=s.p+"img/system1.87f1a598.svg"},6760:(e,t,s)=>{"use strict";e.exports=s.p+"img/qiniu.77c7a49a.svg"},6916:(e,t,s)=>{"use strict";s.d(t,{A:()=>y});var a=s(5471),r=s(4765);const i={newParaDialog:{title:"填写新段落内容",formContent:"段落内容",formTitle:"段落标题",btnContinue:"继续填写",btnCancel:"取消",btnSubmit:"提交",tipCancel:"内容尚未保存，确定要取消吗？"}},o={el:{pagination:{goto:"前往",pagesize:"条/页",total:"共 {total} 条",pageClassifier:"页",prev:"上一页",next:"下一页",first:"首页",last:"尾页",pager:"分页",sizes:"条/页"},table:{emptyText:"暂无数据",confirmFilter:"筛选",resetFilter:"重置",clearFilter:"全部",sumText:"合计"},select:{loading:"加载中",noMatch:"无匹配数据",noData:"无数据",placeholder:"请选择"},datepicker:{now:"此刻",today:"今天",cancel:"取消",clear:"清空",confirm:"确定",selectDate:"选择日期",selectTime:"选择时间",startDate:"开始日期",startTime:"开始时间",endDate:"结束日期",endTime:"结束时间",prevYear:"前一年",nextYear:"后一年",prevMonth:"上个月",nextMonth:"下个月",year:"年",month1:"1 月",month2:"2 月",month3:"3 月",month4:"4 月",month5:"5 月",month6:"6 月",month7:"7 月",month8:"8 月",month9:"9 月",month10:"10 月",month11:"11 月",month12:"12 月",weeks:{sun:"日",mon:"一",tue:"二",wed:"三",thu:"四",fri:"五",sat:"六"},months:{jan:"一月",feb:"二月",mar:"三月",apr:"四月",may:"五月",jun:"六月",jul:"七月",aug:"八月",sep:"九月",oct:"十月",nov:"十一月",dec:"十二月"}},dialog:{close:"关闭此对话框",confirm:"确定",cancel:"取消"},messagebox:{title:"提示",confirm:"确定",cancel:"取消",error:"输入的数据不合法!"},upload:{deleteTip:"按 delete 键可删除",delete:"删除",preview:"查看图片",continue:"继续上传"},popconfirm:{confirmButtonText:"确定",cancelButtonText:"取消"},colorpicker:{confirm:"确定",clear:"清空"},image:{error:"加载失败"}}},n={中学:"中学",大专:"大专",本科:"本科",硕士:"硕士",博士:"博士"},c={freeLimit1:"免费用户每两次AI请求之间需间隔1分钟",freeLimit2:"免费用户需要逐段手动生成内容",vipFeature1:"VIP用户无需等待",vipFeature2:"VIP用户可一键自动生成全文",features:"AI功能",featuresTitle:"题目生成",featuresOutline:"提纲生成",featuresContent:"内容生成",freeUser:"普通用户",freeUserFeatures:"可免费使用下列勾选功能",vipUser:"VIP用户",vipUserFeatures:"可使用下列完整功能",toBuy:"手机淘宝APP扫码购买VIP",tipPrice:"价格",tipFree:"免费"},l={titleRules:[{name:"论文题目AI自动生成",vip:!0,normal:!0},{name:"可选择任意学科",vip:!0,normal:!0},{name:"支持关键字自定义",vip:!0,normal:!0},{name:"中英双语题目自动翻译",vip:!0,normal:!0},{name:"单次最多推荐2个题目",vip:!0,normal:!0},{name:"单题目最多推荐2个关键字",vip:!0,normal:!0},{name:"单次最多推荐6个题目",vip:!0,normal:!1},{name:"单题目最多推荐10个关键字",vip:!0,normal:!1},{name:"生成的论文题目数据一键下载",vip:!0,normal:!1}],outlineRules:[{name:"论文提纲AI自动生成",normal:!0},{name:"支持选择任意学科",normal:!0},{name:"支持大专、本科水平论文提纲",normal:!0},{name:"支持全部学术水平论文提纲",normal:!1},{name:"生成最多6个一级章节的提纲",normal:!0},{name:"生成最多9个一级章节的提纲",normal:!1},{name:"论文提纲数据一键下载",normal:!1}],contentRules:[{name:"论文内容AI自动生成",normal:!0},{name:"自动生成摘要",normal:!1},{name:"单独段落由AI重新生成",normal:!0},{name:"全部段落由AI批量重新生成",normal:!1},{name:"段落标题可单独手动编辑",normal:!1},{name:"段落内容可单独手动编辑",normal:!0},{name:"可生成单段最长200字",normal:!0},{name:"可生成单段最长800字",normal:!1},{name:"可一键下载为word格式",normal:!1},{name:"可一键导出json格式数据",normal:!1}]},u={title:"让AI助你写好论文！高效、专业、便捷<br>早鸟论文，你的论文写作助手！",slogan:"三步完成论文，智能高效！",letsGo:"开始你的写作之旅",step1:{title:"智能选题",desc:"基于AI分析，推荐热门研究方向，生成专业的论文题目。"},step2:{title:"提纲规划",desc:"智能生成专业的论文框架，合理的章节结构让论文更有条理。"},step3:{title:"内容创作",desc:"AI辅助生成专业内容，支持实时修改和优化，让论文更加完善。"},step4:{title:"一键导出",desc:"自动排版，标准格式，直接导出即可使用的论文文档。"}},m={categoryPaper:"论文写作",categoryWork:"工作助手",categoryTutorial:"使用教程",myId:"我的ID:{id}",vipBalance:"还有{balanceDay}",homepage:"首页",postReading:"读后感",brainStorming:"头脑风暴",workSummary:"工作总结",weeklyReport:"周报",rewrite:"改写",polish:"润色",slogan:"AI论文生成只需三步",title:"智能选题",outline:"提纲拟定",content:"内容生成",paperGeneration:"论文生成",smartInteraction:"智能互动",aicgApp:"AICG应用",reportGeneration:"报告生成",dailyReport:"日报生成",weeklyReport:"周报生成",monthlyReport:"月报生成",summary:"总结生成",help:"使用教程",member:"升级VIP",userData:"用量统计",userInfo:"我的信息",loginTip:"登录体验全部功能",loginOut:"退出",advise:"意见建议",rate:"体验评分",tipFreeUser:"免费用户",tipVipUser:"VIP 用户",tipPrice:"产看价格",tipExpireAt:"到期",tipLogout:"已退出登录，欢迎再次使用",tipRemind:"提醒"},d={major:"研究专业",topic:"研究课题",lang:"写作语言",level:"学历",keyword:"关键字",btnSubmit:"推荐论文题目",title:"题目",btnSelectMajor:"点击选择学科",btnSelectTitle:"选择此标题",btnClose:"关闭",placeholderMajor:"请填写或选择学科",placeholderTopic:"请填写细分研究方向",tipSelectThis:"AI建议标题如下，你可以选择合适的标题，进一步生成提纲:",relativeKeyword:"相关关键词"},g={btnSubmit:"生成提纲",title:"论文标题",level:"对应学历",lang:"写作语言",length:"篇幅长度",btnSelectOutline:"选此提纲",btnReSubmit:"重新生成",btnExportWord:"导出为word文档"},p={title:"标题",lang:"语言",length:"篇幅",level:"水平",createTime:"时间",btnOutlineEditMode:"进入大纲精修模式",btnReturnTxtMode:"返回内容编辑模式",btnRunning:"AI引擎书写中，请稍等",btnSubmit:"一键全生成",btnDownload:"下载WORD文档",btnProgress:"可在右侧大纲查看书写进度",leftTitleThesisList:"我所有论文",leftTitleOutline:"论文大纲",notifyStartGeneratTask:"ai引擎将逐个段落，单独生成，可在提纲中查看进度"},h=["中文","英语","法语","俄语","日语","葡萄牙语"],v={needLogin:"需要登录后才能体验所有功能",serverError:"服务器请求失败"},f={paragraphBox:i,languageList:h,education:n,userRankPage:c,userRank:l,helpPage:u,menu:m,titlePage:d,outlinePage:g,contentPage:p,errorMsg:v,elementUI:o};s(8227);a["default"].use(r.A);const _=new r.A({locale:"zh",messages:{zh:{...f}}}),y=_},6945:(e,t,s)=>{"use strict";e.exports=s.p+"img/source.71aef7d4.svg"},6947:(e,t,s)=>{"use strict";e.exports=s.p+"img/server.571e9afc.svg"},6990:(e,t,s)=>{"use strict";e.exports=s.p+"img/international.f9d2289e.svg"},7169:(e,t,s)=>{"use strict";e.exports=s.p+"img/image.1364e63b.svg"},7190:(e,t,s)=>{"use strict";e.exports=s.p+"img/visits.1adfecde.svg"},7371:(e,t,s)=>{"use strict";e.exports=s.p+"img/nested.1abe6ee8.svg"},7410:(e,t,s)=>{"use strict";e.exports=s.p+"img/java.85086920.svg"},7480:(e,t,s)=>{"use strict";e.exports=s.p+"img/error.5902782b.svg"},7519:(e,t,s)=>{"use strict";e.exports=s.p+"img/app.406be878.svg"},7521:(e,t,s)=>{"use strict";e.exports=s.p+"img/task.2faf0dc8.svg"},7577:(e,t,s)=>{"use strict";e.exports=s.p+"img/markdown.0ad7e986.svg"},7867:(e,t,s)=>{"use strict";e.exports=s.p+"img/permission.1a829db7.svg"},7942:(e,t,s)=>{"use strict";e.exports=s.p+"img/date.dbcccf03.svg"},7945:(e,t,s)=>{"use strict";e.exports=s.p+"img/chain.46c8ee13.svg"},8021:(e,t,s)=>{"use strict";e.exports=s.p+"img/skill.70cf40e0.svg"},8055:(e,t,s)=>{"use strict";e.exports=s.p+"img/deploy.a91d7b9d.svg"},8202:(e,t,s)=>{var a={"./Steve-Jobs.svg":980,"./alipay.svg":1960,"./anq.svg":1048,"./app.svg":7519,"./backup.svg":9640,"./blog.svg":5682,"./car.svg":2672,"./chain.svg":7945,"./chart.svg":1534,"./codeConsole.svg":1576,"./dashboard.svg":9290,"./database.svg":3281,"./date.svg":7942,"./deploy.svg":8055,"./dept.svg":2255,"./dev.svg":3567,"./develop.svg":5889,"./dictionary.svg":8734,"./doc.svg":2992,"./download.svg":4178,"./driver.svg":8224,"./edit.svg":2878,"./education.svg":2956,"./email.svg":6486,"./error.svg":7480,"./exit-fullscreen.svg":5196,"./fullscreen.svg":2889,"./fwb.svg":1031,"./github.svg":119,"./gonggao.svg":3288,"./icon.svg":3414,"./image.svg":7169,"./index.svg":8270,"./international.svg":6990,"./ipvisits.svg":8993,"./java.svg":7410,"./link.svg":4594,"./list.svg":1718,"./lock.svg":2499,"./log.svg":116,"./login.svg":5977,"./markdown.svg":7577,"./menu.svg":5979,"./message.svg":3079,"./mnt.svg":6081,"./money.svg":3530,"./monitor.svg":8384,"./nested.svg":7371,"./oil.svg":1042,"./password.svg":3937,"./people.svg":1447,"./peoples.svg":2886,"./permission.svg":7867,"./phone.svg":2522,"./qiniu.svg":6760,"./redis.svg":2895,"./role.svg":1394,"./search.svg":6234,"./server.svg":6947,"./shopping.svg":9238,"./size.svg":3085,"./skill.svg":8021,"./source.svg":6945,"./sqlMonitor.svg":8956,"./swagger.svg":4470,"./sys-tools.svg":8695,"./system.svg":3773,"./system1.svg":6726,"./tab.svg":2055,"./task.svg":7521,"./theme.svg":3093,"./timing.svg":2144,"./tools.svg":3723,"./tree-table.svg":2949,"./tree.svg":9278,"./unlock.svg":9446,"./user.svg":3483,"./user1.svg":3120,"./validCode.svg":8783,"./visits.svg":7190,"./web.svg":744,"./wechat.svg":596,"./weixin.svg":6624,"./zujian.svg":3219};function r(e){var t=i(e);return s(t)}function i(e){if(!s.o(a,e)){var t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}return a[e]}r.keys=function(){return Object.keys(a)},r.resolve=i,e.exports=r,r.id=8202},8224:(e,t,s)=>{"use strict";e.exports=s.p+"img/driver.4bad36a2.svg"},8227:e=>{e.exports={deployEnv:"production",title:"论文生成",tagsView:!0,fixedHeader:!0,tokenCookieExpires:180,passCookieExpires:1,TokenKey:"__UDF__",timeout:12e5,sidebarLogo:!0,showFooter:!0,footerTxt:"© 2018-2023 ",caseNumber:"",wxAppId:"",wxRedirectUri:{NODE_ENV:"production",VUE_APP_ENV:"production",VUE_APP_SITE_TITLE:"神笔365 https://365d.ink",BASE_URL:"/"}.VUE_APP_WXLOGIN_CALLBACK}},8270:(e,t,s)=>{"use strict";e.exports=s.p+"img/index.e33796bb.svg"},8384:(e,t,s)=>{"use strict";e.exports=s.p+"img/monitor.754096fd.svg"},8418:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>m});var a=function(){var e=this,t=e._self._c;return t("div",{staticClass:"admin-login-container"},[t("div",{staticClass:"login-box"},[e._m(0),t("el-form",{ref:"loginForm",staticClass:"login-form",attrs:{model:e.loginForm,rules:e.loginRules,"label-position":"top"},nativeOn:{submit:function(t){return t.preventDefault(),e.handleLogin.apply(null,arguments)}}},[t("el-form-item",{attrs:{label:"用户名",prop:"username"}},[t("el-input",{attrs:{id:"username","prefix-icon":"el-icon-user",placeholder:"请输入管理员用户名"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleLogin.apply(null,arguments)}},model:{value:e.loginForm.username,callback:function(t){e.$set(e.loginForm,"username",t)},expression:"loginForm.username"}})],1),t("el-form-item",{attrs:{label:"密码",prop:"password"}},[t("el-input",{attrs:{id:"password","prefix-icon":"el-icon-lock",type:"password",placeholder:"请输入密码","show-password":""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleLogin.apply(null,arguments)}},model:{value:e.loginForm.password,callback:function(t){e.$set(e.loginForm,"password",t)},expression:"loginForm.password"}})],1),t("el-form-item",[t("el-checkbox",{attrs:{id:"remember"},model:{value:e.rememberMe,callback:function(t){e.rememberMe=t},expression:"rememberMe"}},[e._v("记住我")])],1),t("el-form-item",[t("el-button",{staticClass:"login-btn",attrs:{type:"primary",size:"large",loading:e.loading},on:{click:e.handleLogin}},[e._v(" "+e._s(e.loading?"登录中...":"登录")+" ")])],1)],1),e._m(1)],1)])},r=[function(){var e=this,t=e._self._c;return t("div",{staticClass:"login-header"},[t("img",{staticClass:"logo",attrs:{src:s(2806),alt:"Logo"}}),t("h2",[e._v("后台管理系统")]),t("p",[e._v("管理员登录")])])},function(){var e=this,t=e._self._c;return t("div",{staticClass:"login-footer"},[t("p",[e._v("© 2024 早鸟论文 - 后台管理系统")])])}],i=s(5353),o=s(8987);const n={name:"AdminLogin",data(){return{loading:!1,loginForm:{username:"",password:""},loginRules:{username:[{required:!0,message:"请输入管理员账号",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:6,message:"密码长度不能少于6位",trigger:"blur"}]},rememberMe:!1}},mounted(){this.$store.getters["admin/isAdminLoggedIn"]&&this.$router.push("/admin/dashboard")},methods:{...(0,i.i0)("admin",["adminLogin"]),async handleLogin(){try{const e=await this.$refs.loginForm.validate();if(!e)return;this.loading=!0;const t=await this.adminLogin(this.loginForm);if(t.success){const e=t.data.token;e&&(o.A.set("admin_token",e,{expires:7}),localStorage.setItem("admin_token",e)),this.$message.success("登录成功"),setTimeout((()=>{this.$router.push("/admin/dashboard")}),100)}else this.$message.error(t.message||"登录失败")}catch(e){this.$message.error("登录失败，请稍后重试")}finally{this.loading=!1}}}},c=n;var l=s(1656),u=(0,l.A)(c,a,r,!1,null,"3f977089",null);const m=u.exports},8695:(e,t,s)=>{"use strict";e.exports=s.p+"img/sys-tools.7ffef3aa.svg"},8734:(e,t,s)=>{"use strict";e.exports=s.p+"img/dictionary.e948e867.svg"},8783:(e,t,s)=>{"use strict";e.exports=s.p+"img/validCode.b8674c27.svg"},8956:(e,t,s)=>{"use strict";e.exports=s.p+"img/sqlMonitor.61b03fba.svg"},8993:(e,t,s)=>{"use strict";e.exports=s.p+"img/ipvisits.f24a2a1c.svg"},9192:(e,t,s)=>{"use strict";s.d(t,{BF:()=>o,Qk:()=>i,XN:()=>l,bk:()=>c,cF:()=>n,iX:()=>m,jC:()=>u});var a=s(5545),r=s(8987);const i={login(e){return console.log("发起管理员登录请求"),(0,a["default"])({url:"/api/admin/auth/login",method:"post",data:e}).then((e=>(console.log("管理员登录API响应:",e),e))).catch((e=>(console.error("登录请求失败:",e),{success:!1,message:e.message||"登录失败，请稍后重试"})))},logout(){console.log("调用管理员登出API");const e=r.A.get("admin_token")||localStorage.getItem("admin_token");return(0,a["default"])({url:"/api/admin/auth/logout",method:"post",timeout:5e3,headers:e?{Authorization:`Bearer ${e}`}:{},retry:0}).then((e=>(console.log("登出API调用成功:",e),e))).catch((e=>(console.warn("登出API调用失败，但将继续清理本地状态:",e.message),{success:!0,message:"已退出登录"})))},getProfile(){return(0,a["default"])({url:"/api/admin/auth/profile",method:"get"})},changePassword(e){return(0,a["default"])({url:"/api/admin/auth/change-password",method:"post",data:e})}},o={getList(e){return(0,a["default"])({url:"/api/admin/users/list",method:"get",params:e})},getDetail(e){return(0,a["default"])({url:`/api/admin/users/${e}`,method:"get"})},create(e){return(0,a["default"])({url:"/api/admin/users",method:"post",data:e})},update(e,t){return(0,a["default"])({url:`/api/admin/users/${e}`,method:"put",data:t})},delete(e){return(0,a["default"])({url:`/api/admin/users/${e}`,method:"delete"})},toggleLock(e,t){return(0,a["default"])({url:`/api/admin/users/${e}/lock`,method:"post",data:t})},vipManage(e,t){return(0,a["default"])({url:`/api/admin/users/${e}/vip`,method:"post",data:t})},resetPassword(e,t){return(0,a["default"])({url:`/api/admin/users/${e}/reset-password`,method:"post",data:t})},batchDelete(e){return(0,a["default"])({url:"/api/admin/users/batch/delete",method:"post",data:e})},batchLock(e){return(0,a["default"])({url:"/api/admin/users/batch/lock",method:"post",data:e})},batchVip(e){return(0,a["default"])({url:"/api/admin/users/batch/vip",method:"post",data:e})},getStats(e){return(0,a["default"])({url:"/api/admin/users/stats",method:"get",params:e})}},n={getList(e){return(0,a["default"])({url:"/api/admin/thesis/list",method:"get",params:e})},getDetail(e){return(0,a["default"])({url:`/api/admin/thesis/${e}`,method:"get"})},delete(e){return(0,a["default"])({url:`/api/admin/thesis/${e}`,method:"delete"})},getStats(e){return(0,a["default"])({url:"/api/admin/thesis/stats",method:"get",params:e})},getUserThesis(e,t){return(0,a["default"])({url:`/api/admin/thesis/user/${e}`,method:"get",params:t})}},c={getOverview(){return(0,a["default"])({url:"/api/admin/stats/overview",method:"get"})},getUserStats(e){return(0,a["default"])({url:"/api/admin/stats/users",method:"get",params:e})},getThesisStats(e){return(0,a["default"])({url:"/api/admin/stats/thesis",method:"get",params:e})},getChatStats(e){return(0,a["default"])({url:"/api/admin/stats/chat",method:"get",params:e})}},l={getSettings(){return(0,a["default"])({url:"/api/admin/settings",method:"get"})},saveSettings(e){return(0,a["default"])({url:"/api/admin/settings",method:"post",data:e})},testApiConnection(e){return(0,a["default"])({url:"/api/admin/settings/test-api",method:"post",data:e})},resetSettings(){return(0,a["default"])({url:"/api/admin/settings/reset",method:"post"})}},u={getList(e){return console.log("调用管理员列表API，参数:",e),(0,a["default"])({url:"/api/admin/accounts/list",method:"get",params:e,timeout:1e4}).catch((e=>(console.error("获取管理员列表失败:",e),{success:!1,message:"获取管理员列表失败，请稍后重试",data:{list:[],total:0}})))},getDetail(e){return(0,a["default"])({url:`/api/admin/accounts/${e}`,method:"get",timeout:1e4}).catch((e=>(console.error("获取管理员详情失败:",e),{success:!1,message:"获取管理员详情失败，请稍后重试",data:null})))},create(e){return(0,a["default"])({url:"/api/admin/accounts",method:"post",data:e,timeout:1e4}).catch((e=>(console.error("创建管理员失败:",e),{success:!1,message:"创建管理员失败，请稍后重试",data:null})))},update(e,t){return(0,a["default"])({url:`/api/admin/accounts/${e}`,method:"put",data:t,timeout:1e4}).catch((e=>(console.error("更新管理员失败:",e),{success:!1,message:"更新管理员失败，请稍后重试",data:null})))},delete(e){return(0,a["default"])({url:`/api/admin/accounts/${e}`,method:"delete",timeout:1e4}).catch((e=>(console.error("删除管理员失败:",e),{success:!1,message:"删除管理员失败，请稍后重试",data:null})))},resetPassword(e,t){return(0,a["default"])({url:`/api/admin/accounts/${e}/reset-password`,method:"post",data:t,timeout:1e4}).catch((e=>(console.error("重置密码失败:",e),{success:!1,message:"重置密码失败，请稍后重试",data:null})))}},m={getOrders(e){return(0,a["default"])({url:"/api/admin/settings/payment/orders",method:"get",params:e})},getOrderDetail(e){return(0,a["default"])({url:`/api/admin/settings/payment/order/${e}`,method:"get"})},getPaymentStats(e){return(0,a["default"])({url:"/api/admin/settings/payment/stats",method:"get",params:e})}}},9238:(e,t,s)=>{"use strict";e.exports=s.p+"img/shopping.ea6a63c9.svg"},9278:(e,t,s)=>{"use strict";e.exports=s.p+"img/tree.262249b9.svg"},9290:(e,t,s)=>{"use strict";e.exports=s.p+"img/dashboard.c2d7a0a6.svg"},9446:(e,t,s)=>{"use strict";e.exports=s.p+"img/unlock.1af25bbb.svg"},9525:(e,t,s)=>{"use strict";var a=s(5471),r=s(8987),i=s(1052),o=s.n(i),n=s(9952),c=s(3527),l=function(){var e=this,t=e._self._c;return t("router-view")},u=[];const m={name:"App"},d=m;var g=s(1656),p=(0,g.A)(d,l,u,!1,null,null,null);const h=p.exports;var v=s(3465),f=s(6916),_=s(3688),y=function(){var e=this,t=e._self._c;return e.isExternal?t("div",e._g({staticClass:"svg-external-icon svg-icon",style:e.styleExternalIcon},e.$listeners)):t("svg",e._g({class:e.svgClass,attrs:{"aria-hidden":"true"}},e.$listeners),[t("use",{attrs:{href:e.iconName}})])},b=[];function w(e){return/^(https?:|mailto:|tel:)/.test(e)}const C={name:"SvgIcon",props:{iconClass:{type:String,required:!0},className:{type:String,default:""}},computed:{isExternal(){return w(this.iconClass)},iconName(){return`#icon-${this.iconClass}`},svgClass(){return this.className?"svg-icon "+this.className:"svg-icon"},styleExternalIcon(){return{mask:`url(${this.iconClass}) no-repeat 50% 50%`,"-webkit-mask":`url(${this.iconClass}) no-repeat 50% 50%`}}}},S=C;var I=(0,g.A)(S,y,b,!1,null,"4342976c",null);const A=I.exports;a["default"].component("svg-icon",A);const k=s(8202),x=e=>e.keys().map(e);x(k);var T=s(4335),L=s(5545),P=s(9393);const E={el:{colorpicker:{confirm:"确定",clear:"清空"},datepicker:{now:"此刻",today:"今天",cancel:"取消",clear:"清空",confirm:"确定",selectDate:"选择日期",selectTime:"选择时间",startDate:"开始日期",startTime:"开始时间",endDate:"结束日期",endTime:"结束时间",prevYear:"前一年",nextYear:"后一年",prevMonth:"上个月",nextMonth:"下个月",year:"年",month1:"1 月",month2:"2 月",month3:"3 月",month4:"4 月",month5:"5 月",month6:"6 月",month7:"7 月",month8:"8 月",month9:"9 月",month10:"10 月",month11:"11 月",month12:"12 月",weeks:{sun:"日",mon:"一",tue:"二",wed:"三",thu:"四",fri:"五",sat:"六"},months:{jan:"一月",feb:"二月",mar:"三月",apr:"四月",may:"五月",jun:"六月",jul:"七月",aug:"八月",sep:"九月",oct:"十月",nov:"十一月",dec:"十二月"}},select:{loading:"加载中",noMatch:"无匹配数据",noData:"无数据",placeholder:"请选择"},cascader:{noMatch:"无匹配数据",loading:"加载中",placeholder:"请选择",noData:"暂无数据"},pagination:{goto:"前往",pagesize:"条/页",total:"共 {total} 条",pageClassifier:"页",prev:"上一页",next:"下一页"},messagebox:{title:"提示",confirm:"确定",cancel:"取消",error:"输入的数据不合法!"},upload:{deleteTip:"按 delete 键可删除",delete:"删除",preview:"查看图片",continue:"继续上传"},table:{emptyText:"暂无数据",confirmFilter:"筛选",resetFilter:"重置",clearFilter:"全部",sumText:"合计"},tree:{emptyText:"暂无数据"},transfer:{noMatch:"无匹配数据",noData:"无数据",titles:["列表 1","列表 2"],filterPlaceholder:"请输入搜索内容",noCheckedFormat:"共 {total} 项",hasCheckedFormat:"已选 {checked}/{total} 项"},image:{error:"加载失败"},pageHeader:{title:"返回"},popconfirm:{confirmButtonText:"确定",cancelButtonText:"取消"}}};f.A.mergeLocaleMessage("zh",E),c["default"].use(n["default"]),a["default"].use(o(),{size:r.A.get("size")||"small",i18n:(e,t)=>f.A.t(e,t)}),a["default"].config.productionTip=!1,a["default"].prototype.$axios=T.A,a["default"].prototype.$request=L["default"],a["default"].prototype.$echarts=P;new a["default"]({el:"#app",router:_.A,store:v.A,i18n:f.A,render:e=>e(h)})},9640:(e,t,s)=>{"use strict";e.exports=s.p+"img/backup.89a4d9f3.svg"}},t={};function s(a){var r=t[a];if(void 0!==r)return r.exports;var i=t[a]={id:a,loaded:!1,exports:{}};return e[a](i,i.exports,s),i.loaded=!0,i.exports}s.m=e,(()=>{s.amdO={}})(),(()=>{var e=[];s.O=(t,a,r,i)=>{if(!a){var o=1/0;for(u=0;u<e.length;u++){for(var[a,r,i]=e[u],n=!0,c=0;c<a.length;c++)(!1&i||o>=i)&&Object.keys(s.O).every((e=>s.O[e](a[c])))?a.splice(c--,1):(n=!1,i<o&&(o=i));if(n){e.splice(u--,1);var l=r();void 0!==l&&(t=l)}}return t}i=i||0;for(var u=e.length;u>0&&e[u-1][2]>i;u--)e[u]=e[u-1];e[u]=[a,r,i]}})(),(()=>{s.n=e=>{var t=e&&e.__esModule?()=>e["default"]:()=>e;return s.d(t,{a:t}),t}})(),(()=>{s.d=(e,t)=>{for(var a in t)s.o(t,a)&&!s.o(e,a)&&Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}})(),(()=>{s.f={},s.e=e=>Promise.all(Object.keys(s.f).reduce(((t,a)=>(s.f[a](e,t),t)),[]))})(),(()=>{s.u=e=>"js/"+e+"."+{8:"a13b62ce",12:"3ad85832",22:"683bc166",318:"f0e0c059",344:"55b5d054",345:"60fcc906",389:"07902b1c",503:"feaf9cee",586:"db48df0b",595:"9b29ab14",632:"4708b808",773:"0e3dee40",795:"6403a27c",897:"25207915",917:"d8e6dc91"}[e]+".js"})(),(()=>{s.miniCssF=e=>"css/"+e+"."+{4:"704439ad",8:"38fdf013",12:"9da2fd05",318:"61ca778d",344:"23f5a9b5",345:"dd990fc9",389:"a50a9ea4",503:"2916f959",586:"0fe67223",595:"ff00908e",632:"8fbb1f64",739:"68dac6d0",773:"5a050b18",795:"0932a914",917:"197b99a7"}[e]+".css"})(),(()=>{s.g=function(){if("object"===typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"===typeof window)return window}}()})(),(()=>{s.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t)})(),(()=>{var e={},t="open-essay-gui:";s.l=(a,r,i,o)=>{if(e[a])e[a].push(r);else{var n,c;if(void 0!==i)for(var l=document.getElementsByTagName("script"),u=0;u<l.length;u++){var m=l[u];if(m.getAttribute("src")==a||m.getAttribute("data-webpack")==t+i){n=m;break}}n||(c=!0,n=document.createElement("script"),n.charset="utf-8",n.timeout=120,s.nc&&n.setAttribute("nonce",s.nc),n.setAttribute("data-webpack",t+i),n.src=a),e[a]=[r];var d=(t,s)=>{n.onerror=n.onload=null,clearTimeout(g);var r=e[a];if(delete e[a],n.parentNode&&n.parentNode.removeChild(n),r&&r.forEach((e=>e(s))),t)return t(s)},g=setTimeout(d.bind(null,void 0,{type:"timeout",target:n}),12e4);n.onerror=d.bind(null,n.onerror),n.onload=d.bind(null,n.onload),c&&document.head.appendChild(n)}}})(),(()=>{s.r=e=>{"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}})(),(()=>{s.nmd=e=>(e.paths=[],e.children||(e.children=[]),e)})(),(()=>{s.p="/"})(),(()=>{if("undefined"!==typeof document){var e=(e,t,a,r,i)=>{var o=document.createElement("link");o.rel="stylesheet",o.type="text/css",s.nc&&(o.nonce=s.nc);var n=s=>{if(o.onerror=o.onload=null,"load"===s.type)r();else{var a=s&&s.type,n=s&&s.target&&s.target.href||t,c=new Error("Loading CSS chunk "+e+" failed.\n("+a+": "+n+")");c.name="ChunkLoadError",c.code="CSS_CHUNK_LOAD_FAILED",c.type=a,c.request=n,o.parentNode&&o.parentNode.removeChild(o),i(c)}};return o.onerror=o.onload=n,o.href=t,a?a.parentNode.insertBefore(o,a.nextSibling):document.head.appendChild(o),o},t=(e,t)=>{for(var s=document.getElementsByTagName("link"),a=0;a<s.length;a++){var r=s[a],i=r.getAttribute("data-href")||r.getAttribute("href");if("stylesheet"===r.rel&&(i===e||i===t))return r}var o=document.getElementsByTagName("style");for(a=0;a<o.length;a++){r=o[a],i=r.getAttribute("data-href");if(i===e||i===t)return r}},a=a=>new Promise(((r,i)=>{var o=s.miniCssF(a),n=s.p+o;if(t(o,n))return r();e(a,n,null,r,i)})),r={524:0};s.f.miniCss=(e,t)=>{var s={4:1,8:1,12:1,318:1,344:1,345:1,389:1,503:1,586:1,595:1,632:1,739:1,773:1,795:1,917:1};r[e]?t.push(r[e]):0!==r[e]&&s[e]&&t.push(r[e]=a(e).then((()=>{r[e]=0}),(t=>{throw delete r[e],t})))}}})(),(()=>{var e={524:0};s.f.j=(t,a)=>{var r=s.o(e,t)?e[t]:void 0;if(0!==r)if(r)a.push(r[2]);else if(/^(4|739)$/.test(t))e[t]=0;else{var i=new Promise(((s,a)=>r=e[t]=[s,a]));a.push(r[2]=i);var o=s.p+s.u(t),n=new Error,c=a=>{if(s.o(e,t)&&(r=e[t],0!==r&&(e[t]=void 0),r)){var i=a&&("load"===a.type?"missing":a.type),o=a&&a.target&&a.target.src;n.message="Loading chunk "+t+" failed.\n("+i+": "+o+")",n.name="ChunkLoadError",n.type=i,n.request=o,r[1](n)}};s.l(o,c,"chunk-"+t,t)}},s.O.j=t=>0===e[t];var t=(t,a)=>{var r,i,[o,n,c]=a,l=0;if(o.some((t=>0!==e[t]))){for(r in n)s.o(n,r)&&(s.m[r]=n[r]);if(c)var u=c(s)}for(t&&t(a);l<o.length;l++)i=o[l],s.o(e,i)&&e[i]&&e[i][0](),e[i]=0;return s.O(u)},a=self["webpackChunkopen_essay_gui"]=self["webpackChunkopen_essay_gui"]||[];a.forEach(t.bind(null,0)),a.push=t.bind(null,a.push.bind(a))})();var a=s.O(void 0,[504],(()=>s(9525)));a=s.O(a)})();