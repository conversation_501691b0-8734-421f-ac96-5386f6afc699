#!/usr/bin/env python3
"""
调试价格配置问题
检查数据库中的实际价格配置
"""

import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置环境变量
os.environ["FLASK_ENV"] = "production"

def debug_price_config_database():
    """调试数据库中的价格配置"""
    try:
        import pymysql
        from EarlyBird.config.config import AppConfig
        
        # 解析数据库连接信息
        db_uri = AppConfig.SQLALCHEMY_DATABASE_URI
        
        # 从URI中提取连接信息
        import re
        match = re.match(r'mysql\+pymysql://([^:]+):([^@]+)@([^:]+):(\d+)/([^?]+)', db_uri)
        if not match:
            print("❌ 无法解析数据库连接URI")
            return False
        
        username, password, host, port, database = match.groups()
        port = int(port)
        
        # 连接数据库
        connection = pymysql.connect(
            host=host,
            port=port,
            user=username,
            password=password,
            database=database,
            charset='utf8mb4'
        )
        
        cursor = connection.cursor()
        
        print("🔍 调试价格配置...")
        
        # 查询所有价格相关配置
        cursor.execute("""
            SELECT id, config_key, config_value, is_deleted, create_time, update_time
            FROM earlybird_paper_payment_config 
            WHERE config_key LIKE '%price%' OR config_key LIKE '%thesis.download%'
            ORDER BY config_key, create_time
        """)
        
        configs = cursor.fetchall()
        
        print(f"📋 价格相关配置查询结果 (总数: {len(configs)}):")
        for config in configs:
            config_id, config_key, config_value, is_deleted, create_time, update_time = config
            status = "❌ 已删除" if is_deleted else "✅ 有效"
            print(f"  ID: {config_id}")
            print(f"    Key: {config_key}")
            print(f"    Value: '{config_value}' (类型: {type(config_value)})")
            print(f"    状态: {status}")
            print(f"    创建: {create_time}, 更新: {update_time}")
            print()
        
        # 专门查询thesis.download.price配置
        cursor.execute("""
            SELECT id, config_key, config_value, is_deleted, create_time, update_time
            FROM earlybird_paper_payment_config 
            WHERE config_key = 'thesis.download.price'
            ORDER BY create_time DESC
        """)
        
        price_configs = cursor.fetchall()
        
        print(f"🎯 thesis.download.price 专项查询 (总数: {len(price_configs)}):")
        for config in price_configs:
            config_id, config_key, config_value, is_deleted, create_time, update_time = config
            status = "❌ 已删除" if is_deleted else "✅ 有效"
            print(f"  ID: {config_id}, Value: '{config_value}', 状态: {status}")
            print(f"    创建: {create_time}, 更新: {update_time}")
        
        # 查询当前有效的价格配置
        cursor.execute("""
            SELECT config_value
            FROM earlybird_paper_payment_config 
            WHERE config_key = 'thesis.download.price' AND is_deleted = 0
            ORDER BY update_time DESC
            LIMIT 1
        """)
        
        current_price = cursor.fetchone()
        
        print(f"\n💰 当前有效价格配置:")
        if current_price:
            price_value = current_price[0]
            print(f"  配置值: '{price_value}' (类型: {type(price_value)})")
            try:
                float_price = float(price_value)
                print(f"  转换为浮点数: {float_price}")
                print(f"  是否为13.88: {'✅ 是' if float_price == 13.88 else '❌ 否'}")
            except ValueError as e:
                print(f"  ❌ 无法转换为浮点数: {e}")
        else:
            print(f"  ❌ 未找到有效的价格配置")
        
        cursor.close()
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ 调试价格配置失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def debug_payment_records():
    """调试支付记录中的价格"""
    try:
        import pymysql
        from EarlyBird.config.config import AppConfig
        
        # 解析数据库连接信息
        db_uri = AppConfig.SQLALCHEMY_DATABASE_URI
        
        # 从URI中提取连接信息
        import re
        match = re.match(r'mysql\+pymysql://([^:]+):([^@]+)@([^:]+):(\d+)/([^?]+)', db_uri)
        if not match:
            print("❌ 无法解析数据库连接URI")
            return False
        
        username, password, host, port, database = match.groups()
        port = int(port)
        
        # 连接数据库
        connection = pymysql.connect(
            host=host,
            port=port,
            user=username,
            password=password,
            database=database,
            charset='utf8mb4'
        )
        
        cursor = connection.cursor()
        
        print("\n🔍 调试支付记录中的价格...")
        
        # 查询最近的支付记录
        cursor.execute("""
            SELECT id, user_id, product_id, out_trade_no, amount, status, create_time
            FROM earlybird_paper_payment 
            WHERE product_id LIKE 'thesis_download%'
            ORDER BY create_time DESC
            LIMIT 10
        """)
        
        payments = cursor.fetchall()
        
        print(f"📋 最近的支付记录 (总数: {len(payments)}):")
        for payment in payments:
            payment_id, user_id, product_id, out_trade_no, amount, status, create_time = payment
            status_text = "✅ 已支付" if status == 1 else "❌ 未支付"
            print(f"  ID: {payment_id}, 用户: {user_id}, 金额: {amount}, {status_text}")
            print(f"    产品: {product_id}, 订单: {out_trade_no}")
            print(f"    创建时间: {create_time}")
            print()
        
        # 查询下载记录中的价格
        cursor.execute("""
            SELECT id, uid, thesis_id, price, payment_method, is_paid, create_time
            FROM earlybird_paper_thesis_download_record 
            ORDER BY create_time DESC
            LIMIT 10
        """)
        
        downloads = cursor.fetchall()
        
        print(f"📋 最近的下载记录 (总数: {len(downloads)}):")
        for download in downloads:
            download_id, uid, thesis_id, price, payment_method, is_paid, create_time = download
            paid_text = "✅ 已支付" if is_paid else "❌ 未支付"
            print(f"  ID: {download_id}, 用户: {uid}, 论文: {thesis_id}, 价格: {price}, {paid_text}")
            print(f"    支付方式: {payment_method}, 创建时间: {create_time}")
            print()
        
        cursor.close()
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ 调试支付记录失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def show_expected_fix():
    """显示预期修复"""
    print("\n🔧 预期修复方案:")
    print("=" * 60)
    
    print("1️⃣ 问题诊断:")
    print("  • 检查数据库中是否存在正确的价格配置 13.88")
    print("  • 确认配置的 is_deleted 状态为 0 (有效)")
    print("  • 验证配置读取逻辑是否正确")
    
    print("\n2️⃣ 可能的问题:")
    print("  ❌ 数据库中配置值不是 '13.88'")
    print("  ❌ 配置被标记为已删除 (is_deleted=1)")
    print("  ❌ 代码中使用了硬编码的默认值 10.0")
    print("  ❌ 多个配置记录导致读取错误")
    
    print("\n3️⃣ 修复步骤:")
    print("  1. 确认数据库配置正确")
    print("  2. 修复配置读取逻辑")
    print("  3. 统一所有价格读取点")
    print("  4. 添加详细调试日志")
    print("  5. 测试完整支付流程")

def main():
    """主函数"""
    print("🔧 价格配置调试工具")
    print("=" * 80)
    
    # 1. 调试数据库配置
    debug_price_config_database()
    
    # 2. 调试支付记录
    debug_payment_records()
    
    # 3. 显示修复方案
    show_expected_fix()
    
    print("\n" + "=" * 80)
    print("🎯 调试完成！")
    print("请根据上述信息确定问题根源并实施修复。")

if __name__ == "__main__":
    main()
