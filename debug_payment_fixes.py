#!/usr/bin/env python3
"""
调试支付修复效果
测试支付状态同步和已支付论文识别
"""

import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置环境变量
os.environ["FLASK_ENV"] = "production"

def test_payment_status_sync():
    """测试支付状态同步"""
    try:
        import pymysql
        from EarlyBird.config.config import AppConfig
        
        # 解析数据库连接信息
        db_uri = AppConfig.SQLALCHEMY_DATABASE_URI
        
        # 从URI中提取连接信息
        import re
        match = re.match(r'mysql\+pymysql://([^:]+):([^@]+)@([^:]+):(\d+)/([^?]+)', db_uri)
        if not match:
            print("❌ 无法解析数据库连接URI")
            return False
        
        username, password, host, port, database = match.groups()
        port = int(port)
        
        # 连接数据库
        connection = pymysql.connect(
            host=host,
            port=port,
            user=username,
            password=password,
            database=database,
            charset='utf8mb4'
        )
        
        cursor = connection.cursor()
        
        print("🔍 测试支付状态同步...")
        
        # 查找状态为0的支付记录
        cursor.execute("""
            SELECT id, user_id, product_id, out_trade_no, status, amount, create_time
            FROM earlybird_paper_payment 
            WHERE status = 0 AND product_id LIKE 'thesis_download_%'
            ORDER BY create_time DESC
            LIMIT 5
        """)
        
        unpaid_records = cursor.fetchall()
        
        if unpaid_records:
            print(f"📋 找到 {len(unpaid_records)} 条未支付记录:")
            for record in unpaid_records:
                record_id, user_id, product_id, out_trade_no, status, amount, create_time = record
                print(f"  订单: {out_trade_no}")
                print(f"  用户: {user_id}, 产品: {product_id}")
                print(f"  状态: {status}, 金额: {amount}")
                print(f"  创建时间: {create_time}")
                print()
        else:
            print("✅ 没有找到未支付记录")
        
        # 查找已支付记录
        cursor.execute("""
            SELECT id, user_id, product_id, out_trade_no, status, amount, pay_time
            FROM earlybird_paper_payment 
            WHERE status = 1 AND product_id LIKE 'thesis_download_%'
            ORDER BY pay_time DESC
            LIMIT 5
        """)
        
        paid_records = cursor.fetchall()
        
        if paid_records:
            print(f"💰 找到 {len(paid_records)} 条已支付记录:")
            for record in paid_records:
                record_id, user_id, product_id, out_trade_no, status, amount, pay_time = record
                print(f"  订单: {out_trade_no}")
                print(f"  用户: {user_id}, 产品: {product_id}")
                print(f"  状态: {status}, 金额: {amount}")
                print(f"  支付时间: {pay_time}")
                print()
        else:
            print("❌ 没有找到已支付记录")
        
        cursor.close()
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_download_logic():
    """测试下载逻辑"""
    try:
        from EarlyBird.web_server import create_custom_app
        from EarlyBird.api.thesis.service import ThesisServie
        from EarlyBird.api.thesis.schema import ParamDownloadThesis, ParamPaymentStatus
        
        app = create_custom_app()
        
        with app.app_context():
            print("🔍 测试下载逻辑...")
            
            service = ThesisServie()
            
            # 测试用例：用户14，论文35
            test_cases = [
                {"userId": 14, "thesisId": 35},
                {"userId": 14, "thesisId": 34}
            ]
            
            for i, case in enumerate(test_cases, 1):
                print(f"\n📋 测试用例 {i}: 用户{case['userId']}下载论文{case['thesisId']}")
                
                # 创建下载参数
                download_param = ParamDownloadThesis()
                download_param.userId = case["userId"]
                download_param.thesisId = case["thesisId"]
                
                # 调用下载API
                result = service.downloadThesis(download_param)
                
                if result.isSucc():
                    if result.data.get('file'):
                        print(f"  ✅ 可以下载")
                        print(f"  📄 文件: {result.data['file']}")
                        print(f"  🆓 免费原因: {result.data.get('free_reason', 'N/A')}")
                        print(f"  💬 消息: {result.data.get('message', 'N/A')}")
                    else:
                        print(f"  💰 需要支付")
                        print(f"  💵 价格: {result.data.get('price', 'N/A')}")
                        print(f"  📝 原因: {result.data.get('reason', 'N/A')}")
                else:
                    print(f"  ❌ 下载失败: {result.message}")
            
            return True
                
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_payment_status_query():
    """测试支付状态查询"""
    try:
        from EarlyBird.web_server import create_custom_app
        from EarlyBird.api.thesis.service import ThesisServie
        from EarlyBird.api.thesis.schema import ParamPaymentStatus
        
        app = create_custom_app()
        
        with app.app_context():
            print("🔍 测试支付状态查询...")
            
            service = ThesisServie()
            
            # 测试用例：用户14，论文35，订单thesis_download_45e708ba9040
            test_cases = [
                {"userId": 14, "thesisId": 35, "orderId": "thesis_download_45e708ba9040"},
                {"userId": 14, "thesisId": 34, "orderId": None}
            ]
            
            for i, case in enumerate(test_cases, 1):
                print(f"\n📋 测试用例 {i}: {case}")
                
                # 创建查询参数
                status_param = ParamPaymentStatus()
                status_param.userId = case["userId"]
                status_param.thesisId = case["thesisId"]
                status_param.orderId = case["orderId"]
                
                # 调用状态查询API
                result = service.getPaymentStatus(status_param)
                
                if result.isSucc():
                    print(f"  ✅ 查询成功")
                    print(f"  💰 是否已支付: {result.data.get('is_paid', False)}")
                    print(f"  📊 支付状态: {result.data.get('payment_status', 'N/A')}")
                    print(f"  🆔 订单号: {result.data.get('order_id', 'N/A')}")
                    print(f"  📄 下载文件: {result.data.get('download_file', 'N/A')}")
                    print(f"  🔄 自动下载: {result.data.get('auto_download', False)}")
                    print(f"  💬 消息: {result.data.get('message', 'N/A')}")
                else:
                    print(f"  ❌ 查询失败: {result.message}")
            
            return True
                
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 支付修复效果调试工具")
    print("=" * 50)
    
    print("\n1️⃣ 测试支付状态同步...")
    test_payment_status_sync()
    
    print("\n2️⃣ 测试下载逻辑...")
    test_download_logic()
    
    print("\n3️⃣ 测试支付状态查询...")
    test_payment_status_query()
    
    print("\n🎉 测试完成！")

if __name__ == "__main__":
    main()
