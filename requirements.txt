# Flask 相关依赖
Flask==2.2.5                  # Web 框架
Flask-CORS==5.0.0             # 跨域支持
Flask-SQLAlchemy==3.0.3       # ORM 支持
Flask-Migrate==4.0.5          # 数据库迁移
Flask-Pydantic==0.12.0        # 请求参数校验

# 数据库相关
SQLAlchemy==2.0.23            # ORM 核心库
alembic==1.12.0               # 数据库迁移工具
PyMySQL==1.1.1                # MySQL 驱动

# Web 服务器
waitress==3.0.2               # 生产环境 WSGI 服务器

# 数据处理
pandas==2.0.3                 # 数据分析
numpy==1.24.3                 # 数值计算

# HTTP 请求
requests==2.32.3              # HTTP 客户端
# aiohttp 用于异步 HTTP 请求，当前项目未直接用到可选
# aiohttp==3.11.11

# 日期时间处理
python-dateutil==2.8.2        # 日期处理

# 日志
colorlog==6.7.0               # 彩色日志输出
loguru==0.7.3                 # 日志库

# 加密相关
pycryptodome==3.20.0          # 加密算法
cryptography==42.0.4          # 加密算法
pyjwt==2.8.0                  # JWT 认证（auth.py 用到）

# 文档处理
python-docx==1.1.2            # 处理 docx 文档
lxml==5.3.0                   # XML/HTML 解析
beautifulsoup4==4.12.3        # HTML 解析

# AI相关
# dashscope 用于通义千问等大模型
# deepseek、kimi、doubao、gpt35 适配器依赖 dashscope
# 具体模型服务如有额外依赖请补充
# dashscope 需注册阿里云账号获取 key
# 官网：https://help.aliyun.com/zh/dashscope/developer-reference/quick-start
# pip 安装 dashscope
# pip install dashscope
# dashscope==1.20.14

dashscope==1.20.14             # 通义千问等大模型

# 其他工具
python-dotenv==1.0.0          # 环境变量加载
pydantic==2.10.4              # 数据校验
shortuuid==1.0.13             # 简短唯一ID
pillow==11.0.0                # 图片处理
psutil==6.1.1                 # 系统信息
prettytable==3.10.0           # 表格打印（init_flask.py 用到）
# pywebview 用于桌面 GUI（common/gui.py 用到）
pywebview==5.3.2              # 桌面窗口 