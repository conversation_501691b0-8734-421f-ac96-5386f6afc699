"use strict";(self["webpackChunkopen_essay_gui"]=self["webpackChunkopen_essay_gui"]||[]).push([[595],{7595:(e,t,a)=>{a.r(t),a.d(t,{default:()=>c});var s=function(){var e=this,t=e._self._c;return t("div",{staticClass:"payment-orders"},[t("div",{staticClass:"page-header"},[t("h2",[e._v("订单管理")]),t("div",{staticClass:"header-actions"},[t("el-button",{attrs:{type:"primary",size:"small",icon:"el-icon-refresh"},on:{click:e.loadOrders}},[e._v("刷新")])],1)]),t("el-card",{staticClass:"filter-container"},[t("el-form",{attrs:{inline:!0,model:e.queryParams,size:"small"}},[t("el-form-item",{attrs:{label:"订单状态"}},[t("el-select",{attrs:{placeholder:"全部状态",clearable:""},model:{value:e.queryParams.status,callback:function(t){e.$set(e.queryParams,"status",t)},expression:"queryParams.status"}},[t("el-option",{attrs:{label:"未支付",value:0}}),t("el-option",{attrs:{label:"已支付",value:1}}),t("el-option",{attrs:{label:"已退款",value:2}})],1)],1),t("el-form-item",{attrs:{label:"用户ID"}},[t("el-input",{attrs:{placeholder:"用户ID",clearable:""},model:{value:e.queryParams.user_id,callback:function(t){e.$set(e.queryParams,"user_id",t)},expression:"queryParams.user_id"}})],1),t("el-form-item",{attrs:{label:"论文ID"}},[t("el-input",{attrs:{placeholder:"论文ID",clearable:""},model:{value:e.queryParams.product_id,callback:function(t){e.$set(e.queryParams,"product_id",t)},expression:"queryParams.product_id"}})],1),t("el-form-item",{attrs:{label:"时间范围"}},[t("el-date-picker",{attrs:{type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"yyyy-MM-dd",align:"right"},model:{value:e.dateRange,callback:function(t){e.dateRange=t},expression:"dateRange"}})],1),t("el-form-item",[t("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.handleSearch}},[e._v("搜索")]),t("el-button",{attrs:{icon:"el-icon-refresh-left"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1)],1),t("el-card",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"table-container"},[t("el-table",{staticStyle:{width:"100%"},attrs:{data:e.orderList,border:""}},[t("el-table-column",{attrs:{prop:"id",label:"ID",width:"80",align:"center"}}),t("el-table-column",{attrs:{prop:"user_id",label:"用户ID",width:"100",align:"center"}}),t("el-table-column",{attrs:{label:"论文信息","min-width":"180"},scopedSlots:e._u([{key:"default",fn:function(a){return[a.row.thesisInfo?t("div",[t("div",{staticClass:"thesis-info"},[t("span",{staticClass:"thesis-id"},[e._v("ID: "+e._s(a.row.thesisInfo.id))]),t("el-link",{staticClass:"thesis-title",attrs:{type:"primary"},on:{click:function(t){return e.viewThesisDetail(a.row.thesisInfo.id)}}},[e._v(" "+e._s(a.row.thesisInfo.title||"未知论文")+" ")])],1)]):a.row.product_id&&a.row.product_id.includes("thesis_download")?t("div",[t("div",{staticClass:"thesis-info pending"},[t("span",{staticClass:"thesis-id"},[e._v("ID: "+e._s(e.extractThesisId(a.row.product_id)))]),t("el-tooltip",{attrs:{content:"论文可能已被删除或无法访问",placement:"top"}},[t("span",{staticClass:"thesis-title-pending"},[e._v("论文信息未找到")])])],1)]):t("div",[t("span",{staticClass:"non-thesis-product"},[e._v(e._s(a.row.product_id||"未知商品"))])])]}}])}),t("el-table-column",{attrs:{prop:"amount",label:"金额",width:"100",align:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.row.amount.toFixed(2))+" 元 ")]}}])}),t("el-table-column",{attrs:{prop:"out_trade_no",label:"订单号","min-width":"180"}}),t("el-table-column",{attrs:{prop:"status",label:"状态",width:"100",align:"center"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-tag",{attrs:{type:e.getStatusType(a.row.status)}},[e._v(" "+e._s(e.getStatusText(a.row.status))+" ")])]}}])}),t("el-table-column",{attrs:{prop:"pay_type",label:"支付方式",width:"100",align:"center"}}),t("el-table-column",{attrs:{prop:"create_time",label:"创建时间",width:"160",align:"center"}}),t("el-table-column",{attrs:{prop:"pay_time",label:"支付时间",width:"160",align:"center"}}),t("el-table-column",{attrs:{label:"操作",width:"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-button",{attrs:{type:"text",size:"small",icon:"el-icon-view"},on:{click:function(t){return e.viewOrderDetail(a.row)}}},[e._v(" 详情 ")])]}}])})],1),t("div",{staticClass:"pagination-container"},[t("el-pagination",{attrs:{"current-page":e.queryParams.page,"page-sizes":[10,20,50,100],"page-size":e.queryParams.size,layout:"total, sizes, prev, pager, next, jumper",total:e.total},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)],1)],1)},r=[],i=a(9192);const l={name:"PaymentOrders",data(){return{queryParams:{page:1,size:20,status:"",user_id:"",product_id:"",start_date:"",end_date:""},dateRange:[],orderList:[],thesisCache:{},total:0,loading:!1}},created(){this.loadOrders()},methods:{async loadOrders(){try{this.loading=!0,this.dateRange&&2===this.dateRange.length&&(this.queryParams.start_date=this.dateRange[0],this.queryParams.end_date=this.dateRange[1]);const e=await i.iX.getOrders(this.queryParams);e.success&&e.data?(this.orderList=e.data.list||[],this.total=e.data.total||0,this.loadThesisInfo()):this.$message.error(e.message||"加载订单列表失败")}catch(e){console.error("加载订单列表失败:",e),this.$message.error("加载订单列表失败: "+(e.message||"未知错误"))}finally{this.loading=!1}},async loadThesisInfo(){try{console.log("开始加载论文信息...");const t=this.orderList.filter((e=>e.product_id&&e.product_id.includes("thesis_download"))).map((e=>{const t=e.product_id.match(/thesis_download_?(\d+)?/),a=t&&t[1]||null;return console.log(`从订单 ${e.id} 的商品ID ${e.product_id} 中提取论文ID: ${a}`),a})).filter((e=>null!==e)),a=[...new Set(t)];console.log(`找到 ${a.length} 个唯一论文ID需要加载: ${a.join(", ")}`);for(const s of a)if(this.thesisCache[s])console.log(`使用缓存中的论文ID ${s} 信息: ${this.thesisCache[s].title}`);else try{console.log(`获取论文ID ${s} 的详细信息...`);const e=await this.$axios.get(`/api/admin/thesis/detail/${s}`);e.data&&e.data.success&&e.data.data?(this.thesisCache[s]=e.data.data,console.log(`成功加载论文ID ${s} 的信息: ${e.data.data.title}`)):console.warn(`获取论文ID ${s} 信息失败: ${e.data?e.data.message:"未知错误"}`)}catch(e){console.error(`获取论文ID ${s} 信息失败:`,e)}this.orderList=this.orderList.map((e=>{if(e.product_id&&e.product_id.includes("thesis_download")){const t=e.product_id.match(/thesis_download_?(\d+)?/),a=t&&t[1]||null;if(a&&this.thesisCache[a])return console.log(`关联论文ID ${a} 到订单 ${e.id}`),{...e,thesisInfo:this.thesisCache[a]};console.log(`订单 ${e.id} 的论文ID ${a} 未找到对应信息`)}return e}))}catch(e){console.error("加载论文信息失败:",e)}},viewThesisDetail(e){e&&this.$router.push({name:"AdminThesisList",query:{id:e}})},extractThesisId(e){const t=e.match(/thesis_download_?(\d+)?/);return t&&t[1]||"未知"},getStatusType(e){switch(e){case 0:return"warning";case 1:return"success";case 2:return"info";default:return"info"}},getStatusText(e){switch(e){case 0:return"未支付";case 1:return"已支付";case 2:return"已退款";default:return"未知"}},viewOrderDetail(e){this.$router.push({name:"AdminPaymentOrderDetail",params:{id:e.id}})},handleSearch(){this.queryParams.page=1,this.loadOrders()},resetQuery(){this.queryParams={page:1,size:20,status:"",user_id:"",product_id:"",start_date:"",end_date:""},this.dateRange=[],this.loadOrders()},handleSizeChange(e){this.queryParams.size=e,this.loadOrders()},handleCurrentChange(e){this.queryParams.page=e,this.loadOrders()},goBack(){this.$router.push({name:"AdminThesis"})},goToStats(){this.$message.info("统计分析功能即将上线")}}},n=l;var o=a(1656),d=(0,o.A)(n,s,r,!1,null,"ec00b9a8",null);const c=d.exports}}]);