#!/usr/bin/env python3
"""
验证API路由修复
检查前端API调用与后端路由的一致性
"""

import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def check_backend_routes():
    """检查后端路由"""
    try:
        print("🔍 检查后端API路由...")
        
        # 读取后端路由文件
        with open('EarlyBird/api/admin/thesis.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找路由定义
        import re
        routes = re.findall(r"@admin_thesis_bp\.route\('([^']+)'", content)
        
        print("📋 后端论文API路由:")
        for route in routes:
            full_route = f"/api/admin/thesis{route}"
            print(f"  ✅ {full_route}")
        
        return routes
        
    except Exception as e:
        print(f"❌ 检查后端路由失败: {str(e)}")
        return []

def check_frontend_api():
    """检查前端API定义"""
    try:
        print("\n🔍 检查前端API定义...")
        
        # 读取前端API文件
        with open('frontend/src/api/admin.js', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找论文相关的API调用
        import re
        
        # 查找getDetail方法
        detail_match = re.search(r"getDetail\(thesisId\)\s*{[^}]*url:\s*[`']([^`']+)[`']", content)
        if detail_match:
            detail_url = detail_match.group(1)
            print(f"📋 前端API定义:")
            print(f"  getDetail: {detail_url}")
        
        # 查找delete方法
        delete_match = re.search(r"delete\(thesisId\)\s*{[^}]*url:\s*[`']([^`']+)[`']", content)
        if delete_match:
            delete_url = delete_match.group(1)
            print(f"  delete: {delete_url}")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查前端API失败: {str(e)}")
        return False

def verify_route_consistency():
    """验证路由一致性"""
    print("\n🔍 验证路由一致性...")
    
    expected_mappings = {
        "前端API": "后端路由",
        "getDetail: /api/admin/thesis/detail/${thesisId}": "/api/admin/thesis/detail/<int:thesis_id>",
        "delete: /api/admin/thesis/delete/${thesisId}": "/api/admin/thesis/delete/<int:thesis_id>",
        "title: /api/admin/thesis/title/${thesisId}": "/api/admin/thesis/title/<int:thesis_id>",
    }
    
    print("📋 预期的路由映射:")
    for frontend, backend in expected_mappings.items():
        if frontend == "前端API":
            print(f"  {frontend} -> {backend}")
        else:
            print(f"  ✅ {frontend}")
            print(f"     -> {backend}")

def show_fix_summary():
    """显示修复总结"""
    print("\n📋 API路由修复总结")
    print("=" * 80)
    
    print("🔧 修复的问题:")
    print("  1. ✅ 论文详情API路径不匹配")
    print("     - 前端: /api/admin/thesis/${thesisId} (错误)")
    print("     - 后端: /api/admin/thesis/detail/<int:thesis_id>")
    print("     - 修复: /api/admin/thesis/detail/${thesisId}")
    
    print("  2. ✅ 删除论文API路径不匹配")
    print("     - 前端: /api/admin/thesis/${thesisId} (错误)")
    print("     - 后端: /api/admin/thesis/delete/<int:thesis_id>")
    print("     - 修复: /api/admin/thesis/delete/${thesisId}")
    
    print("  3. ✅ 新增论文标题API")
    print("     - 前端: /api/admin/thesis/title/${thesisId}")
    print("     - 后端: /api/admin/thesis/title/<int:thesis_id>")
    print("     - 用途: 订单页面显示论文标题")

def show_test_scenarios():
    """显示测试场景"""
    print("\n🧪 测试场景")
    print("=" * 80)
    
    print("📋 需要测试的功能:")
    print("  1. 论文列表页面 (/admin/thesis/list)")
    print("     - 点击'查看详情'按钮")
    print("     - 应该正常打开论文详情弹窗")
    print("     - 不再出现404错误")
    
    print("  2. 论文删除功能")
    print("     - 点击'删除'按钮")
    print("     - 应该正常删除论文")
    print("     - 不再出现404错误")
    
    print("  3. 订单页面 (/admin/settings/orders)")
    print("     - 论文标题正常显示")
    print("     - 不再出现401错误")
    print("     - 使用新的title API")

def show_available_thesis():
    """显示可用的论文"""
    try:
        print("\n📋 可用的论文数据")
        print("=" * 80)
        
        import pymysql
        from EarlyBird.config.config import AppConfig
        
        # 解析数据库连接信息
        db_uri = AppConfig.SQLALCHEMY_DATABASE_URI
        
        # 从URI中提取连接信息
        import re
        match = re.match(r'mysql\+pymysql://([^:]+):([^@]+)@([^:]+):(\d+)/([^?]+)', db_uri)
        if not match:
            print("❌ 无法解析数据库连接URI")
            return
        
        username, password, host, port, database = match.groups()
        port = int(port)
        
        # 连接数据库
        connection = pymysql.connect(
            host=host,
            port=port,
            user=username,
            password=password,
            database=database,
            charset='utf8mb4'
        )
        
        cursor = connection.cursor()
        
        # 查找可用的论文
        cursor.execute("""
            SELECT id, title, uid, create_time
            FROM earlybird_paper_thesis 
            ORDER BY id DESC
            LIMIT 5
        """)
        
        thesis_list = cursor.fetchall()
        
        print(f"📋 系统中的论文 (总数: {len(thesis_list)}):")
        for thesis in thesis_list:
            thesis_id, title, uid, create_time = thesis
            title_display = title[:40] + "..." if title and len(title) > 40 else title or f"论文{thesis_id}"
            print(f"  ID: {thesis_id}")
            print(f"    标题: {title_display}")
            print(f"    用户: {uid}, 创建时间: {create_time}")
            print()
        
        cursor.close()
        connection.close()
        
    except Exception as e:
        print(f"❌ 获取论文数据失败: {str(e)}")

def main():
    """主函数"""
    print("🔧 API路由修复验证工具")
    print("=" * 80)
    
    # 检查后端路由
    backend_routes = check_backend_routes()
    
    # 检查前端API
    frontend_ok = check_frontend_api()
    
    # 验证路由一致性
    verify_route_consistency()
    
    # 显示修复总结
    show_fix_summary()
    
    # 显示测试场景
    show_test_scenarios()
    
    # 显示可用论文
    show_available_thesis()
    
    print("\n" + "=" * 80)
    print("🎯 验证结果:")
    print(f"  后端路由检查: {'✅ 通过' if backend_routes else '❌ 失败'}")
    print(f"  前端API检查: {'✅ 通过' if frontend_ok else '❌ 失败'}")
    
    if backend_routes and frontend_ok:
        print("\n🎉 API路由修复验证通过！")
        print("\n现在应该能够:")
        print("  • 正常查看论文详情 (不再404)")
        print("  • 正常删除论文 (不再404)")
        print("  • 订单页面显示论文标题 (不再401)")
        print("  • 所有管理员论文功能正常工作")
    else:
        print("\n⚠️ 部分验证失败，请检查错误信息")
    
    print("\n🔄 请重启服务器并测试:")
    print("  1. 论文列表: http://127.0.0.1:3301/admin/thesis/list")
    print("  2. 订单页面: http://127.0.0.1:3301/admin/settings/orders")

if __name__ == "__main__":
    main()
