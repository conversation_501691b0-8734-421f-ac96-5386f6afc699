%YAML 1.1
--- !!map {
  ? !!str "scalars" : !!map {
      ? !!str "plain"
      : !!str "some text",
      ? !!str "quoted"
      : !!map {
        ? !!str "single"
        : !!str "some text",
        ? !!str "double"
        : !!str "some text"
  } },
  ? !!str "collections" : !!map {
    ? !!str "sequence" : !!seq [
      !!str "entry",
      !!map {
        ? !!str "key" : !!str "value"
    } ],
    ? !!str "mapping" : !!map {
      ? !!str "key" : !!str "value"
} } }
