<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单支付测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .section h3 {
            color: #666;
            margin-top: 0;
        }
        button {
            background-color: #409EFF;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background-color: #66b1ff;
        }
        button.success {
            background-color: #67c23a;
        }
        button.success:hover {
            background-color: #85ce61;
        }
        .input-group {
            margin: 10px 0;
        }
        .input-group label {
            display: inline-block;
            width: 100px;
            font-weight: bold;
        }
        .input-group input {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 200px;
        }
        .result {
            background-color: #f8f8f8;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 10px;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-weight: bold;
        }
        .status.success {
            background-color: #f0f9ff;
            border: 1px solid #67c23a;
            color: #67c23a;
        }
        .status.error {
            background-color: #fef0f0;
            border: 1px solid #f56c6c;
            color: #f56c6c;
        }
        .status.warning {
            background-color: #fdf6ec;
            border: 1px solid #e6a23c;
            color: #e6a23c;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 简单支付功能测试</h1>
        
        <div class="section">
            <h3>📝 测试参数</h3>
            
            <div class="input-group">
                <label>用户ID:</label>
                <input type="number" id="userId" value="14" />
            </div>
            
            <div class="input-group">
                <label>论文ID:</label>
                <input type="number" id="thesisId" value="33" />
            </div>
            
            <div class="input-group">
                <label>支付方式:</label>
                <input type="text" id="paymentMethod" value="wechat" />
            </div>
        </div>

        <div class="section">
            <h3>🔧 测试操作</h3>
            
            <button onclick="testDownloadCheck()">1. 检查下载权限</button>
            <button onclick="testCreatePayment()">2. 创建支付订单</button>
            <button onclick="testPaymentStatus()">3. 查询支付状态</button>
            <button onclick="testSimulatePayment()" class="success">4. 模拟支付成功</button>
            <button onclick="testDownloadAfterPay()">5. 支付后下载</button>
            <button onclick="clearResult()">清空结果</button>
            
            <div id="test-status" class="status warning">
                等待测试操作...
            </div>
        </div>

        <div class="section">
            <h3>📊 测试结果</h3>
            <div id="test-result" class="result">等待测试结果...\n</div>
        </div>

        <div class="section">
            <h3>💡 使用说明</h3>
            <ol>
                <li><strong>检查下载权限</strong>：调用 downloadThesis API，查看是否需要支付</li>
                <li><strong>创建支付订单</strong>：调用 payDownload API，创建支付订单</li>
                <li><strong>查询支付状态</strong>：调用 paymentStatus API，查看支付状态</li>
                <li><strong>模拟支付成功</strong>：调用 simulatePayment API，模拟支付完成</li>
                <li><strong>支付后下载</strong>：再次调用 downloadThesis API，应该可以下载</li>
            </ol>
        </div>
    </div>

    <script>
        let currentOrderId = '';

        function log(message, type = 'info') {
            const resultElement = document.getElementById('test-result');
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warning' ? '⚠️' : 'ℹ️';
            resultElement.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            resultElement.scrollTop = resultElement.scrollHeight;
        }

        function showStatus(message, type) {
            const statusElement = document.getElementById('test-status');
            statusElement.textContent = message;
            statusElement.className = `status ${type}`;
        }

        function clearResult() {
            document.getElementById('test-result').textContent = '日志已清空...\n';
            showStatus('等待测试操作...', 'warning');
        }

        function getParams() {
            return {
                userId: parseInt(document.getElementById('userId').value),
                thesisId: parseInt(document.getElementById('thesisId').value),
                paymentMethod: document.getElementById('paymentMethod').value
            };
        }

        async function testDownloadCheck() {
            const params = getParams();
            log(`检查下载权限 - 用户: ${params.userId}, 论文: ${params.thesisId}`, 'info');
            
            try {
                const response = await fetch('/api/thesis/downloadThesis', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        thesisId: params.thesisId
                    })
                });
                
                const result = await response.json();
                
                if (result.is_success) {
                    if (result.data.need_payment) {
                        log(`需要支付: ${result.data.price} 元`, 'warning');
                        log(`原因: ${result.data.reason || '未提供原因'}`, 'info');
                        showStatus(`需要支付 ${result.data.price} 元`, 'warning');
                    } else {
                        log(`可以免费下载: ${result.data.file}`, 'success');
                        log(`免费原因: ${result.data.free_reason}`, 'info');
                        showStatus('可以免费下载', 'success');
                    }
                } else {
                    log(`检查失败: ${result.message}`, 'error');
                    showStatus(`检查失败: ${result.message}`, 'error');
                }
            } catch (error) {
                log(`请求失败: ${error.message}`, 'error');
                showStatus(`请求失败: ${error.message}`, 'error');
            }
        }

        async function testCreatePayment() {
            const params = getParams();
            log(`创建支付订单 - 用户: ${params.userId}, 论文: ${params.thesisId}`, 'info');
            
            try {
                const response = await fetch('/api/thesis/payDownload', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        thesisId: params.thesisId,
                        paymentMethod: params.paymentMethod
                    })
                });
                
                const result = await response.json();
                
                if (result.is_success) {
                    currentOrderId = result.data.order_id;
                    log(`支付订单创建成功: ${currentOrderId}`, 'success');
                    log(`支付金额: ${result.data.price} 元`, 'info');
                    log(`二维码URL: ${result.data.qr_code_url}`, 'info');
                    showStatus(`订单创建成功: ${currentOrderId}`, 'success');
                } else {
                    log(`创建失败: ${result.message}`, 'error');
                    showStatus(`创建失败: ${result.message}`, 'error');
                }
            } catch (error) {
                log(`请求失败: ${error.message}`, 'error');
                showStatus(`请求失败: ${error.message}`, 'error');
            }
        }

        async function testPaymentStatus() {
            const params = getParams();
            const orderId = currentOrderId || prompt('请输入订单号:');
            
            if (!orderId) {
                log('请先创建支付订单或输入订单号', 'error');
                return;
            }
            
            log(`查询支付状态 - 订单: ${orderId}`, 'info');
            
            try {
                const response = await fetch('/api/thesis/paymentStatus', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        thesisId: params.thesisId,
                        orderId: orderId
                    })
                });
                
                const result = await response.json();
                
                if (result.is_success) {
                    log(`支付状态: ${result.data.payment_status}`, 'info');
                    log(`是否已支付: ${result.data.is_paid}`, 'info');
                    log(`消息: ${result.data.message}`, 'info');
                    
                    if (result.data.is_paid) {
                        showStatus('支付已完成', 'success');
                    } else {
                        showStatus('支付未完成', 'warning');
                    }
                } else {
                    log(`查询失败: ${result.message}`, 'error');
                    showStatus(`查询失败: ${result.message}`, 'error');
                }
            } catch (error) {
                log(`请求失败: ${error.message}`, 'error');
                showStatus(`请求失败: ${error.message}`, 'error');
            }
        }

        async function testSimulatePayment() {
            const orderId = currentOrderId || prompt('请输入订单号:');
            
            if (!orderId) {
                log('请先创建支付订单或输入订单号', 'error');
                return;
            }
            
            log(`模拟支付成功 - 订单: ${orderId}`, 'info');
            
            try {
                const response = await fetch('/api/thesis/simulatePayment', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        order_id: orderId
                    })
                });
                
                const result = await response.json();
                
                if (result.is_success) {
                    log(`模拟支付成功: ${JSON.stringify(result.data)}`, 'success');
                    showStatus('模拟支付成功', 'success');
                } else {
                    log(`模拟支付失败: ${result.message}`, 'error');
                    showStatus(`模拟支付失败: ${result.message}`, 'error');
                }
            } catch (error) {
                log(`请求失败: ${error.message}`, 'error');
                showStatus(`请求失败: ${error.message}`, 'error');
            }
        }

        async function testDownloadAfterPay() {
            log('测试支付后下载...', 'info');
            await testDownloadCheck();
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('🧪 简单支付功能测试工具已加载', 'success');
            log('📋 请按顺序执行测试步骤', 'info');
        });
    </script>
</body>
</html>
