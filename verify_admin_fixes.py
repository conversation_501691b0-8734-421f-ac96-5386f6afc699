#!/usr/bin/env python3
"""
验证管理员系统修复
测试所有修复的功能
"""

import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置环境变量
os.environ["FLASK_ENV"] = "production"

def test_setting_model():
    """测试Setting模型"""
    try:
        print("🧪 测试Setting模型...")
        
        # 导入模型
        from EarlyBird.model.setting import Setting
        from EarlyBird.ExtendRegister.db_register import db
        
        # 测试查询
        settings = Setting.query.filter_by(category='system').all()
        print(f"✅ 查询成功，找到 {len(settings)} 个系统设置")
        
        # 测试to_dict方法
        if settings:
            setting = settings[0]
            setting_dict = setting.to_dict()
            print(f"✅ to_dict()方法测试成功")
            print(f"   示例数据: {setting_dict}")
        else:
            # 创建测试设置
            test_setting = Setting(
                category='system',
                key='test_setting',
                value='test_value',
                description='测试设置'
            )
            
            db.session.add(test_setting)
            db.session.commit()
            
            setting_dict = test_setting.to_dict()
            print(f"✅ 创建并测试设置成功: {setting_dict}")
            
            # 删除测试设置
            db.session.delete(test_setting)
            db.session.commit()
        
        return True
        
    except Exception as e:
        print(f"❌ 测试Setting模型失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_admin_permissions():
    """测试管理员权限"""
    try:
        print("\n🧪 测试管理员权限...")
        
        from EarlyBird.model.admin import Admin
        
        # 查询管理员
        admin = Admin.query.filter_by(is_active=True).first()
        
        if not admin:
            print("❌ 未找到活跃的管理员")
            return False
        
        print(f"✅ 找到管理员: {admin.username}")
        
        # 测试权限检查
        permissions_to_test = [
            ('system', 'config'),
            ('user', 'view'),
            ('thesis', 'view'),
        ]
        
        for resource, action in permissions_to_test:
            has_perm = admin.has_permission(resource, action)
            status = "✅" if has_perm else "❌"
            print(f"  {status} {resource}.{action}: {has_perm}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试管理员权限失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_wechat_pay_config():
    """测试微信支付配置"""
    try:
        print("\n🧪 测试微信支付配置...")
        
        from EarlyBird.model.wechat_pay_config import WeChatPayConfig
        
        # 查询配置
        config = WeChatPayConfig.query.filter_by(is_enabled=True).first()
        
        if config:
            print(f"✅ 找到微信支付配置: ID={config.id}")
            
            # 测试to_dict方法
            config_dict = config.to_dict()
            print(f"✅ to_dict()方法测试成功")
            print(f"   配置名称: {config_dict.get('name', 'N/A')}")
            print(f"   应用ID: {config_dict.get('appid', 'N/A')}")
            
        else:
            print("⚠️ 未找到启用的微信支付配置")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试微信支付配置失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_payment_config():
    """测试支付配置"""
    try:
        print("\n🧪 测试支付配置...")
        
        from EarlyBird.model.payment import PaymentConfig
        
        # 查询关键配置
        configs_to_check = [
            'thesis.download.is_active',
            'thesis.download.price',
            'thesis.download.first_free',
            'thesis.download.vip_free'
        ]
        
        for config_key in configs_to_check:
            config = PaymentConfig.query.filter_by(
                config_key=config_key, 
                is_deleted=False
            ).first()
            
            if config:
                print(f"✅ {config_key}: {config.config_value}")
            else:
                print(f"❌ {config_key}: 未找到")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试支付配置失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_database_tables():
    """测试数据库表"""
    try:
        print("\n🧪 测试数据库表...")
        
        import pymysql
        from EarlyBird.config.config import AppConfig
        
        # 解析数据库连接信息
        db_uri = AppConfig.SQLALCHEMY_DATABASE_URI
        
        # 从URI中提取连接信息
        import re
        match = re.match(r'mysql\+pymysql://([^:]+):([^@]+)@([^:]+):(\d+)/([^?]+)', db_uri)
        if not match:
            print("❌ 无法解析数据库连接URI")
            return False
        
        username, password, host, port, database = match.groups()
        port = int(port)
        
        # 连接数据库
        connection = pymysql.connect(
            host=host,
            port=port,
            user=username,
            password=password,
            database=database,
            charset='utf8mb4'
        )
        
        cursor = connection.cursor()
        
        # 检查关键表
        tables_to_check = [
            'earlybird_paper_setting',
            'earlybird_paper_admin',
            'earlybird_paper_payment_config',
            'wechat_pay_config'
        ]
        
        for table_name in tables_to_check:
            cursor.execute("""
                SELECT COUNT(*) 
                FROM information_schema.tables 
                WHERE table_schema = %s AND table_name = %s
            """, (database, table_name))
            
            exists = cursor.fetchone()[0] > 0
            status = "✅" if exists else "❌"
            print(f"  {status} {table_name}: {'存在' if exists else '不存在'}")
        
        cursor.close()
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ 测试数据库表失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def show_fix_summary():
    """显示修复总结"""
    print("\n📋 修复总结")
    print("=" * 80)
    
    print("🔧 已修复的问题:")
    print("  1. ✅ Setting模型字段不匹配")
    print("     - 添加了category, key, value, description字段")
    print("     - 修复了to_dict()方法")
    print("     - 迁移了旧数据")
    
    print("  2. ✅ 管理员权限系统")
    print("     - 修复了权限检查逻辑")
    print("     - 更新了所有管理员的权限配置")
    print("     - 统一了权限名称")
    
    print("  3. ✅ 微信支付配置模型")
    print("     - 添加了name字段")
    print("     - 修复了to_dict()方法")
    print("     - 添加了权限检查")
    
    print("  4. ✅ 支付订单端点")
    print("     - 添加了缺少的导入")
    print("     - 修复了查询逻辑")
    print("     - 添加了错误处理")
    
    print("  5. ✅ 支付配置")
    print("     - 更新了价格为13.88")
    print("     - 启用了首次免费下载")
    print("     - 启用了VIP免费下载")

def main():
    """主函数"""
    print("🔧 管理员系统修复验证工具")
    print("=" * 80)
    
    # 测试各个组件
    tests = [
        ("Setting模型", test_setting_model),
        ("管理员权限", test_admin_permissions),
        ("微信支付配置", test_wechat_pay_config),
        ("支付配置", test_payment_config),
        ("数据库表", test_database_tables),
    ]
    
    results = {}
    for test_name, test_func in tests:
        results[test_name] = test_func()
    
    # 显示修复总结
    show_fix_summary()
    
    print("\n" + "=" * 80)
    print("🎯 测试结果:")
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    all_passed = all(results.values())
    
    if all_passed:
        print("\n🎉 所有测试通过！管理员系统修复成功！")
        print("\n现在应该能够:")
        print("  • 正常加载管理员设置页面")
        print("  • 保存微信支付配置")
        print("  • 查看支付订单列表")
        print("  • 访问论文信息")
        print("  • 使用所有管理员功能")
    else:
        print("\n⚠️ 部分测试失败，请检查错误信息")
    
    print("\n🔄 请重启服务器以确保所有修复生效")

if __name__ == "__main__":
    main()
