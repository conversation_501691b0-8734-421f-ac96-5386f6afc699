#!/usr/bin/env node
// -*- js -*-

var args = process.argv.slice(2);

function squeeze_out(css_in) {
    process.stdout.write( require("cssmin").cssmin(css_in) );
}

if (args.length) {
    require("fs").readFile( args[0], "utf8", function(err, css_in) {
        if (err) {
            throw err;
        }
        else {
            squeeze_out(css_in);
        }
    } );
}
else {
    var stdin = process.openStdin();
    stdin.setEncoding("utf8");
    var css_in = "";
    stdin.on("end", function() { squeeze_out(css_in) });
    stdin.on("data", function(chunk) { css_in += chunk });
}
