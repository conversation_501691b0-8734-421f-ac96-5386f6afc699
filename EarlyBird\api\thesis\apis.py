from EarlyBird.api.thesis import thesis_blueprint
import logging
import os
from flask import jsonify, g, request, send_from_directory
from flask_pydantic import validate
from EarlyBird.common.libs.api_result import ApiResponse
from EarlyBird.common import ApiResponse, Result
from EarlyBird.common.docx import THESIS_EXPORT_BASE
from EarlyBird.api.thesis.schema import (
    ParamThesisId,
    ParamSaveSingleParagraph,
    ParamSaveDigest,
    SaveNewPara,
    MovePara,
    DeleteParagraph,
    SaveThesisProperty,
    ParamPayDownload,
    ParamPaymentStatus,
    ParamConfirmPayment
)
from EarlyBird.api.thesis.service import ThesisServie


LOGGER = logging.getLogger(__name__)


@thesis_blueprint.route("/saveNewParagraph", methods=["POST"])
@validate()
def saveNewParagraph(body: SaveNewPara):
    try:
        body.userId = g.userid
        res: Result = ThesisServie().saveNewPara(body)
        if res.isSucc():
            return ApiResponse().json()
        return ApiResponse(data=[]).error(res.message).json()
    except Exception as e:
        LOGGER.exception(e)
        return ApiResponse().error(str(e)).json()


@thesis_blueprint.route("/paragraphMove", methods=["POST"])
@validate()
def paragraphMove(body: MovePara):
    try:
        body.userId = g.userid
        res: Result = ThesisServie().movelPara(body)
        if res.isSucc():
            return ApiResponse(data=res.data).json()
        return ApiResponse(data=[]).error(res.message).json()
    except Exception as e:
        LOGGER.exception(e)
        return ApiResponse().error(str(e)).json()
    

@thesis_blueprint.route("/paragraphDelete", methods=["POST"])
@validate()
def paragraphDelete(body: DeleteParagraph):
    try:
        body.userId = g.userid
        res: Result = ThesisServie().deletePara(body)
        if res.isSucc():
            return ApiResponse(data=res.data).json()
        return ApiResponse(data=[]).error(res.message).json()
    except Exception as e:
        LOGGER.exception(e)
        return ApiResponse().error(str(e)).json()


@thesis_blueprint.route("/getProgress", methods=["POST"])
@validate()
def getProgress(body: ParamThesisId):
    try:
        body.userId = g.userid
        res: Result = ThesisServie().getProgress(body)
        if res.isSucc():
            return ApiResponse(data=res.data).json()
        return ApiResponse(data=[]).error(res.message).json()
    except Exception as e:
        LOGGER.exception(e)
        return ApiResponse().error(str(e)).json()


@thesis_blueprint.route("/getList", methods=["POST"])
@validate()
def getList():
    try:
        # 检查用户是否已登录
        if not hasattr(g, 'userid') or not g.userid:
            LOGGER.warning("用户未登录或session失效，无法获取论文列表")
            return ApiResponse().needLogin().json()
            
        LOGGER.info(f"用户 {g.userid} 请求获取论文列表")
        res: Result = ThesisServie().getListByUid(g.userid)
        if res.isSucc():
            LOGGER.info(f"用户 {g.userid} 获取论文列表成功，共 {len(res.data)} 篇")
            return ApiResponse(data=res.data).json()
        return ApiResponse().error(res.message).json()

    except Exception as e:
        LOGGER.exception(e)
        return ApiResponse().error(str(e)).json()


@thesis_blueprint.route("/getDetail", methods=["POST"])
@validate()
def detail(body: ParamThesisId):

    body.userId = g.userid
    res: Result = ThesisServie().getDetailById(body)
    if not res.is_success():
        return ApiResponse().error(res.message).json()

    thesis = res.data

    return ApiResponse().set_data(thesis.to_json()).json()


@thesis_blueprint.route("/getOutline", methods=["POST"])
@validate()
def getOutline(body: ParamThesisId):

    body.userId = g.userid
    res: Result = ThesisServie().getOutlineProgressById(body)
    if not res.is_success():
        return ApiResponse().error(res.message).json()

    return ApiResponse().set_data(res.data).json()


@thesis_blueprint.route("/deleteThesis", methods=["POST"])
@validate()
def deleteThesis(body: ParamThesisId):
    body.userId = g.userid
    res: Result = ThesisServie().deleteThesis(body)
    if not res.is_success():
        return ApiResponse().error(res.message).json()

    return ApiResponse().json()


@thesis_blueprint.route("/downloadThesis", methods=["POST"])
@validate()
def downloadThesis(body: ParamThesisId):
    body.userId = g.userid
    LOGGER.info(f"用户 {body.userId} 请求下载论文 {body.thesisId}")
    res: Result = ThesisServie().exportThesis(body)
    
    # 检查是否需要支付
    if not res.is_success() and res.data and isinstance(res.data, dict) and res.data.get("need_payment"):
        # 需要支付，返回成功状态但包含支付信息，让前端根据need_payment字段判断
        LOGGER.info(f"用户 {body.userId} 需要支付才能下载论文 {body.thesisId}，价格: {res.data.get('price', 10.0)}")
        return ApiResponse().success().set_data(res.data).json()
    elif not res.is_success():
        # 其他错误
        LOGGER.error(f"用户 {body.userId} 下载论文 {body.thesisId} 失败: {res.message}")
        return ApiResponse().error(res.message).json()

    # 下载成功
    LOGGER.info(f"用户 {body.userId} 下载论文 {body.thesisId} 成功")
    return ApiResponse().success().set_data(res.data).json()


@thesis_blueprint.route("/payDownload", methods=["POST"])
@validate()
def payDownload(body: ParamPayDownload):
    body.userId = g.userid
    res: Result = ThesisServie().payDownload(body)
    if not res.is_success():
        return ApiResponse().error(res.message).json()

    return ApiResponse().set_data(res.data).json()


@thesis_blueprint.route("/confirmPayment", methods=["POST"])
@validate()
def confirmPayment(body: ParamConfirmPayment):
    """确认支付并下载论文"""
    body.userId = g.userid
    LOGGER.info(f"用户 {body.userId} 确认支付订单 {body.orderId} 并下载论文 {body.thesisId}")
    
    res: Result = ThesisServie().confirmPaymentAndDownload(body)
    if not res.is_success():
        LOGGER.warning(f"用户 {body.userId} 确认支付失败: {res.message}")
        return ApiResponse().error(res.message).json()

    LOGGER.info(f"用户 {body.userId} 确认支付成功，开始下载论文 {body.thesisId}")
    return ApiResponse().set_data(res.data).json()


@thesis_blueprint.route("/paymentStatus", methods=["POST"])
@validate()
def paymentStatus(body: ParamPaymentStatus):
    body.userId = g.userid
    res: Result = ThesisServie().getPaymentStatus(body)
    if not res.is_success():
        return ApiResponse().error(res.message).json()

    return ApiResponse().set_data(res.data).json()


@thesis_blueprint.route("/download", methods=["GET"])
def download():
    fileName = request.values.get("fileName")
    if not fileName:
        return ApiResponse().error("filename is required").json()

    # Get base directory and construct download path
    base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    download_dir = os.path.join(base_dir, "resources", "download")
    file_path = os.path.join(download_dir, fileName)

    if not os.path.exists(file_path):
        return ApiResponse().error("file not found").json()

    return send_from_directory(download_dir, fileName, as_attachment=True)


@thesis_blueprint.route("/saveSingleParagraph", methods=["POST"])
@validate()
def saveSingleParagraph(body: ParamSaveSingleParagraph):
    body.userId = g.userid
    res: Result = ThesisServie().saveSingleParagraph(body)
    if not res.is_success():
        return ApiResponse().error(res.message).json()

    return ApiResponse().json()


@thesis_blueprint.route("/saveDigest", methods=["POST"])
@validate()
def saveDigest(body: ParamSaveDigest):
    body.userId = g.userid
    res: Result = ThesisServie().saveDigest(body)
    if not res.is_success():
        return ApiResponse().error(res.message).json()

    return ApiResponse().json()


@thesis_blueprint.route("/saveReference", methods=["POST"])
@validate()
def saveReference(body: SaveThesisProperty):
    body.propName = 'references'
    body.userId = g.userid
    res: Result = ThesisServie().saveThesisProperty(body)
    if not res.is_success():
        return ApiResponse().error(res.message).json()

    return ApiResponse().json()


@thesis_blueprint.route("/saveThanks", methods=["POST"])
@validate()
def saveThanks(body: SaveThesisProperty):
    body.propName = 'thanks'
    body.userId = g.userid
    res: Result = ThesisServie().saveThesisProperty(body)
    if not res.is_success():
        return ApiResponse().error(res.message).json()

    return ApiResponse().json()
