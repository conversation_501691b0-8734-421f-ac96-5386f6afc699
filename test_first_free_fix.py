#!/usr/bin/env python3
"""
测试首次免费下载修复
验证新用户免费下载功能
"""

import sys
import os
import requests
import json

def test_payment_config():
    """测试支付配置"""
    print("🔍 测试支付配置...")
    
    try:
        import pymysql
        from EarlyBird.config.config import AppConfig
        
        # 解析数据库连接信息
        db_uri = AppConfig.SQLALCHEMY_DATABASE_URI
        
        # 从URI中提取连接信息
        import re
        match = re.match(r'mysql\+pymysql://([^:]+):([^@]+)@([^:]+):(\d+)/([^?]+)', db_uri)
        if not match:
            print("❌ 无法解析数据库连接URI")
            return False
        
        username, password, host, port, database = match.groups()
        port = int(port)
        
        # 连接数据库
        connection = pymysql.connect(
            host=host,
            port=port,
            user=username,
            password=password,
            database=database,
            charset='utf8mb4'
        )
        
        cursor = connection.cursor()
        
        # 查询支付配置
        cursor.execute("""
            SELECT config_key, config_value, is_deleted
            FROM earlybird_paper_payment_config 
            WHERE config_key IN (
                'thesis.download.is_active',
                'thesis.download.price', 
                'thesis.download.first_free',
                'thesis.download.vip_free'
            )
            ORDER BY config_key
        """)
        
        configs = cursor.fetchall()
        
        print(f"📋 当前支付配置:")
        config_dict = {}
        for config in configs:
            config_key, config_value, is_deleted = config
            status = "❌ 已删除" if is_deleted else "✅ 有效"
            print(f"  {config_key}: '{config_value}' ({status})")
            if not is_deleted:
                config_dict[config_key] = config_value
        
        # 验证配置
        expected_configs = {
            'thesis.download.is_active': 'true',
            'thesis.download.price': '13.88',
            'thesis.download.first_free': 'true',
            'thesis.download.vip_free': 'true'
        }
        
        print(f"\n🎯 配置验证:")
        all_correct = True
        for key, expected_value in expected_configs.items():
            actual_value = config_dict.get(key)
            if actual_value == expected_value:
                print(f"  ✅ {key}: {actual_value}")
            else:
                print(f"  ❌ {key}: 期望'{expected_value}', 实际'{actual_value}'")
                all_correct = False
        
        cursor.close()
        connection.close()
        
        return all_correct
        
    except Exception as e:
        print(f"❌ 测试配置失败: {str(e)}")
        return False

def test_user_records():
    """测试用户记录"""
    print("\n🔍 测试用户记录...")
    
    try:
        import pymysql
        from EarlyBird.config.config import AppConfig
        
        # 解析数据库连接信息
        db_uri = AppConfig.SQLALCHEMY_DATABASE_URI
        
        # 从URI中提取连接信息
        import re
        match = re.match(r'mysql\+pymysql://([^:]+):([^@]+)@([^:]+):(\d+)/([^?]+)', db_uri)
        if not match:
            print("❌ 无法解析数据库连接URI")
            return False
        
        username, password, host, port, database = match.groups()
        port = int(port)
        
        # 连接数据库
        connection = pymysql.connect(
            host=host,
            port=port,
            user=username,
            password=password,
            database=database,
            charset='utf8mb4'
        )
        
        cursor = connection.cursor()
        
        # 查询用户14的下载记录
        cursor.execute("""
            SELECT id, thesis_id, is_paid, price, payment_method, 
                   payment_order_id, create_time
            FROM earlybird_paper_thesis_download_record 
            WHERE uid = 14
            ORDER BY create_time DESC
        """)
        
        records = cursor.fetchall()
        
        print(f"📋 用户14的下载记录 (总数: {len(records)}):")
        paid_count = 0
        for record in records:
            record_id, thesis_id, is_paid, price, payment_method, payment_order_id, create_time = record
            paid_status = "✅ 已支付" if is_paid else "❌ 未支付"
            if is_paid:
                paid_count += 1
            print(f"  记录{record_id}: 论文{thesis_id}, {paid_status}, 价格{price}, 方式{payment_method}")
            print(f"    订单: {payment_order_id}, 时间: {create_time}")
        
        print(f"\n🎯 统计结果:")
        print(f"  总记录数: {len(records)}")
        print(f"  已支付记录数: {paid_count}")
        print(f"  是否为新用户: {'✅ 是' if paid_count == 0 else '❌ 否'}")
        
        cursor.close()
        connection.close()
        
        return paid_count == 0  # 新用户应该没有已支付记录
        
    except Exception as e:
        print(f"❌ 测试用户记录失败: {str(e)}")
        return False

def show_debug_expectations():
    """显示调试期望"""
    print("\n📝 调试日志期望:")
    print("=" * 60)
    
    print("🔍 首次下载免费配置查询结果:")
    print("  配置值: 'true' (类型: <class 'str'>)")
    print("  is_deleted: False")
    print("  config_key: thesis.download.first_free")
    print("  配置值处理: 'true' -> 'true' -> True")
    print("🎯 首次下载是否免费: True")
    
    print("\n🔍 用户 14 下载记录检查:")
    print("  是否有任何下载记录: False")
    print("  总下载记录数: 0")
    
    print("\n🎯 首次免费判断:")
    print("  first_free配置: True")
    print("  any_download_record: False")
    print("  判断条件: first_free=True AND not any_download_record=True")
    print("  最终判断: True")
    
    print("\n✅ 用户 14 首次下载论文 40，免费")

def show_expected_api_response():
    """显示期望的API响应"""
    print("\n📱 期望的API响应:")
    print("=" * 60)
    
    print("❌ 修复前 (错误):")
    print(json.dumps({
        "is_success": True,
        "data": {
            "need_payment": True,
            "price": 10,
            "reason": "需要支付下载费用"
        }
    }, ensure_ascii=False, indent=2))
    
    print("\n✅ 修复后 (正确):")
    print(json.dumps({
        "is_success": True,
        "data": {
            "file": "/resources/download/[早鸟论文]_论文标题_V时间戳.docx",
            "free_reason": "first_free",
            "message": "首次下载免费，已为您创建下载记录"
        }
    }, ensure_ascii=False, indent=2))

def show_fixes_summary():
    """显示修复总结"""
    print("\n🔧 修复总结:")
    print("=" * 60)
    
    print("1️⃣ 首次免费检测修复:")
    print("  ✅ 添加详细的配置读取日志")
    print("  ✅ 改进配置值判断逻辑（处理空格和大小写）")
    print("  ✅ 添加下载记录检查的详细日志")
    print("  ✅ 修复测试记录的识别逻辑")
    
    print("\n2️⃣ 下载计数修复:")
    print("  ✅ 只统计已支付的记录 (is_paid=True)")
    print("  ✅ 添加支付取消时的清理逻辑")
    print("  ✅ 区分总记录数和已支付记录数")
    
    print("\n3️⃣ 价格配置修复:")
    print("  ✅ 添加价格配置读取的详细日志")
    print("  ✅ 确保读取数据库中的实际配置值")
    print("  ✅ 处理配置不存在的情况")
    
    print("\n4️⃣ 调试增强:")
    print("  ✅ 添加详细的决策过程日志")
    print("  ✅ 记录每个判断条件的值")
    print("  ✅ 提供清晰的错误诊断信息")

def main():
    """主函数"""
    print("🔧 首次免费下载修复验证")
    print("=" * 80)
    
    # 1. 测试支付配置
    config_ok = test_payment_config()
    
    # 2. 测试用户记录
    user_ok = test_user_records()
    
    # 3. 显示调试期望
    show_debug_expectations()
    
    # 4. 显示期望的API响应
    show_expected_api_response()
    
    # 5. 显示修复总结
    show_fixes_summary()
    
    print("\n" + "=" * 80)
    print("🎯 测试结果:")
    print(f"  配置正确性: {'✅ 通过' if config_ok else '❌ 失败'}")
    print(f"  用户状态: {'✅ 新用户' if user_ok else '❌ 非新用户'}")
    
    if config_ok and user_ok:
        print("\n🎉 所有条件满足，首次免费下载应该生效！")
    else:
        print("\n⚠️ 存在问题，需要进一步检查配置或用户状态")
    
    print("\n🔄 请重启服务器并测试:")
    print("  1. 访问论文页面")
    print("  2. 点击下载按钮")
    print("  3. 检查服务器日志中的详细调试信息")
    print("  4. 验证是否直接下载而不要求支付")

if __name__ == "__main__":
    main()
