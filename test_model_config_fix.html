<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI模型配置修复指南</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .step-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .step-section h3 {
            color: #666;
            margin-top: 0;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-weight: bold;
        }
        .status.success {
            background-color: #f0f9ff;
            border: 1px solid #67c23a;
            color: #67c23a;
        }
        .status.error {
            background-color: #fef0f0;
            border: 1px solid #f56c6c;
            color: #f56c6c;
        }
        .status.warning {
            background-color: #fdf6ec;
            border: 1px solid #e6a23c;
            color: #e6a23c;
        }
        .code-block {
            background-color: #f8f8f8;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            overflow-x: auto;
        }
        .highlight {
            background-color: #fff3cd;
            padding: 2px 4px;
            border-radius: 3px;
        }
        .problem-item {
            background-color: #fff2f0;
            border-left: 4px solid #ff4d4f;
            padding: 10px;
            margin: 10px 0;
        }
        .solution-item {
            background-color: #f6ffed;
            border-left: 4px solid #52c41a;
            padding: 10px;
            margin: 10px 0;
        }
        .step-number {
            display: inline-block;
            width: 30px;
            height: 30px;
            background-color: #409EFF;
            color: white;
            border-radius: 50%;
            text-align: center;
            line-height: 30px;
            margin-right: 10px;
            font-weight: bold;
        }
        .link-button {
            display: inline-block;
            background-color: #409EFF;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 4px;
            margin: 10px 5px;
        }
        .link-button:hover {
            background-color: #66b1ff;
            color: white;
            text-decoration: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 AI模型配置修复指南</h1>
        
        <div class="step-section">
            <h3>📋 问题描述</h3>
            <div class="problem-item">
                <strong>错误信息：</strong>
                <code>Exception: 未知的模型类型: 默认模型</code>
            </div>
            <div class="problem-item">
                <strong>问题原因：</strong>
                后台管理系统的模型配置页面没有读取到配置项，导致新用户无法正常使用AI功能
            </div>
        </div>

        <div class="step-section">
            <h3>🛠️ 修复步骤</h3>
            
            <div class="solution-item">
                <span class="step-number">1</span>
                <strong>访问管理员模型配置页面</strong>
                <p>点击下面的链接访问模型配置页面：</p>
                <a href="http://127.0.0.1:3301/admin/settings/model" target="_blank" class="link-button">
                    🔗 打开模型配置页面
                </a>
            </div>
            
            <div class="solution-item">
                <span class="step-number">2</span>
                <strong>设置默认模型</strong>
                <p>在"当前模型"下拉框中选择：<span class="highlight">千问 (qianwen)</span></p>
            </div>
            
            <div class="solution-item">
                <span class="step-number">3</span>
                <strong>配置API Key</strong>
                <p>在"千问API Key"输入框中输入你的API Key</p>
                <p>格式通常是：<code>sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx</code></p>
            </div>
            
            <div class="solution-item">
                <span class="step-number">4</span>
                <strong>保存配置</strong>
                <p>点击"保存大模型密钥"按钮保存配置</p>
            </div>
            
            <div class="solution-item">
                <span class="step-number">5</span>
                <strong>测试功能</strong>
                <p>注册一个新账号，测试AI功能是否正常工作</p>
            </div>
        </div>

        <div class="step-section">
            <h3>🎯 代码修复内容</h3>
            <p>我已经修复了以下代码问题：</p>
            
            <div class="solution-item">
                <strong>✅ HomeService.getModelName() 修复</strong>
                <p>当数据库中没有配置时，返回有效的"qianwen"而不是"默认模型"</p>
            </div>
            
            <div class="solution-item">
                <strong>✅ AI适配器容错处理</strong>
                <p>当遇到"默认模型"或未知模型时，自动使用千问模型</p>
            </div>
            
            <div class="solution-item">
                <strong>✅ HomeService.getSetting() 增强</strong>
                <p>返回完整的API Key配置信息，确保前端能正确显示</p>
            </div>
        </div>

        <div class="step-section">
            <h3>📝 预期结果</h3>
            
            <h4>✅ 修复后应该看到：</h4>
            <div class="code-block">INFO  Using model: qianwen
INFO  使用模型: qianwen
INFO  使用千问 API Key: sk-xxxxx...</div>

            <h4>❌ 不应该再看到：</h4>
            <div class="code-block">ERROR  未知的模型类型: 默认模型
Exception: 未知的模型类型: 默认模型</div>
        </div>

        <div class="step-section">
            <h3>🔍 问题排查</h3>
            <p>如果仍然有问题，请检查：</p>
            <ul>
                <li><strong>API Key有效性</strong>：确保千问API Key是有效的</li>
                <li><strong>网络连接</strong>：确保服务器能访问千问API</li>
                <li><strong>数据库连接</strong>：确保MySQL数据库连接正常</li>
                <li><strong>服务器重启</strong>：配置修改后重启服务器</li>
            </ul>
        </div>

        <div class="step-section">
            <h3>🎯 快速链接</h3>
            <a href="http://127.0.0.1:3301/admin/login" target="_blank" class="link-button">
                🔐 管理员登录
            </a>
            <a href="http://127.0.0.1:3301/admin/settings/model" target="_blank" class="link-button">
                ⚙️ 模型配置
            </a>
            <a href="http://127.0.0.1:3301/admin/settings" target="_blank" class="link-button">
                📊 系统设置
            </a>
        </div>

        <div class="step-section">
            <h3>📊 修复状态</h3>
            <div class="status success">
                ✅ 后端代码修复完成
            </div>
            <div class="status warning">
                ⚠️ 需要手动配置模型设置
            </div>
            <div class="status warning">
                ⚠️ 建议重启服务器确保修改生效
            </div>
        </div>
    </div>

    <script>
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔧 AI模型配置修复指南页面已加载');
            console.log('📋 请按照步骤进行配置');
        });
    </script>
</body>
</html>
