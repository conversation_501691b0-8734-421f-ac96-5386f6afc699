<!--
作者名：EarlyBird
作者邮箱：<EMAIL>
官方网址：https://blog.zaoniao.vip

微信支付对话框组件
-->
<template>
  <el-dialog
    title="微信支付"
    :visible.sync="dialogVisible"
    width="400px"
    :before-close="handleClose"
    center
  >
    <div class="payment-content">
      <div class="payment-amount">
        <div class="amount-label">支付金额</div>
        <div class="amount-value">¥{{ amount.toFixed(2) }}</div>
      </div>
      
      <div class="qr-code-container">
        <img v-if="qrCodeUrl" :src="qrCodeUrl" alt="微信支付二维码" class="qr-code" />
        <div v-else class="qr-code-placeholder">
          <i class="el-icon-loading"></i>
          <p>正在生成支付二维码...</p>
        </div>
        <p class="qr-tip">请使用微信扫描二维码完成支付</p>
      </div>
    </div>
    
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消支付</el-button>
      <el-button type="primary" @click="checkPaymentStatus">支付完成</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { payThesisDownload, getThesisPaymentStatus, confirmPayment } from '@/api/thesis'

export default {
  name: 'WeChatPayDialog',
  
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    amount: {
      type: Number,
      default: 10.00
    },
    thesisId: {
      type: [Number, String],
      required: true
    }
  },
  
  data() {
    return {
      dialogVisible: false,
      qrCodeUrl: '',
      orderId: '',
      paymentInterval: null,
      isPaymentProcessing: false,
      retryCount: 0,
      maxRetries: 3,
      isInitializing: false  // 添加初始化状态标志
    }
  },
  
  watch: {
    visible(val) {
      this.dialogVisible = val
      if (val) {
        // 重置状态
        this.retryCount = 0
        this.isInitializing = false

        // 延迟初始化，确保弹窗完全显示后再初始化支付
        this.$nextTick(() => {
          setTimeout(() => {
            // 确保弹窗仍然显示且有论文ID才初始化
            if (this.dialogVisible && this.thesisId && !this.isInitializing) {
              console.log('弹窗显示，开始初始化支付，论文ID:', this.thesisId)
              this.initPayment()
            }
          }, 200) // 增加延迟时间确保弹窗稳定
        })
      } else {
        this.clearPaymentInterval()
        this.isInitializing = false  // 重置初始化状态
        this.retryCount = 0
      }
    },
    thesisId(val, oldVal) {
      // 只有在弹窗显示且论文ID真正变化时才重新初始化
      // 避免从 undefined 到实际值的初始化触发
      if (this.dialogVisible && val && oldVal && val !== oldVal && !this.isInitializing) {
        console.log('论文ID已更改，重新初始化支付', val, '(旧值:', oldVal, ')')
        // 清除之前的状态
        this.clearPaymentInterval()
        this.qrCodeUrl = ''
        this.orderId = ''

        // 延迟一点时间再初始化，确保弹窗已经稳定显示
        setTimeout(() => {
          if (this.dialogVisible && !this.isInitializing) {
            this.initPayment()
          }
        }, 300)
      }
    }
  },
  
  methods: {
    async initPayment() {
      try {
        // 防止重复初始化
        if (this.isInitializing) {
          console.log('支付初始化正在进行中，跳过重复调用')
          return
        }

        this.isInitializing = true

        // 重置状态
        this.qrCodeUrl = ''
        this.orderId = ''
        // 不重置 retryCount，保持重试计数

        console.log('初始化支付，论文ID:', this.thesisId)

        if (!this.thesisId) {
          console.error('缺少论文ID')
          this.$message.error('缺少论文ID，无法创建支付订单')
          this.handleClose()
          return
        }
        
        // 显示加载状态
        const loading = this.$loading({
          lock: true,
          text: '正在创建支付订单...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        })
        
        try {
          // 调用创建支付订单API
          const res = await payThesisDownload({
            thesisId: this.thesisId,
            paymentMethod: 'wechat'
          })

          loading.close()
          this.isInitializing = false  // 重置初始化状态
          console.log('支付订单API响应:', res)

          if (res && (res.success || res.is_success) && res.data) {
            this.orderId = res.data.order_id
            // 使用API返回的二维码URL，如果没有则使用默认图片
            this.qrCodeUrl = res.data.qr_code_url || require('@/assets/images/qrcode-wechat.png')

            // 开始轮询支付状态
            this.startPaymentStatusPolling()
          } else {
            this.$message.error('创建支付订单失败：' + (res.message || '未知错误'))
            this.handleClose()
          }
        } catch (error) {
          loading.close()
          this.isInitializing = false  // 重置初始化状态
          console.error('创建支付订单请求失败:', error)

          // 如果是请求被取消，不关闭弹窗，而是重试
          if (error.message && (error.message.includes('请求被取消') || error.message.includes('canceled') || error.message.includes('aborted'))) {
            console.log('请求被取消，尝试重新初始化支付')
            // 增加重试次数限制
            if (this.retryCount < this.maxRetries) {
              this.retryCount++
              console.log(`第 ${this.retryCount} 次重试初始化支付，延迟 ${2000 * this.retryCount}ms`)
              setTimeout(() => {
                if (this.dialogVisible && !this.isInitializing && this.thesisId) {
                  this.initPayment()
                }
              }, 2000 * this.retryCount) // 使用固定的2秒递增延迟
            } else {
              console.log('重试次数已达上限，停止重试')
              this.$message.error('网络连接不稳定，请关闭弹窗后重新尝试')
              // 不自动关闭弹窗，让用户手动关闭
            }
          } else {
            this.$message.error('创建支付订单失败：' + (error.message || '未知错误'))
            // 延迟关闭，给用户时间看到错误信息
            setTimeout(() => {
              this.handleClose()
            }, 2000)
          }
        }
      } catch (error) {
        this.isInitializing = false  // 重置初始化状态
        console.error('初始化支付失败:', error)
        this.$message.error('创建支付订单失败，请稍后重试')
        this.handleClose()
      }
    },
    
    startPaymentStatusPolling() {
      // 清除可能存在的定时器
      this.clearPaymentInterval()
      
      // 每3秒查询一次支付状态
      this.paymentInterval = setInterval(async () => {
        await this.checkPaymentStatus(true)
      }, 3000)
    },
    
    clearPaymentInterval() {
      if (this.paymentInterval) {
        clearInterval(this.paymentInterval)
        this.paymentInterval = null
      }
    },
    
    async checkPaymentStatus(isAutoCheck = false) {
      if (this.isPaymentProcessing) return
      
      try {
        this.isPaymentProcessing = true
        
        // 如果不是自动检查，显示加载提示
        let loading = null
        if (!isAutoCheck) {
          loading = this.$loading({
            lock: true,
            text: '正在检查支付状态...',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          })
        }
        
        // 调用查询支付状态API
        const res = await getThesisPaymentStatus({
          thesisId: this.thesisId,
          orderId: this.orderId
        })
        
        if (loading) loading.close()
        
        console.log('支付状态API响应:', res)
        
        if (res && (res.success || res.is_success) && res.data) {
          if (res.data.is_paid || res.data.payment_status === 'paid') {
            // 支付成功
            this.clearPaymentInterval()
            
            if (!isAutoCheck) {
              this.$message.success('支付成功')
            }
            
            // 确认支付并下载论文
            await this.confirmPaymentAndDownload()
            
            // 通知父组件支付成功
            this.$emit('payment-success', {
              orderId: this.orderId,
              amount: this.amount,
              thesisId: this.thesisId
            })
            
            // 关闭对话框
            this.handleClose()
          } else if (!isAutoCheck) {
            // 如果是手动检查且未支付，提示用户
            this.$message.info('支付尚未完成，请扫码支付或稍后再试')
          }
        } else if (!isAutoCheck) {
          this.$message.error('查询支付状态失败：' + (res && res.message ? res.message : '未知错误'))
        }
      } catch (error) {
        console.error('查询支付状态失败:', error)
        if (!isAutoCheck) {
          this.$message.error('查询支付状态失败，请稍后再试')
        } else if (this.retryCount < this.maxRetries) {
          // 自动查询失败时，增加重试计数但不中断轮询
          this.retryCount++
          console.log(`自动查询支付状态失败，已重试 ${this.retryCount}/${this.maxRetries} 次`)
        } else if (this.retryCount >= this.maxRetries) {
          // 超过最大重试次数，停止轮询
          console.error('查询支付状态失败次数过多，停止自动查询')
          this.clearPaymentInterval()
        }
      } finally {
        this.isPaymentProcessing = false
      }
    },
    
    async confirmPaymentAndDownload() {
      try {
        console.log('确认支付并准备下载论文')
        
        const loading = this.$loading({
          lock: true,
          text: '正在确认支付并准备下载...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        })
        
        // 调用确认支付API
        const res = await confirmPayment({
          thesisId: this.thesisId,
          orderId: this.orderId
        })
        
        loading.close()
        
        if (res && (res.success || res.is_success) && res.data && res.data.file) {
          console.log('确认支付成功，开始下载论文')
          
          // 下载文件
          let link = document.createElement("a")
          link.style.display = "none"
          link.href = "/api/thesis/download?fileName=" + res.data.file
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)
          
          return true
        } else {
          console.error('确认支付失败或无法获取下载文件')
          this.$message.error('确认支付成功，但无法下载论文，请稍后在"我的论文"中重试下载')
          return false
        }
      } catch (error) {
        console.error('确认支付并下载论文失败:', error)
        this.$message.error('确认支付失败，请稍后在"我的论文"中重试下载')
        return false
      }
    },
    
    handleClose() {
      console.log('微信支付弹窗关闭请求')

      // 如果正在初始化，询问用户是否确认关闭
      if (this.isInitializing) {
        console.log('正在初始化支付，询问用户是否确认关闭')
        this.$confirm('支付正在初始化中，确定要关闭吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.forceClose()
        }).catch(() => {
          console.log('用户取消关闭弹窗')
        })
        return
      }

      this.forceClose()
    },

    forceClose() {
      console.log('强制关闭微信支付弹窗')
      this.clearPaymentInterval()
      this.isInitializing = false
      this.retryCount = 0
      this.qrCodeUrl = ''
      this.orderId = ''
      this.dialogVisible = false
      this.$emit('update:visible', false)
      this.$emit('close')
    }
  },
  
  beforeDestroy() {
    // 确保组件销毁时清除定时器
    this.clearPaymentInterval()
  }
}
</script>

<style scoped>
.payment-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10px 0;
}

.payment-amount {
  text-align: center;
  margin-bottom: 20px;
}

.amount-label {
  font-size: 14px;
  color: #606266;
  margin-bottom: 5px;
}

.amount-value {
  font-size: 24px;
  font-weight: bold;
  color: #f56c6c;
}

.qr-code-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.qr-code {
  width: 200px;
  height: 200px;
  margin-bottom: 10px;
}

.qr-code-placeholder {
  width: 200px;
  height: 200px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: #f5f7fa;
  border-radius: 4px;
  margin-bottom: 10px;
}

.qr-code-placeholder i {
  font-size: 40px;
  color: #409EFF;
  margin-bottom: 10px;
}

.qr-tip {
  font-size: 14px;
  color: #606266;
  margin-top: 10px;
}
</style> 