#!/usr/bin/env python3
"""
调试支付系统的三个关键问题
1. 新用户免费下载不生效
2. 取消支付后下载计数错误
3. 前后端价格不匹配
"""

import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置环境变量
os.environ["FLASK_ENV"] = "production"

def debug_payment_config():
    """调试支付配置"""
    try:
        import pymysql
        from EarlyBird.config.config import AppConfig
        
        # 解析数据库连接信息
        db_uri = AppConfig.SQLALCHEMY_DATABASE_URI
        
        # 从URI中提取连接信息
        import re
        match = re.match(r'mysql\+pymysql://([^:]+):([^@]+)@([^:]+):(\d+)/([^?]+)', db_uri)
        if not match:
            print("❌ 无法解析数据库连接URI")
            return False
        
        username, password, host, port, database = match.groups()
        port = int(port)
        
        # 连接数据库
        connection = pymysql.connect(
            host=host,
            port=port,
            user=username,
            password=password,
            database=database,
            charset='utf8mb4'
        )
        
        cursor = connection.cursor()
        
        print("🔍 调试支付配置...")
        
        # 查询支付配置
        cursor.execute("""
            SELECT config_key, config_value, is_deleted, create_time, update_time
            FROM earlybird_paper_payment_config 
            WHERE config_key LIKE 'thesis.download.%'
            ORDER BY config_key
        """)
        
        configs = cursor.fetchall()
        
        print(f"📋 支付配置查询结果:")
        for config in configs:
            config_key, config_value, is_deleted, create_time, update_time = config
            status = "❌ 已删除" if is_deleted else "✅ 有效"
            print(f"  {config_key}: {config_value} ({status})")
            print(f"    创建时间: {create_time}, 更新时间: {update_time}")
        
        cursor.close()
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ 调试配置失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def debug_user_download_records():
    """调试用户下载记录"""
    try:
        import pymysql
        from EarlyBird.config.config import AppConfig
        
        # 解析数据库连接信息
        db_uri = AppConfig.SQLALCHEMY_DATABASE_URI
        
        # 从URI中提取连接信息
        import re
        match = re.match(r'mysql\+pymysql://([^:]+):([^@]+)@([^:]+):(\d+)/([^?]+)', db_uri)
        if not match:
            print("❌ 无法解析数据库连接URI")
            return False
        
        username, password, host, port, database = match.groups()
        port = int(port)
        
        # 连接数据库
        connection = pymysql.connect(
            host=host,
            port=port,
            user=username,
            password=password,
            database=database,
            charset='utf8mb4'
        )
        
        cursor = connection.cursor()
        
        print("\n🔍 调试用户下载记录...")
        
        # 查询用户14的下载记录
        cursor.execute("""
            SELECT id, uid, thesis_id, is_paid, price, payment_method, 
                   payment_order_id, payment_time, create_time
            FROM earlybird_paper_thesis_download_record 
            WHERE uid = 14
            ORDER BY create_time DESC
            LIMIT 10
        """)
        
        records = cursor.fetchall()
        
        print(f"📋 用户14的下载记录:")
        if records:
            for record in records:
                record_id, uid, thesis_id, is_paid, price, payment_method, payment_order_id, payment_time, create_time = record
                paid_status = "✅ 已支付" if is_paid else "❌ 未支付"
                print(f"  记录ID: {record_id}")
                print(f"    论文ID: {thesis_id}, {paid_status}")
                print(f"    价格: {price}, 支付方式: {payment_method}")
                print(f"    订单号: {payment_order_id}")
                print(f"    支付时间: {payment_time}, 创建时间: {create_time}")
                print()
        else:
            print("  ✅ 用户14没有下载记录（符合新用户预期）")
        
        # 查询支付记录
        cursor.execute("""
            SELECT id, user_id, product_id, out_trade_no, status, amount, 
                   pay_time, create_time
            FROM earlybird_paper_payment 
            WHERE user_id = 14
            ORDER BY create_time DESC
            LIMIT 5
        """)
        
        payments = cursor.fetchall()
        
        print(f"📋 用户14的支付记录:")
        if payments:
            for payment in payments:
                payment_id, user_id, product_id, out_trade_no, status, amount, pay_time, create_time = payment
                status_text = "✅ 已支付" if status == 1 else "❌ 未支付"
                print(f"  支付ID: {payment_id}")
                print(f"    产品: {product_id}, {status_text}")
                print(f"    金额: {amount}, 订单号: {out_trade_no}")
                print(f"    支付时间: {pay_time}, 创建时间: {create_time}")
                print()
        else:
            print("  ✅ 用户14没有支付记录（符合新用户预期）")
        
        cursor.close()
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ 调试下载记录失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def debug_first_free_logic():
    """调试首次免费逻辑"""
    print("\n🔍 调试首次免费逻辑...")
    
    print("📋 首次免费判断条件:")
    print("  1. first_free配置为true")
    print("  2. user_download_count == 0")
    print("  3. any_download_record为None")
    
    print("\n🎯 可能的问题:")
    print("  ❌ 配置读取问题：config_value可能不是'true'")
    print("  ❌ 下载记录计数问题：可能包含了测试记录")
    print("  ❌ 记录检查逻辑问题：可能误判了下载记录")
    
    print("\n🔧 需要检查的地方:")
    print("  1. PaymentConfig表中thesis.download.first_free的值")
    print("  2. ThesisDownloadRecord表中用户14的记录数")
    print("  3. 首次免费判断逻辑是否正确")

def debug_download_count_issue():
    """调试下载计数问题"""
    print("\n🔍 调试下载计数问题...")
    
    print("📋 下载计数逻辑:")
    print("  user_download_count = ThesisDownloadRecord.query.filter_by(uid=userId).count()")
    
    print("\n🎯 可能的问题:")
    print("  ❌ 创建时机问题：在支付前就创建了记录")
    print("  ❌ 删除逻辑缺失：取消支付后没有删除记录")
    print("  ❌ 状态判断问题：计数包含了未支付的记录")
    
    print("\n🔧 修复方案:")
    print("  1. 只在确认支付成功后创建下载记录")
    print("  2. 取消支付时删除未完成的记录")
    print("  3. 计数时只统计is_paid=true的记录")

def debug_price_mismatch():
    """调试价格不匹配问题"""
    print("\n🔍 调试价格不匹配问题...")
    
    print("📋 价格读取逻辑:")
    print("  price_config = PaymentConfig.query.filter_by(")
    print("      config_key='thesis.download.price',")
    print("      is_deleted=False")
    print("  ).first()")
    print("  download_price = float(price_config.config_value)")
    
    print("\n🎯 可能的问题:")
    print("  ❌ 配置值类型问题：可能是字符串'13.88'")
    print("  ❌ 缓存问题：前端可能使用了旧的缓存值")
    print("  ❌ 默认值问题：配置不存在时使用了默认值10.0")
    
    print("\n🔧 需要验证:")
    print("  1. 数据库中的实际配置值")
    print("  2. 后端API返回的价格")
    print("  3. 前端显示的价格")

def main():
    """主函数"""
    print("🔧 支付系统问题调试工具")
    print("=" * 80)
    
    # 1. 调试支付配置
    debug_payment_config()
    
    # 2. 调试用户下载记录
    debug_user_download_records()
    
    # 3. 调试首次免费逻辑
    debug_first_free_logic()
    
    # 4. 调试下载计数问题
    debug_download_count_issue()
    
    # 5. 调试价格不匹配
    debug_price_mismatch()
    
    print("\n" + "=" * 80)
    print("📋 问题总结:")
    print("  1. 🔍 新用户免费下载不生效")
    print("     - 检查first_free配置和下载记录计数")
    print("  2. 🔍 取消支付后下载计数错误")
    print("     - 检查记录创建时机和删除逻辑")
    print("  3. 🔍 前后端价格不匹配")
    print("     - 检查配置读取和类型转换")
    
    print("\n🎯 下一步行动:")
    print("  1. 查看数据库中的实际配置值")
    print("  2. 检查用户14的下载记录状态")
    print("  3. 修复首次免费判断逻辑")
    print("  4. 修复下载记录创建时机")
    print("  5. 确保价格配置正确读取")

if __name__ == "__main__":
    main()
