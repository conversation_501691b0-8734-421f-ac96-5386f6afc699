2025-07-29 09:24:31  init_flask.py 27: INFO  {'CpuCount': 6, 'Mem': '32 GB'} [MainThread]
2025-07-29 09:24:31  web_server.py 32: INFO  Static directory: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\resources\ui [MainThread]
2025-07-29 09:24:31  web_server.py 33: INFO  Static directory exists: True [MainThread]
2025-07-29 09:24:31  web_server.py 37: INFO  设置工作目录: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird [MainThread]
2025-07-29 09:24:31  EarlyBird.config.config 21: INFO  load configFile at d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\config/production.ini [MainThread]
2025-07-29 09:24:31  config.py 21: INFO  load configFile at d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\config/production.ini [MainThread]
2025-07-29 09:24:31  config.py 94: INFO  Database URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-29 09:24:33  EarlyBird.api.admin.app 39: INFO  ✅ 后台管理系统API已注册 [MainThread]
2025-07-29 09:24:33  app.py 39: INFO  ✅ 后台管理系统API已注册 [MainThread]
2025-07-29 09:24:33  EarlyBird 40: INFO  确保数据目录存在: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\resources\data [MainThread]
2025-07-29 09:24:33  server.py 40: INFO  确保数据目录存在: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\resources\data [MainThread]
2025-07-29 09:24:33  EarlyBird 44: INFO  开始执行数据库迁移... [MainThread]
2025-07-29 09:24:33  server.py 44: INFO  开始执行数据库迁移... [MainThread]
2025-07-29 09:24:33  config.py 21: INFO  load configFile at d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\config/production.ini [MainThread]
2025-07-29 09:24:33  config.py 94: INFO  Database URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-29 09:24:33  EarlyBird 53: INFO  数据库连接URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-29 09:24:33  server.py 53: INFO  数据库连接URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-29 09:24:33  EarlyBird 97: WARNING  ChatLog表不存在，将在应用启动时自动创建 [MainThread]
2025-07-29 09:24:33  server.py 97: WARNING  ChatLog表不存在，将在应用启动时自动创建 [MainThread]
2025-07-29 09:24:33  add_uid_to_chatlog.py 20: INFO  开始执行ChatLog表迁移... [MainThread]
2025-07-29 09:24:33  add_uid_to_chatlog.py 24: INFO  数据库连接URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-29 09:24:33  add_uid_to_chatlog.py 38: WARNING  ChatLog表不存在，可能需要先创建表 [MainThread]
2025-07-29 09:24:33  task_api_server.py 31: INFO  TaskApiServer start [MainThread]
2025-07-29 09:24:33  task_paper_generator.py 38: INFO  论文生成任务处理线程初始化 [MainThread]
2025-07-29 09:24:33  task_paper_generator.py 72: INFO  论文生成任务处理线程启动 [Thread-2]
2025-07-29 09:24:33  task_api_server.py 47: INFO  TaskApiServer listen on 127.0.0.1:3302 [Thread-1]
2025-07-29 09:24:33  task_api_server.py 17: INFO  _ApiServerThread start [Thread-3]
2025-07-29 09:25:58  web_server.py 73: INFO  Serving request for path:  [waitress-0]
2025-07-29 09:25:58  web_server.py 85: INFO  Serving index.html for path:  [waitress-0]
2025-07-29 09:25:58  web_server.py 73: INFO  Serving request for path: js/chunk-vendors.04aff60f.js [waitress-1]
2025-07-29 09:25:58  web_server.py 82: INFO  Serving static file: js/chunk-vendors.04aff60f.js [waitress-1]
2025-07-29 09:25:58  web_server.py 73: INFO  Serving request for path: js/app.4ad3be7b.js [waitress-2]
2025-07-29 09:25:58  web_server.py 82: INFO  Serving static file: js/app.4ad3be7b.js [waitress-2]
2025-07-29 09:25:58  web_server.py 73: INFO  Serving request for path: css/chunk-vendors.d5efd04f.css [waitress-3]
2025-07-29 09:25:58  web_server.py 73: INFO  Serving request for path: css/app.1e68ddae.css [waitress-0]
2025-07-29 09:25:58  web_server.py 82: INFO  Serving static file: css/chunk-vendors.d5efd04f.css [waitress-3]
2025-07-29 09:25:58  web_server.py 82: INFO  Serving static file: css/app.1e68ddae.css [waitress-0]
2025-07-29 09:25:58  web_server.py 73: INFO  Serving request for path: css/389.ae47f7d6.css [waitress-1]
2025-07-29 09:25:58  web_server.py 73: INFO  Serving request for path: js/22.683bc166.js [waitress-2]
2025-07-29 09:25:58  web_server.py 82: INFO  Serving static file: css/389.ae47f7d6.css [waitress-1]
2025-07-29 09:25:58  web_server.py 73: INFO  Serving request for path: js/897.25207915.js [waitress-0]
2025-07-29 09:25:58  web_server.py 82: INFO  Serving static file: js/22.683bc166.js [waitress-2]
2025-07-29 09:25:58  web_server.py 73: INFO  Serving request for path: js/389.9dbcae13.js [waitress-3]
2025-07-29 09:25:58  web_server.py 82: INFO  Serving static file: js/897.25207915.js [waitress-0]
2025-07-29 09:25:58  web_server.py 82: INFO  Serving static file: js/389.9dbcae13.js [waitress-3]
2025-07-29 09:25:58  web_server.py 73: INFO  Serving request for path: img/logo.ce3a5bfd.png [waitress-1]
2025-07-29 09:25:58  web_server.py 82: INFO  Serving static file: img/logo.ce3a5bfd.png [waitress-1]
2025-07-29 09:25:58  service.py 70: INFO  获取设置成功，包含 8 个配置项 [waitress-2]
2025-07-29 09:25:58  web_server.py 73: INFO  Serving request for path: fonts/element-icons.ff18efd1.woff [waitress-0]
2025-07-29 09:25:58  web_server.py 82: INFO  Serving static file: fonts/element-icons.ff18efd1.woff [waitress-0]
2025-07-29 09:26:27  EarlyBird.api.user.apis 33: INFO  用户登录请求: username=test1010 [waitress-3]
2025-07-29 09:26:27  apis.py 33: INFO  用户登录请求: username=test1010 [waitress-3]
2025-07-29 09:26:27  EarlyBird.api.user.apis 41: INFO  已清除之前的session数据和激活相关字段 [waitress-3]
2025-07-29 09:26:27  apis.py 41: INFO  已清除之前的session数据和激活相关字段 [waitress-3]
2025-07-29 09:26:27  EarlyBird.api.user.service 52: WARNING  登录失败: 用户名 test1010 不存在 [waitress-3]
2025-07-29 09:26:27  service.py 52: WARNING  登录失败: 用户名 test1010 不存在 [waitress-3]
2025-07-29 09:26:27  EarlyBird.api.user.apis 80: WARNING  用户登录失败: 用户名或密码错误 [waitress-3]
2025-07-29 09:26:27  apis.py 80: WARNING  用户登录失败: 用户名或密码错误 [waitress-3]
2025-07-29 09:28:32  EarlyBird.api.user.apis 33: INFO  用户登录请求: username=test1011 [waitress-1]
2025-07-29 09:28:32  apis.py 33: INFO  用户登录请求: username=test1011 [waitress-1]
2025-07-29 09:28:32  EarlyBird.api.user.apis 41: INFO  已清除之前的session数据和激活相关字段 [waitress-1]
2025-07-29 09:28:32  apis.py 41: INFO  已清除之前的session数据和激活相关字段 [waitress-1]
2025-07-29 09:28:33  EarlyBird.api.user.service 81: INFO  用户 test1011(ID=14) 登录成功 [waitress-1]
2025-07-29 09:28:33  service.py 81: INFO  用户 test1011(ID=14) 登录成功 [waitress-1]
2025-07-29 09:28:33  EarlyBird.api.user.apis 57: INFO  用户 14 登录成功，session已设置 [waitress-1]
2025-07-29 09:28:33  apis.py 57: INFO  用户 14 登录成功，session已设置 [waitress-1]
2025-07-29 09:28:33  EarlyBird.api.user.apis 58: INFO  Session内容: {'user_id': 14, 'username': 'test1011', 'login_time': '2025-07-29T09:28:33.015415', '_permanent': True} [waitress-1]
2025-07-29 09:28:33  apis.py 58: INFO  Session内容: {'user_id': 14, 'username': 'test1011', 'login_time': '2025-07-29T09:28:33.015415', '_permanent': True} [waitress-1]
2025-07-29 09:28:33  web_server.py 73: INFO  Serving request for path: img/logo.ce3a5bfd.png [waitress-0]
2025-07-29 09:28:33  web_server.py 82: INFO  Serving static file: img/logo.ce3a5bfd.png [waitress-0]
2025-07-29 09:28:33  service.py 70: INFO  获取设置成功，包含 8 个配置项 [waitress-2]
2025-07-29 09:28:35  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/user/info [waitress-3]
2025-07-29 09:28:35  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/user/info [waitress-3]
2025-07-29 09:28:35  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:28:35  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:28:35  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:28:35  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:28:35  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:28:35  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:28:35  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getList [waitress-1]
2025-07-29 09:28:35  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getList [waitress-1]
2025-07-29 09:28:35  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:28:35  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:28:35  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:28:35  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:28:35  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:28:35  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:28:35  EarlyBird.api.thesis.apis 92: INFO  用户 14 请求获取论文列表 [waitress-1]
2025-07-29 09:28:35  apis.py 92: INFO  用户 14 请求获取论文列表 [waitress-1]
2025-07-29 09:28:35  EarlyBird.api.thesis.service 595: INFO  查询论文列表，用户ID: 14 [waitress-1]
2025-07-29 09:28:35  service.py 595: INFO  查询论文列表，用户ID: 14 [waitress-1]
2025-07-29 09:28:35  EarlyBird.api.thesis.service 601: INFO  查询到 1 篇论文 [waitress-1]
2025-07-29 09:28:35  service.py 601: INFO  查询到 1 篇论文 [waitress-1]
2025-07-29 09:28:35  EarlyBird.api.thesis.service 611: INFO  论文ID: 32, 标题: CES技术在提高电源效率中的应用研究, 用户ID: 14 [waitress-1]
2025-07-29 09:28:35  service.py 611: INFO  论文ID: 32, 标题: CES技术在提高电源效率中的应用研究, 用户ID: 14 [waitress-1]
2025-07-29 09:28:35  EarlyBird.api.thesis.service 613: INFO  返回论文列表，共 1 篇 [waitress-1]
2025-07-29 09:28:35  service.py 613: INFO  返回论文列表，共 1 篇 [waitress-1]
2025-07-29 09:28:35  EarlyBird.api.thesis.apis 95: INFO  用户 14 获取论文列表成功，共 1 篇 [waitress-1]
2025-07-29 09:28:35  apis.py 95: INFO  用户 14 获取论文列表成功，共 1 篇 [waitress-1]
2025-07-29 09:28:35  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getDetail [waitress-0]
2025-07-29 09:28:35  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getDetail [waitress-0]
2025-07-29 09:28:35  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:28:35  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:28:35  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:28:35  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:28:35  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:28:35  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:28:35  EarlyBird.api.talk2ai.utils 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-0]
2025-07-29 09:28:35  utils.py 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-0]
2025-07-29 09:28:35  EarlyBird.api.talk2ai.utils 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-0]
2025-07-29 09:28:35  utils.py 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-0]
2025-07-29 09:28:35  EarlyBird.api.talk2ai.utils 161: INFO  提纲处理完成，根节点ID: E2em6gS9wrhodhQoZ8tQHc [waitress-0]
2025-07-29 09:28:35  utils.py 161: INFO  提纲处理完成，根节点ID: E2em6gS9wrhodhQoZ8tQHc [waitress-0]
2025-07-29 09:28:35  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getOutline [waitress-2]
2025-07-29 09:28:35  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getOutline [waitress-2]
2025-07-29 09:28:35  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:28:35  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:28:35  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:28:35  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:28:35  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:28:35  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:28:35  EarlyBird.api.talk2ai.utils 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-2]
2025-07-29 09:28:35  utils.py 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-2]
2025-07-29 09:28:35  EarlyBird.api.talk2ai.utils 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-2]
2025-07-29 09:28:35  utils.py 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-2]
2025-07-29 09:28:35  EarlyBird.api.talk2ai.utils 161: INFO  提纲处理完成，根节点ID: E2em6gS9wrhodhQoZ8tQHc [waitress-2]
2025-07-29 09:28:35  utils.py 161: INFO  提纲处理完成，根节点ID: E2em6gS9wrhodhQoZ8tQHc [waitress-2]
2025-07-29 09:28:36  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/downloadThesis [waitress-3]
2025-07-29 09:28:36  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/downloadThesis [waitress-3]
2025-07-29 09:28:36  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:28:36  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:28:36  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:28:36  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:28:36  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:28:36  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:28:36  EarlyBird.api.thesis.apis 145: INFO  用户 14 请求下载论文 32 [waitress-3]
2025-07-29 09:28:36  apis.py 145: INFO  用户 14 请求下载论文 32 [waitress-3]
2025-07-29 09:28:36  EarlyBird.api.thesis.service 296: INFO  用户 14 请求导出论文 32 [waitress-3]
2025-07-29 09:28:36  service.py 296: INFO  用户 14 请求导出论文 32 [waitress-3]
2025-07-29 09:28:36  EarlyBird.api.thesis.service 306: INFO  用户 14 VIP状态: <bound method User.isVip of {"accessToken": null, "phone": null, "vip_expire_at": null, "refreshToken": null, "gender": 3, "balance": null, "update_time": "2025-07-29 09:28:33", "nickname": "\u7528\u6237_test1011", "email": null, "id": 14, "headimg": null, "is_lock": false, "invite_code": null, "is_deleted": 0, "activation_key": null, "last_login_time": "2025-07-29 09:28:32", "from_user_id": null, "status": 1, "username": "test1011", "last_login_ip": "127.0.0.1", "openPlatform": "wx", "password_hash": "pbkdf2:sha256:260000$3VYSebusaQuZ1fdD$9217323331b6562a816f82255c4826fd0d815e00bee146926900d409eee250fd", "vip_level": 1, "create_time": "2025-07-28 16:58:53", "openId": null, "realname": null, "vip_start_at": null}> [waitress-3]
2025-07-29 09:28:36  service.py 306: INFO  用户 14 VIP状态: <bound method User.isVip of {"accessToken": null, "phone": null, "vip_expire_at": null, "refreshToken": null, "gender": 3, "balance": null, "update_time": "2025-07-29 09:28:33", "nickname": "\u7528\u6237_test1011", "email": null, "id": 14, "headimg": null, "is_lock": false, "invite_code": null, "is_deleted": 0, "activation_key": null, "last_login_time": "2025-07-29 09:28:32", "from_user_id": null, "status": 1, "username": "test1011", "last_login_ip": "127.0.0.1", "openPlatform": "wx", "password_hash": "pbkdf2:sha256:260000$3VYSebusaQuZ1fdD$9217323331b6562a816f82255c4826fd0d815e00bee146926900d409eee250fd", "vip_level": 1, "create_time": "2025-07-28 16:58:53", "openId": null, "realname": null, "vip_start_at": null}> [waitress-3]
2025-07-29 09:28:36  EarlyBird.api.thesis.service 311: INFO  论文下载收费功能是否启用: True [waitress-3]
2025-07-29 09:28:36  service.py 311: INFO  论文下载收费功能是否启用: True [waitress-3]
2025-07-29 09:28:36  EarlyBird.api.thesis.service 327: INFO  VIP用户是否免费下载: False [waitress-3]
2025-07-29 09:28:36  service.py 327: INFO  VIP用户是否免费下载: False [waitress-3]
2025-07-29 09:28:36  EarlyBird.api.thesis.service 344: INFO  首次下载是否免费: False [waitress-3]
2025-07-29 09:28:36  service.py 344: INFO  首次下载是否免费: False [waitress-3]
2025-07-29 09:28:36  EarlyBird.api.thesis.service 349: INFO  用户 14 是否有任何下载记录: True [waitress-3]
2025-07-29 09:28:36  service.py 349: INFO  用户 14 是否有任何下载记录: True [waitress-3]
2025-07-29 09:28:36  EarlyBird.api.thesis.service 459: INFO  从配置中读取到论文下载价格: 10.0 [waitress-3]
2025-07-29 09:28:36  service.py 459: INFO  从配置中读取到论文下载价格: 10.0 [waitress-3]
2025-07-29 09:28:36  EarlyBird.api.thesis.service 467: INFO  用户 14 需要支付才能下载论文 32，价格: 10.0 [waitress-3]
2025-07-29 09:28:36  service.py 467: INFO  用户 14 需要支付才能下载论文 32，价格: 10.0 [waitress-3]
2025-07-29 09:28:37  EarlyBird.api.thesis.apis 151: INFO  用户 14 需要支付才能下载论文 32，价格: 10.0 [waitress-3]
2025-07-29 09:28:37  apis.py 151: INFO  用户 14 需要支付才能下载论文 32，价格: 10.0 [waitress-3]
2025-07-29 09:28:37  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/payDownload [waitress-1]
2025-07-29 09:28:37  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/payDownload [waitress-1]
2025-07-29 09:28:37  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:28:37  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:28:37  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:28:37  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:28:37  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:28:37  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:28:37  EarlyBird.api.thesis.service 724: INFO  创建支付记录成功 - 订单: thesis_download_20e38f5a98a0, 金额: 10.0 [waitress-1]
2025-07-29 09:28:37  service.py 724: INFO  创建支付记录成功 - 订单: thesis_download_20e38f5a98a0, 金额: 10.0 [waitress-1]
2025-07-29 09:28:40  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-0]
2025-07-29 09:28:40  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-0]
2025-07-29 09:28:40  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:28:40  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:28:40  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:28:40  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:28:40  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:28:40  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:28:40  EarlyBird.api.thesis.service 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_20e38f5a98a0 [waitress-0]
2025-07-29 09:28:40  service.py 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_20e38f5a98a0 [waitress-0]
2025-07-29 09:28:40  EarlyBird.api.thesis.service 788: INFO  找到支付记录 - 订单: thesis_download_20e38f5a98a0, 状态: 0 [waitress-0]
2025-07-29 09:28:40  service.py 788: INFO  找到支付记录 - 订单: thesis_download_20e38f5a98a0, 状态: 0 [waitress-0]
2025-07-29 09:28:43  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-2]
2025-07-29 09:28:43  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-2]
2025-07-29 09:28:43  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:28:43  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:28:43  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:28:43  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:28:43  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:28:43  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:28:43  EarlyBird.api.thesis.service 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_20e38f5a98a0 [waitress-2]
2025-07-29 09:28:43  service.py 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_20e38f5a98a0 [waitress-2]
2025-07-29 09:28:43  EarlyBird.api.thesis.service 788: INFO  找到支付记录 - 订单: thesis_download_20e38f5a98a0, 状态: 0 [waitress-2]
2025-07-29 09:28:43  service.py 788: INFO  找到支付记录 - 订单: thesis_download_20e38f5a98a0, 状态: 0 [waitress-2]
2025-07-29 09:28:46  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-3]
2025-07-29 09:28:46  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-3]
2025-07-29 09:28:46  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:28:46  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:28:46  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:28:46  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:28:46  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:28:46  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:28:46  EarlyBird.api.thesis.service 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_20e38f5a98a0 [waitress-3]
2025-07-29 09:28:46  service.py 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_20e38f5a98a0 [waitress-3]
2025-07-29 09:28:46  EarlyBird.api.thesis.service 788: INFO  找到支付记录 - 订单: thesis_download_20e38f5a98a0, 状态: 0 [waitress-3]
2025-07-29 09:28:46  service.py 788: INFO  找到支付记录 - 订单: thesis_download_20e38f5a98a0, 状态: 0 [waitress-3]
2025-07-29 09:28:49  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-1]
2025-07-29 09:28:49  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-1]
2025-07-29 09:28:49  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:28:49  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:28:49  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:28:49  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:28:49  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:28:49  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:28:49  EarlyBird.api.thesis.service 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_20e38f5a98a0 [waitress-1]
2025-07-29 09:28:49  service.py 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_20e38f5a98a0 [waitress-1]
2025-07-29 09:28:49  EarlyBird.api.thesis.service 788: INFO  找到支付记录 - 订单: thesis_download_20e38f5a98a0, 状态: 0 [waitress-1]
2025-07-29 09:28:49  service.py 788: INFO  找到支付记录 - 订单: thesis_download_20e38f5a98a0, 状态: 0 [waitress-1]
2025-07-29 09:28:51  web_server.py 73: INFO  Serving request for path: css/chunk-vendors.d5efd04f.css [waitress-0]
2025-07-29 09:28:51  web_server.py 73: INFO  Serving request for path: css/app.1e68ddae.css [waitress-2]
2025-07-29 09:28:51  web_server.py 82: INFO  Serving static file: css/chunk-vendors.d5efd04f.css [waitress-0]
2025-07-29 09:28:51  web_server.py 73: INFO  Serving request for path: css/389.ae47f7d6.css [waitress-3]
2025-07-29 09:28:51  web_server.py 82: INFO  Serving static file: css/app.1e68ddae.css [waitress-2]
2025-07-29 09:28:51  web_server.py 82: INFO  Serving static file: css/389.ae47f7d6.css [waitress-3]
2025-07-29 09:28:51  web_server.py 73: INFO  Serving request for path: .well-known/appspecific/com.chrome.devtools.json [waitress-1]
2025-07-29 09:28:51  web_server.py 85: INFO  Serving index.html for path: .well-known/appspecific/com.chrome.devtools.json [waitress-1]
2025-07-29 09:28:52  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-0]
2025-07-29 09:28:52  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-0]
2025-07-29 09:28:52  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:28:52  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:28:52  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:28:52  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:28:52  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:28:52  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:28:52  EarlyBird.api.thesis.service 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_20e38f5a98a0 [waitress-0]
2025-07-29 09:28:52  service.py 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_20e38f5a98a0 [waitress-0]
2025-07-29 09:28:52  EarlyBird.api.thesis.service 788: INFO  找到支付记录 - 订单: thesis_download_20e38f5a98a0, 状态: 0 [waitress-0]
2025-07-29 09:28:52  service.py 788: INFO  找到支付记录 - 订单: thesis_download_20e38f5a98a0, 状态: 0 [waitress-0]
2025-07-29 09:28:55  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-2]
2025-07-29 09:28:55  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-2]
2025-07-29 09:28:55  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:28:55  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:28:55  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:28:55  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:28:55  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:28:55  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:28:55  EarlyBird.api.thesis.service 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_20e38f5a98a0 [waitress-2]
2025-07-29 09:28:55  service.py 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_20e38f5a98a0 [waitress-2]
2025-07-29 09:28:55  EarlyBird.api.thesis.service 788: INFO  找到支付记录 - 订单: thesis_download_20e38f5a98a0, 状态: 0 [waitress-2]
2025-07-29 09:28:55  service.py 788: INFO  找到支付记录 - 订单: thesis_download_20e38f5a98a0, 状态: 0 [waitress-2]
2025-07-29 09:28:56  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/downloadThesis [waitress-3]
2025-07-29 09:28:56  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/downloadThesis [waitress-3]
2025-07-29 09:28:56  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:28:56  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:28:56  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:28:56  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:28:56  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:28:56  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:28:56  EarlyBird.api.thesis.apis 145: INFO  用户 14 请求下载论文 32 [waitress-3]
2025-07-29 09:28:56  apis.py 145: INFO  用户 14 请求下载论文 32 [waitress-3]
2025-07-29 09:28:56  EarlyBird.api.thesis.service 296: INFO  用户 14 请求导出论文 32 [waitress-3]
2025-07-29 09:28:56  service.py 296: INFO  用户 14 请求导出论文 32 [waitress-3]
2025-07-29 09:28:56  EarlyBird.api.thesis.service 306: INFO  用户 14 VIP状态: <bound method User.isVip of {"accessToken": null, "phone": null, "vip_expire_at": null, "refreshToken": null, "gender": 3, "balance": null, "update_time": "2025-07-29 09:28:33", "nickname": "\u7528\u6237_test1011", "email": null, "id": 14, "headimg": null, "is_lock": false, "invite_code": null, "is_deleted": 0, "activation_key": null, "last_login_time": "2025-07-29 09:28:32", "from_user_id": null, "status": 1, "username": "test1011", "last_login_ip": "127.0.0.1", "openPlatform": "wx", "password_hash": "pbkdf2:sha256:260000$3VYSebusaQuZ1fdD$9217323331b6562a816f82255c4826fd0d815e00bee146926900d409eee250fd", "vip_level": 1, "create_time": "2025-07-28 16:58:53", "openId": null, "realname": null, "vip_start_at": null}> [waitress-3]
2025-07-29 09:28:56  service.py 306: INFO  用户 14 VIP状态: <bound method User.isVip of {"accessToken": null, "phone": null, "vip_expire_at": null, "refreshToken": null, "gender": 3, "balance": null, "update_time": "2025-07-29 09:28:33", "nickname": "\u7528\u6237_test1011", "email": null, "id": 14, "headimg": null, "is_lock": false, "invite_code": null, "is_deleted": 0, "activation_key": null, "last_login_time": "2025-07-29 09:28:32", "from_user_id": null, "status": 1, "username": "test1011", "last_login_ip": "127.0.0.1", "openPlatform": "wx", "password_hash": "pbkdf2:sha256:260000$3VYSebusaQuZ1fdD$9217323331b6562a816f82255c4826fd0d815e00bee146926900d409eee250fd", "vip_level": 1, "create_time": "2025-07-28 16:58:53", "openId": null, "realname": null, "vip_start_at": null}> [waitress-3]
2025-07-29 09:28:56  EarlyBird.api.thesis.service 311: INFO  论文下载收费功能是否启用: True [waitress-3]
2025-07-29 09:28:56  service.py 311: INFO  论文下载收费功能是否启用: True [waitress-3]
2025-07-29 09:28:56  EarlyBird.api.thesis.service 327: INFO  VIP用户是否免费下载: False [waitress-3]
2025-07-29 09:28:56  service.py 327: INFO  VIP用户是否免费下载: False [waitress-3]
2025-07-29 09:28:56  EarlyBird.api.thesis.service 344: INFO  首次下载是否免费: False [waitress-3]
2025-07-29 09:28:56  service.py 344: INFO  首次下载是否免费: False [waitress-3]
2025-07-29 09:28:56  EarlyBird.api.thesis.service 349: INFO  用户 14 是否有任何下载记录: True [waitress-3]
2025-07-29 09:28:56  service.py 349: INFO  用户 14 是否有任何下载记录: True [waitress-3]
2025-07-29 09:28:56  EarlyBird.api.thesis.service 459: INFO  从配置中读取到论文下载价格: 10.0 [waitress-3]
2025-07-29 09:28:56  service.py 459: INFO  从配置中读取到论文下载价格: 10.0 [waitress-3]
2025-07-29 09:28:56  EarlyBird.api.thesis.service 467: INFO  用户 14 需要支付才能下载论文 32，价格: 10.0 [waitress-3]
2025-07-29 09:28:56  service.py 467: INFO  用户 14 需要支付才能下载论文 32，价格: 10.0 [waitress-3]
2025-07-29 09:28:56  EarlyBird.api.thesis.apis 151: INFO  用户 14 需要支付才能下载论文 32，价格: 10.0 [waitress-3]
2025-07-29 09:28:56  apis.py 151: INFO  用户 14 需要支付才能下载论文 32，价格: 10.0 [waitress-3]
2025-07-29 09:28:56  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/payDownload [waitress-1]
2025-07-29 09:28:56  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/payDownload [waitress-1]
2025-07-29 09:28:56  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:28:56  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:28:56  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:28:56  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:28:56  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:28:56  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:28:56  EarlyBird.api.thesis.service 724: INFO  创建支付记录成功 - 订单: thesis_download_4b693ddcc20f, 金额: 10.0 [waitress-1]
2025-07-29 09:28:56  service.py 724: INFO  创建支付记录成功 - 订单: thesis_download_4b693ddcc20f, 金额: 10.0 [waitress-1]
2025-07-29 09:28:59  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-0]
2025-07-29 09:28:59  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-0]
2025-07-29 09:28:59  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:28:59  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:28:59  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:28:59  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:28:59  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:28:59  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:28:59  EarlyBird.api.thesis.service 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_4b693ddcc20f [waitress-0]
2025-07-29 09:28:59  service.py 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_4b693ddcc20f [waitress-0]
2025-07-29 09:28:59  EarlyBird.api.thesis.service 788: INFO  找到支付记录 - 订单: thesis_download_4b693ddcc20f, 状态: 0 [waitress-0]
2025-07-29 09:28:59  service.py 788: INFO  找到支付记录 - 订单: thesis_download_4b693ddcc20f, 状态: 0 [waitress-0]
2025-07-29 09:29:02  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-2]
2025-07-29 09:29:02  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-2]
2025-07-29 09:29:02  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:29:02  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:29:02  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:29:02  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:29:02  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:29:02  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:29:02  EarlyBird.api.thesis.service 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_4b693ddcc20f [waitress-2]
2025-07-29 09:29:02  service.py 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_4b693ddcc20f [waitress-2]
2025-07-29 09:29:02  EarlyBird.api.thesis.service 788: INFO  找到支付记录 - 订单: thesis_download_4b693ddcc20f, 状态: 0 [waitress-2]
2025-07-29 09:29:02  service.py 788: INFO  找到支付记录 - 订单: thesis_download_4b693ddcc20f, 状态: 0 [waitress-2]
2025-07-29 09:29:05  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-3]
2025-07-29 09:29:05  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-3]
2025-07-29 09:29:05  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:29:05  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:29:05  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:29:05  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:29:05  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:29:05  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:29:05  EarlyBird.api.thesis.service 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_4b693ddcc20f [waitress-3]
2025-07-29 09:29:05  service.py 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_4b693ddcc20f [waitress-3]
2025-07-29 09:29:05  EarlyBird.api.thesis.service 788: INFO  找到支付记录 - 订单: thesis_download_4b693ddcc20f, 状态: 0 [waitress-3]
2025-07-29 09:29:05  service.py 788: INFO  找到支付记录 - 订单: thesis_download_4b693ddcc20f, 状态: 0 [waitress-3]
2025-07-29 09:29:08  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-1]
2025-07-29 09:29:08  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-1]
2025-07-29 09:29:08  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:29:08  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:29:08  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:29:08  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:29:08  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:29:08  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:29:08  EarlyBird.api.thesis.service 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_4b693ddcc20f [waitress-1]
2025-07-29 09:29:08  service.py 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_4b693ddcc20f [waitress-1]
2025-07-29 09:29:08  EarlyBird.api.thesis.service 788: INFO  找到支付记录 - 订单: thesis_download_4b693ddcc20f, 状态: 0 [waitress-1]
2025-07-29 09:29:08  service.py 788: INFO  找到支付记录 - 订单: thesis_download_4b693ddcc20f, 状态: 0 [waitress-1]
2025-07-29 09:29:11  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-0]
2025-07-29 09:29:11  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-0]
2025-07-29 09:29:11  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:29:11  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:29:11  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:29:11  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:29:11  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:29:11  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:29:11  EarlyBird.api.thesis.service 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_4b693ddcc20f [waitress-0]
2025-07-29 09:29:11  service.py 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_4b693ddcc20f [waitress-0]
2025-07-29 09:29:11  EarlyBird.api.thesis.service 788: INFO  找到支付记录 - 订单: thesis_download_4b693ddcc20f, 状态: 0 [waitress-0]
2025-07-29 09:29:11  service.py 788: INFO  找到支付记录 - 订单: thesis_download_4b693ddcc20f, 状态: 0 [waitress-0]
2025-07-29 09:29:14  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-2]
2025-07-29 09:29:14  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-2]
2025-07-29 09:29:14  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:29:14  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:29:14  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:29:14  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:29:14  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:29:14  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:29:14  EarlyBird.api.thesis.service 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_4b693ddcc20f [waitress-2]
2025-07-29 09:29:14  service.py 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_4b693ddcc20f [waitress-2]
2025-07-29 09:29:14  EarlyBird.api.thesis.service 788: INFO  找到支付记录 - 订单: thesis_download_4b693ddcc20f, 状态: 0 [waitress-2]
2025-07-29 09:29:14  service.py 788: INFO  找到支付记录 - 订单: thesis_download_4b693ddcc20f, 状态: 0 [waitress-2]
2025-07-29 09:29:17  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-3]
2025-07-29 09:29:17  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-3]
2025-07-29 09:29:17  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:29:17  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:29:17  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:29:17  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:29:17  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:29:17  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:29:17  EarlyBird.api.thesis.service 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_4b693ddcc20f [waitress-3]
2025-07-29 09:29:17  service.py 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_4b693ddcc20f [waitress-3]
2025-07-29 09:29:17  EarlyBird.api.thesis.service 788: INFO  找到支付记录 - 订单: thesis_download_4b693ddcc20f, 状态: 0 [waitress-3]
2025-07-29 09:29:17  service.py 788: INFO  找到支付记录 - 订单: thesis_download_4b693ddcc20f, 状态: 0 [waitress-3]
2025-07-29 09:29:20  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-1]
2025-07-29 09:29:20  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-1]
2025-07-29 09:29:20  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:29:20  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:29:20  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:29:20  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:29:20  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:29:20  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:29:20  EarlyBird.api.thesis.service 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_4b693ddcc20f [waitress-1]
2025-07-29 09:29:20  service.py 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_4b693ddcc20f [waitress-1]
2025-07-29 09:29:20  EarlyBird.api.thesis.service 788: INFO  找到支付记录 - 订单: thesis_download_4b693ddcc20f, 状态: 0 [waitress-1]
2025-07-29 09:29:20  service.py 788: INFO  找到支付记录 - 订单: thesis_download_4b693ddcc20f, 状态: 0 [waitress-1]
2025-07-29 09:29:23  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-0]
2025-07-29 09:29:23  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-0]
2025-07-29 09:29:23  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:29:23  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:29:23  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:29:23  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:29:23  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:29:23  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:29:23  EarlyBird.api.thesis.service 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_4b693ddcc20f [waitress-0]
2025-07-29 09:29:23  service.py 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_4b693ddcc20f [waitress-0]
2025-07-29 09:29:23  EarlyBird.api.thesis.service 788: INFO  找到支付记录 - 订单: thesis_download_4b693ddcc20f, 状态: 0 [waitress-0]
2025-07-29 09:29:23  service.py 788: INFO  找到支付记录 - 订单: thesis_download_4b693ddcc20f, 状态: 0 [waitress-0]
2025-07-29 09:29:26  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-2]
2025-07-29 09:29:26  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-2]
2025-07-29 09:29:26  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:29:26  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:29:26  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:29:26  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:29:26  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:29:26  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:29:26  EarlyBird.api.thesis.service 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_4b693ddcc20f [waitress-2]
2025-07-29 09:29:26  service.py 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_4b693ddcc20f [waitress-2]
2025-07-29 09:29:26  EarlyBird.api.thesis.service 788: INFO  找到支付记录 - 订单: thesis_download_4b693ddcc20f, 状态: 0 [waitress-2]
2025-07-29 09:29:26  service.py 788: INFO  找到支付记录 - 订单: thesis_download_4b693ddcc20f, 状态: 0 [waitress-2]
2025-07-29 09:29:29  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-3]
2025-07-29 09:29:29  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-3]
2025-07-29 09:29:29  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:29:29  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:29:29  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:29:29  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:29:29  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:29:29  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:29:29  EarlyBird.api.thesis.service 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_4b693ddcc20f [waitress-3]
2025-07-29 09:29:29  service.py 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_4b693ddcc20f [waitress-3]
2025-07-29 09:29:29  EarlyBird.api.thesis.service 788: INFO  找到支付记录 - 订单: thesis_download_4b693ddcc20f, 状态: 0 [waitress-3]
2025-07-29 09:29:29  service.py 788: INFO  找到支付记录 - 订单: thesis_download_4b693ddcc20f, 状态: 0 [waitress-3]
2025-07-29 09:29:32  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-1]
2025-07-29 09:29:32  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-1]
2025-07-29 09:29:32  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:29:32  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:29:32  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:29:32  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:29:32  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:29:32  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:29:32  EarlyBird.api.thesis.service 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_4b693ddcc20f [waitress-1]
2025-07-29 09:29:32  service.py 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_4b693ddcc20f [waitress-1]
2025-07-29 09:29:32  EarlyBird.api.thesis.service 788: INFO  找到支付记录 - 订单: thesis_download_4b693ddcc20f, 状态: 0 [waitress-1]
2025-07-29 09:29:32  service.py 788: INFO  找到支付记录 - 订单: thesis_download_4b693ddcc20f, 状态: 0 [waitress-1]
2025-07-29 09:29:35  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-0]
2025-07-29 09:29:35  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-0]
2025-07-29 09:29:35  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:29:35  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:29:35  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:29:35  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:29:35  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:29:35  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:29:35  EarlyBird.api.thesis.service 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_4b693ddcc20f [waitress-0]
2025-07-29 09:29:35  service.py 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_4b693ddcc20f [waitress-0]
2025-07-29 09:29:35  EarlyBird.api.thesis.service 788: INFO  找到支付记录 - 订单: thesis_download_4b693ddcc20f, 状态: 0 [waitress-0]
2025-07-29 09:29:35  service.py 788: INFO  找到支付记录 - 订单: thesis_download_4b693ddcc20f, 状态: 0 [waitress-0]
2025-07-29 09:29:38  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-2]
2025-07-29 09:29:38  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-2]
2025-07-29 09:29:38  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:29:38  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:29:38  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:29:38  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:29:38  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:29:38  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:29:38  EarlyBird.api.thesis.service 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_4b693ddcc20f [waitress-2]
2025-07-29 09:29:38  service.py 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_4b693ddcc20f [waitress-2]
2025-07-29 09:29:38  EarlyBird.api.thesis.service 788: INFO  找到支付记录 - 订单: thesis_download_4b693ddcc20f, 状态: 0 [waitress-2]
2025-07-29 09:29:38  service.py 788: INFO  找到支付记录 - 订单: thesis_download_4b693ddcc20f, 状态: 0 [waitress-2]
2025-07-29 09:29:41  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-3]
2025-07-29 09:29:41  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-3]
2025-07-29 09:29:41  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:29:41  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:29:41  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:29:41  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:29:41  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:29:41  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:29:41  EarlyBird.api.thesis.service 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_4b693ddcc20f [waitress-3]
2025-07-29 09:29:41  service.py 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_4b693ddcc20f [waitress-3]
2025-07-29 09:29:41  EarlyBird.api.thesis.service 788: INFO  找到支付记录 - 订单: thesis_download_4b693ddcc20f, 状态: 0 [waitress-3]
2025-07-29 09:29:41  service.py 788: INFO  找到支付记录 - 订单: thesis_download_4b693ddcc20f, 状态: 0 [waitress-3]
2025-07-29 09:29:44  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-1]
2025-07-29 09:29:44  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-1]
2025-07-29 09:29:44  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:29:44  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:29:44  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:29:44  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:29:44  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:29:44  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:29:44  EarlyBird.api.thesis.service 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_4b693ddcc20f [waitress-1]
2025-07-29 09:29:44  service.py 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_4b693ddcc20f [waitress-1]
2025-07-29 09:29:44  EarlyBird.api.thesis.service 788: INFO  找到支付记录 - 订单: thesis_download_4b693ddcc20f, 状态: 0 [waitress-1]
2025-07-29 09:29:44  service.py 788: INFO  找到支付记录 - 订单: thesis_download_4b693ddcc20f, 状态: 0 [waitress-1]
2025-07-29 09:29:47  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-0]
2025-07-29 09:29:47  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-0]
2025-07-29 09:29:47  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:29:47  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:29:47  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:29:47  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:29:47  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:29:47  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:29:47  EarlyBird.api.thesis.service 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_4b693ddcc20f [waitress-0]
2025-07-29 09:29:47  service.py 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_4b693ddcc20f [waitress-0]
2025-07-29 09:29:47  EarlyBird.api.thesis.service 788: INFO  找到支付记录 - 订单: thesis_download_4b693ddcc20f, 状态: 0 [waitress-0]
2025-07-29 09:29:47  service.py 788: INFO  找到支付记录 - 订单: thesis_download_4b693ddcc20f, 状态: 0 [waitress-0]
2025-07-29 09:29:50  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-2]
2025-07-29 09:29:50  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-2]
2025-07-29 09:29:50  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:29:50  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:29:50  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:29:50  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:29:50  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:29:50  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:29:50  EarlyBird.api.thesis.service 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_4b693ddcc20f [waitress-2]
2025-07-29 09:29:50  service.py 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_4b693ddcc20f [waitress-2]
2025-07-29 09:29:50  EarlyBird.api.thesis.service 788: INFO  找到支付记录 - 订单: thesis_download_4b693ddcc20f, 状态: 0 [waitress-2]
2025-07-29 09:29:50  service.py 788: INFO  找到支付记录 - 订单: thesis_download_4b693ddcc20f, 状态: 0 [waitress-2]
2025-07-29 09:29:53  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-3]
2025-07-29 09:29:53  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-3]
2025-07-29 09:29:53  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:29:53  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:29:53  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:29:53  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:29:53  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:29:53  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:29:53  EarlyBird.api.thesis.service 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_4b693ddcc20f [waitress-3]
2025-07-29 09:29:53  service.py 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_4b693ddcc20f [waitress-3]
2025-07-29 09:29:53  EarlyBird.api.thesis.service 788: INFO  找到支付记录 - 订单: thesis_download_4b693ddcc20f, 状态: 0 [waitress-3]
2025-07-29 09:29:53  service.py 788: INFO  找到支付记录 - 订单: thesis_download_4b693ddcc20f, 状态: 0 [waitress-3]
2025-07-29 09:29:56  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-1]
2025-07-29 09:29:56  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-1]
2025-07-29 09:29:56  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:29:56  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:29:56  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:29:56  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:29:56  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:29:56  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:29:56  EarlyBird.api.thesis.service 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_4b693ddcc20f [waitress-1]
2025-07-29 09:29:56  service.py 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_4b693ddcc20f [waitress-1]
2025-07-29 09:29:56  EarlyBird.api.thesis.service 788: INFO  找到支付记录 - 订单: thesis_download_4b693ddcc20f, 状态: 0 [waitress-1]
2025-07-29 09:29:56  service.py 788: INFO  找到支付记录 - 订单: thesis_download_4b693ddcc20f, 状态: 0 [waitress-1]
2025-07-29 09:29:59  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-0]
2025-07-29 09:29:59  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-0]
2025-07-29 09:29:59  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:29:59  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:29:59  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:29:59  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:29:59  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:29:59  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:29:59  EarlyBird.api.thesis.service 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_4b693ddcc20f [waitress-0]
2025-07-29 09:29:59  service.py 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_4b693ddcc20f [waitress-0]
2025-07-29 09:29:59  EarlyBird.api.thesis.service 788: INFO  找到支付记录 - 订单: thesis_download_4b693ddcc20f, 状态: 0 [waitress-0]
2025-07-29 09:29:59  service.py 788: INFO  找到支付记录 - 订单: thesis_download_4b693ddcc20f, 状态: 0 [waitress-0]
2025-07-29 09:30:02  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-2]
2025-07-29 09:30:02  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-2]
2025-07-29 09:30:02  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:30:02  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:30:02  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:30:02  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:30:02  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:30:02  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:30:02  EarlyBird.api.thesis.service 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_4b693ddcc20f [waitress-2]
2025-07-29 09:30:02  service.py 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_4b693ddcc20f [waitress-2]
2025-07-29 09:30:02  EarlyBird.api.thesis.service 788: INFO  找到支付记录 - 订单: thesis_download_4b693ddcc20f, 状态: 0 [waitress-2]
2025-07-29 09:30:02  service.py 788: INFO  找到支付记录 - 订单: thesis_download_4b693ddcc20f, 状态: 0 [waitress-2]
2025-07-29 09:30:05  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-3]
2025-07-29 09:30:05  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-3]
2025-07-29 09:30:05  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:30:05  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:30:05  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:30:05  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:30:05  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:30:05  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:30:05  EarlyBird.api.thesis.service 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_4b693ddcc20f [waitress-3]
2025-07-29 09:30:05  service.py 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_4b693ddcc20f [waitress-3]
2025-07-29 09:30:05  EarlyBird.api.thesis.service 788: INFO  找到支付记录 - 订单: thesis_download_4b693ddcc20f, 状态: 0 [waitress-3]
2025-07-29 09:30:05  service.py 788: INFO  找到支付记录 - 订单: thesis_download_4b693ddcc20f, 状态: 0 [waitress-3]
2025-07-29 09:30:08  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-1]
2025-07-29 09:30:08  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-1]
2025-07-29 09:30:08  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:30:08  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:30:08  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:30:08  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:30:08  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:30:08  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:30:08  EarlyBird.api.thesis.service 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_4b693ddcc20f [waitress-1]
2025-07-29 09:30:08  service.py 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_4b693ddcc20f [waitress-1]
2025-07-29 09:30:08  EarlyBird.api.thesis.service 788: INFO  找到支付记录 - 订单: thesis_download_4b693ddcc20f, 状态: 0 [waitress-1]
2025-07-29 09:30:08  service.py 788: INFO  找到支付记录 - 订单: thesis_download_4b693ddcc20f, 状态: 0 [waitress-1]
2025-07-29 09:30:11  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-0]
2025-07-29 09:30:11  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-0]
2025-07-29 09:30:11  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:30:11  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:30:11  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:30:11  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:30:11  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:30:11  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:30:11  EarlyBird.api.thesis.service 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_4b693ddcc20f [waitress-0]
2025-07-29 09:30:11  service.py 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_4b693ddcc20f [waitress-0]
2025-07-29 09:30:11  EarlyBird.api.thesis.service 788: INFO  找到支付记录 - 订单: thesis_download_4b693ddcc20f, 状态: 0 [waitress-0]
2025-07-29 09:30:11  service.py 788: INFO  找到支付记录 - 订单: thesis_download_4b693ddcc20f, 状态: 0 [waitress-0]
2025-07-29 09:30:14  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-2]
2025-07-29 09:30:14  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-2]
2025-07-29 09:30:14  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:30:14  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:30:14  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:30:14  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:30:14  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:30:14  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:30:14  EarlyBird.api.thesis.service 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_4b693ddcc20f [waitress-2]
2025-07-29 09:30:14  service.py 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_4b693ddcc20f [waitress-2]
2025-07-29 09:30:14  EarlyBird.api.thesis.service 788: INFO  找到支付记录 - 订单: thesis_download_4b693ddcc20f, 状态: 0 [waitress-2]
2025-07-29 09:30:14  service.py 788: INFO  找到支付记录 - 订单: thesis_download_4b693ddcc20f, 状态: 0 [waitress-2]
2025-07-29 09:30:17  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-3]
2025-07-29 09:30:17  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-3]
2025-07-29 09:30:17  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:30:17  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:30:17  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:30:17  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:30:17  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:30:17  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:30:17  EarlyBird.api.thesis.service 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_4b693ddcc20f [waitress-3]
2025-07-29 09:30:17  service.py 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_4b693ddcc20f [waitress-3]
2025-07-29 09:30:17  EarlyBird.api.thesis.service 788: INFO  找到支付记录 - 订单: thesis_download_4b693ddcc20f, 状态: 0 [waitress-3]
2025-07-29 09:30:17  service.py 788: INFO  找到支付记录 - 订单: thesis_download_4b693ddcc20f, 状态: 0 [waitress-3]
2025-07-29 09:30:20  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-1]
2025-07-29 09:30:20  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-1]
2025-07-29 09:30:20  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:30:20  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:30:20  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:30:20  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:30:20  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:30:20  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:30:20  EarlyBird.api.thesis.service 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_4b693ddcc20f [waitress-1]
2025-07-29 09:30:20  service.py 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_4b693ddcc20f [waitress-1]
2025-07-29 09:30:20  EarlyBird.api.thesis.service 788: INFO  找到支付记录 - 订单: thesis_download_4b693ddcc20f, 状态: 0 [waitress-1]
2025-07-29 09:30:20  service.py 788: INFO  找到支付记录 - 订单: thesis_download_4b693ddcc20f, 状态: 0 [waitress-1]
2025-07-29 09:30:23  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-0]
2025-07-29 09:30:23  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-0]
2025-07-29 09:30:23  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:30:23  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:30:23  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:30:23  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:30:23  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:30:23  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:30:23  EarlyBird.api.thesis.service 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_4b693ddcc20f [waitress-0]
2025-07-29 09:30:23  service.py 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_4b693ddcc20f [waitress-0]
2025-07-29 09:30:23  EarlyBird.api.thesis.service 788: INFO  找到支付记录 - 订单: thesis_download_4b693ddcc20f, 状态: 0 [waitress-0]
2025-07-29 09:30:23  service.py 788: INFO  找到支付记录 - 订单: thesis_download_4b693ddcc20f, 状态: 0 [waitress-0]
2025-07-29 09:30:26  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-2]
2025-07-29 09:30:26  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-2]
2025-07-29 09:30:26  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:30:26  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:30:26  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:30:26  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:30:26  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:30:26  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:30:26  EarlyBird.api.thesis.service 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_4b693ddcc20f [waitress-2]
2025-07-29 09:30:26  service.py 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_4b693ddcc20f [waitress-2]
2025-07-29 09:30:26  EarlyBird.api.thesis.service 788: INFO  找到支付记录 - 订单: thesis_download_4b693ddcc20f, 状态: 0 [waitress-2]
2025-07-29 09:30:26  service.py 788: INFO  找到支付记录 - 订单: thesis_download_4b693ddcc20f, 状态: 0 [waitress-2]
2025-07-29 09:30:29  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-3]
2025-07-29 09:30:29  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-3]
2025-07-29 09:30:29  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:30:29  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:30:29  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:30:29  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:30:29  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:30:29  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:30:29  EarlyBird.api.thesis.service 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_4b693ddcc20f [waitress-3]
2025-07-29 09:30:29  service.py 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_4b693ddcc20f [waitress-3]
2025-07-29 09:30:29  EarlyBird.api.thesis.service 788: INFO  找到支付记录 - 订单: thesis_download_4b693ddcc20f, 状态: 0 [waitress-3]
2025-07-29 09:30:29  service.py 788: INFO  找到支付记录 - 订单: thesis_download_4b693ddcc20f, 状态: 0 [waitress-3]
2025-07-29 09:30:32  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-1]
2025-07-29 09:30:32  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-1]
2025-07-29 09:30:32  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:30:32  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:30:32  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:30:32  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:30:32  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:30:32  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:30:32  EarlyBird.api.thesis.service 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_4b693ddcc20f [waitress-1]
2025-07-29 09:30:32  service.py 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_4b693ddcc20f [waitress-1]
2025-07-29 09:30:32  EarlyBird.api.thesis.service 788: INFO  找到支付记录 - 订单: thesis_download_4b693ddcc20f, 状态: 0 [waitress-1]
2025-07-29 09:30:32  service.py 788: INFO  找到支付记录 - 订单: thesis_download_4b693ddcc20f, 状态: 0 [waitress-1]
2025-07-29 09:30:35  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-0]
2025-07-29 09:30:35  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-0]
2025-07-29 09:30:35  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:30:35  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:30:35  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:30:35  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:30:35  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:30:35  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:30:35  EarlyBird.api.thesis.service 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_4b693ddcc20f [waitress-0]
2025-07-29 09:30:35  service.py 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_4b693ddcc20f [waitress-0]
2025-07-29 09:30:35  EarlyBird.api.thesis.service 788: INFO  找到支付记录 - 订单: thesis_download_4b693ddcc20f, 状态: 0 [waitress-0]
2025-07-29 09:30:35  service.py 788: INFO  找到支付记录 - 订单: thesis_download_4b693ddcc20f, 状态: 0 [waitress-0]
2025-07-29 09:30:38  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-2]
2025-07-29 09:30:38  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-2]
2025-07-29 09:30:38  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:30:38  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:30:38  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:30:38  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:30:38  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:30:38  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:30:38  EarlyBird.api.thesis.service 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_4b693ddcc20f [waitress-2]
2025-07-29 09:30:38  service.py 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_4b693ddcc20f [waitress-2]
2025-07-29 09:30:38  EarlyBird.api.thesis.service 788: INFO  找到支付记录 - 订单: thesis_download_4b693ddcc20f, 状态: 0 [waitress-2]
2025-07-29 09:30:38  service.py 788: INFO  找到支付记录 - 订单: thesis_download_4b693ddcc20f, 状态: 0 [waitress-2]
2025-07-29 09:30:41  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-3]
2025-07-29 09:30:41  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-3]
2025-07-29 09:30:41  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:30:41  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:30:41  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:30:41  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:30:41  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:30:41  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:30:41  EarlyBird.api.thesis.service 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_4b693ddcc20f [waitress-3]
2025-07-29 09:30:41  service.py 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_4b693ddcc20f [waitress-3]
2025-07-29 09:30:41  EarlyBird.api.thesis.service 788: INFO  找到支付记录 - 订单: thesis_download_4b693ddcc20f, 状态: 0 [waitress-3]
2025-07-29 09:30:41  service.py 788: INFO  找到支付记录 - 订单: thesis_download_4b693ddcc20f, 状态: 0 [waitress-3]
2025-07-29 09:30:44  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-1]
2025-07-29 09:30:44  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-1]
2025-07-29 09:30:44  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:30:44  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:30:44  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:30:44  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:30:44  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:30:44  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:30:44  EarlyBird.api.thesis.service 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_4b693ddcc20f [waitress-1]
2025-07-29 09:30:44  service.py 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_4b693ddcc20f [waitress-1]
2025-07-29 09:30:44  EarlyBird.api.thesis.service 788: INFO  找到支付记录 - 订单: thesis_download_4b693ddcc20f, 状态: 0 [waitress-1]
2025-07-29 09:30:44  service.py 788: INFO  找到支付记录 - 订单: thesis_download_4b693ddcc20f, 状态: 0 [waitress-1]
2025-07-29 09:30:47  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-0]
2025-07-29 09:30:47  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-0]
2025-07-29 09:30:47  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:30:47  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:30:47  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:30:47  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:30:47  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:30:47  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:30:47  EarlyBird.api.thesis.service 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_4b693ddcc20f [waitress-0]
2025-07-29 09:30:47  service.py 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_4b693ddcc20f [waitress-0]
2025-07-29 09:30:47  EarlyBird.api.thesis.service 788: INFO  找到支付记录 - 订单: thesis_download_4b693ddcc20f, 状态: 0 [waitress-0]
2025-07-29 09:30:47  service.py 788: INFO  找到支付记录 - 订单: thesis_download_4b693ddcc20f, 状态: 0 [waitress-0]
2025-07-29 09:30:50  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-2]
2025-07-29 09:30:50  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-2]
2025-07-29 09:30:50  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:30:50  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:30:50  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:30:50  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:30:50  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:30:50  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:30:50  EarlyBird.api.thesis.service 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_4b693ddcc20f [waitress-2]
2025-07-29 09:30:50  service.py 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_4b693ddcc20f [waitress-2]
2025-07-29 09:30:50  EarlyBird.api.thesis.service 788: INFO  找到支付记录 - 订单: thesis_download_4b693ddcc20f, 状态: 0 [waitress-2]
2025-07-29 09:30:50  service.py 788: INFO  找到支付记录 - 订单: thesis_download_4b693ddcc20f, 状态: 0 [waitress-2]
2025-07-29 09:30:53  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-3]
2025-07-29 09:30:53  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-3]
2025-07-29 09:30:53  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:30:53  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:30:53  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:30:53  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:30:53  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:30:53  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:30:53  EarlyBird.api.thesis.service 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_4b693ddcc20f [waitress-3]
2025-07-29 09:30:53  service.py 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_4b693ddcc20f [waitress-3]
2025-07-29 09:30:53  EarlyBird.api.thesis.service 788: INFO  找到支付记录 - 订单: thesis_download_4b693ddcc20f, 状态: 0 [waitress-3]
2025-07-29 09:30:53  service.py 788: INFO  找到支付记录 - 订单: thesis_download_4b693ddcc20f, 状态: 0 [waitress-3]
2025-07-29 09:31:54  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-1]
2025-07-29 09:31:54  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-1]
2025-07-29 09:31:54  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:31:54  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:31:54  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:31:54  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:31:54  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:31:54  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:31:54  EarlyBird.api.thesis.service 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_4b693ddcc20f [waitress-1]
2025-07-29 09:31:54  service.py 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_4b693ddcc20f [waitress-1]
2025-07-29 09:31:54  EarlyBird.api.thesis.service 788: INFO  找到支付记录 - 订单: thesis_download_4b693ddcc20f, 状态: 0 [waitress-1]
2025-07-29 09:31:54  service.py 788: INFO  找到支付记录 - 订单: thesis_download_4b693ddcc20f, 状态: 0 [waitress-1]
2025-07-29 09:32:54  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-0]
2025-07-29 09:32:54  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-0]
2025-07-29 09:32:54  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:32:54  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:32:54  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:32:54  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:32:54  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:32:54  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:32:54  EarlyBird.api.thesis.service 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_4b693ddcc20f [waitress-0]
2025-07-29 09:32:54  service.py 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_4b693ddcc20f [waitress-0]
2025-07-29 09:32:54  EarlyBird.api.thesis.service 788: INFO  找到支付记录 - 订单: thesis_download_4b693ddcc20f, 状态: 0 [waitress-0]
2025-07-29 09:32:54  service.py 788: INFO  找到支付记录 - 订单: thesis_download_4b693ddcc20f, 状态: 0 [waitress-0]
2025-07-29 09:33:52  init_flask.py 27: INFO  {'CpuCount': 6, 'Mem': '32 GB'} [MainThread]
2025-07-29 09:33:52  web_server.py 32: INFO  Static directory: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\resources\ui [MainThread]
2025-07-29 09:33:52  web_server.py 33: INFO  Static directory exists: True [MainThread]
2025-07-29 09:33:52  web_server.py 37: INFO  设置工作目录: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird [MainThread]
2025-07-29 09:33:52  EarlyBird.config.config 21: INFO  load configFile at d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\config/production.ini [MainThread]
2025-07-29 09:33:52  config.py 21: INFO  load configFile at d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\config/production.ini [MainThread]
2025-07-29 09:33:52  config.py 94: INFO  Database URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-29 09:33:55  EarlyBird.api.admin.app 39: INFO  ✅ 后台管理系统API已注册 [MainThread]
2025-07-29 09:33:55  app.py 39: INFO  ✅ 后台管理系统API已注册 [MainThread]
2025-07-29 09:33:55  EarlyBird 40: INFO  确保数据目录存在: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\resources\data [MainThread]
2025-07-29 09:33:55  server.py 40: INFO  确保数据目录存在: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\resources\data [MainThread]
2025-07-29 09:33:55  EarlyBird 44: INFO  开始执行数据库迁移... [MainThread]
2025-07-29 09:33:55  server.py 44: INFO  开始执行数据库迁移... [MainThread]
2025-07-29 09:33:55  config.py 21: INFO  load configFile at d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\config/production.ini [MainThread]
2025-07-29 09:33:55  config.py 94: INFO  Database URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-29 09:33:55  EarlyBird 53: INFO  数据库连接URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-29 09:33:55  server.py 53: INFO  数据库连接URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-29 09:33:55  EarlyBird 97: WARNING  ChatLog表不存在，将在应用启动时自动创建 [MainThread]
2025-07-29 09:33:55  server.py 97: WARNING  ChatLog表不存在，将在应用启动时自动创建 [MainThread]
2025-07-29 09:33:55  add_uid_to_chatlog.py 20: INFO  开始执行ChatLog表迁移... [MainThread]
2025-07-29 09:33:55  add_uid_to_chatlog.py 24: INFO  数据库连接URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-29 09:33:55  add_uid_to_chatlog.py 38: WARNING  ChatLog表不存在，可能需要先创建表 [MainThread]
2025-07-29 09:33:55  task_api_server.py 31: INFO  TaskApiServer start [MainThread]
2025-07-29 09:33:55  task_paper_generator.py 38: INFO  论文生成任务处理线程初始化 [MainThread]
2025-07-29 09:33:55  task_paper_generator.py 72: INFO  论文生成任务处理线程启动 [Thread-2]
2025-07-29 09:33:55  task_api_server.py 47: INFO  TaskApiServer listen on 127.0.0.1:3302 [Thread-1]
2025-07-29 09:33:55  task_api_server.py 17: INFO  _ApiServerThread start [Thread-3]
2025-07-29 09:33:55  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-0]
2025-07-29 09:33:55  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-0]
2025-07-29 09:33:55  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:33:55  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:33:55  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:33:55  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:33:55  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:33:55  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:33:55  EarlyBird.api.thesis.service 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_4b693ddcc20f [waitress-0]
2025-07-29 09:33:55  service.py 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_4b693ddcc20f [waitress-0]
2025-07-29 09:33:55  EarlyBird.api.thesis.service 765: WARNING  未找到下载记录 - 用户: 14, 论文: 32 [waitress-0]
2025-07-29 09:33:55  service.py 765: WARNING  未找到下载记录 - 用户: 14, 论文: 32 [waitress-0]
2025-07-29 09:34:04  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-1]
2025-07-29 09:34:04  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-1]
2025-07-29 09:34:04  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:34:04  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:34:04  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:34:04  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:34:04  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:34:04  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:34:04  EarlyBird.api.thesis.service 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_4b693ddcc20f [waitress-1]
2025-07-29 09:34:04  service.py 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_4b693ddcc20f [waitress-1]
2025-07-29 09:34:04  EarlyBird.api.thesis.service 765: WARNING  未找到下载记录 - 用户: 14, 论文: 32 [waitress-1]
2025-07-29 09:34:04  service.py 765: WARNING  未找到下载记录 - 用户: 14, 论文: 32 [waitress-1]
2025-07-29 09:34:05  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-2]
2025-07-29 09:34:05  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-2]
2025-07-29 09:34:05  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:34:05  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:34:05  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:34:05  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:34:05  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:34:05  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:34:05  EarlyBird.api.thesis.service 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_4b693ddcc20f [waitress-2]
2025-07-29 09:34:05  service.py 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_4b693ddcc20f [waitress-2]
2025-07-29 09:34:05  EarlyBird.api.thesis.service 765: WARNING  未找到下载记录 - 用户: 14, 论文: 32 [waitress-2]
2025-07-29 09:34:05  service.py 765: WARNING  未找到下载记录 - 用户: 14, 论文: 32 [waitress-2]
2025-07-29 09:34:07  web_server.py 73: INFO  Serving request for path: paper/content [waitress-3]
2025-07-29 09:34:07  web_server.py 85: INFO  Serving index.html for path: paper/content [waitress-3]
2025-07-29 09:34:07  web_server.py 73: INFO  Serving request for path: js/chunk-vendors.04aff60f.js [waitress-0]
2025-07-29 09:34:07  web_server.py 73: INFO  Serving request for path: css/chunk-vendors.d5efd04f.css [waitress-2]
2025-07-29 09:34:07  web_server.py 73: INFO  Serving request for path: js/app.4ad3be7b.js [waitress-3]
2025-07-29 09:34:07  web_server.py 73: INFO  Serving request for path: css/app.1e68ddae.css [waitress-1]
2025-07-29 09:34:07  web_server.py 82: INFO  Serving static file: js/chunk-vendors.04aff60f.js [waitress-0]
2025-07-29 09:34:07  web_server.py 82: INFO  Serving static file: css/chunk-vendors.d5efd04f.css [waitress-2]
2025-07-29 09:34:07  web_server.py 82: INFO  Serving static file: js/app.4ad3be7b.js [waitress-3]
2025-07-29 09:34:07  web_server.py 82: INFO  Serving static file: css/app.1e68ddae.css [waitress-1]
2025-07-29 09:34:07  web_server.py 73: INFO  Serving request for path: .well-known/appspecific/com.chrome.devtools.json [waitress-2]
2025-07-29 09:34:07  web_server.py 85: INFO  Serving index.html for path: .well-known/appspecific/com.chrome.devtools.json [waitress-2]
2025-07-29 09:34:07  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/user/info [waitress-0]
2025-07-29 09:34:07  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/user/info [waitress-0]
2025-07-29 09:34:07  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:34:07  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:34:07  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:34:07  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:34:07  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:34:07  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:34:07  web_server.py 73: INFO  Serving request for path: logo.png [waitress-3]
2025-07-29 09:34:07  web_server.py 82: INFO  Serving static file: logo.png [waitress-3]
2025-07-29 09:34:07  web_server.py 73: INFO  Serving request for path: js/22.683bc166.js [waitress-1]
2025-07-29 09:34:07  web_server.py 82: INFO  Serving static file: js/22.683bc166.js [waitress-1]
2025-07-29 09:34:07  web_server.py 73: INFO  Serving request for path: js/897.25207915.js [waitress-2]
2025-07-29 09:34:07  web_server.py 82: INFO  Serving static file: js/897.25207915.js [waitress-2]
2025-07-29 09:34:07  web_server.py 73: INFO  Serving request for path: css/389.ae47f7d6.css [waitress-0]
2025-07-29 09:34:07  web_server.py 73: INFO  Serving request for path: js/389.9dbcae13.js [waitress-1]
2025-07-29 09:34:07  web_server.py 82: INFO  Serving static file: css/389.ae47f7d6.css [waitress-0]
2025-07-29 09:34:07  web_server.py 82: INFO  Serving static file: js/389.9dbcae13.js [waitress-1]
2025-07-29 09:34:07  web_server.py 73: INFO  Serving request for path: img/logo.ce3a5bfd.png [waitress-3]
2025-07-29 09:34:07  web_server.py 82: INFO  Serving static file: img/logo.ce3a5bfd.png [waitress-3]
2025-07-29 09:34:07  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getList [waitress-0]
2025-07-29 09:34:07  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getList [waitress-0]
2025-07-29 09:34:07  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:34:07  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:34:07  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:34:07  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:34:07  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:34:07  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:34:07  web_server.py 73: INFO  Serving request for path: fonts/element-icons.ff18efd1.woff [waitress-1]
2025-07-29 09:34:07  web_server.py 82: INFO  Serving static file: fonts/element-icons.ff18efd1.woff [waitress-1]
2025-07-29 09:34:07  service.py 70: INFO  获取设置成功，包含 8 个配置项 [waitress-2]
2025-07-29 09:34:07  EarlyBird.api.thesis.apis 92: INFO  用户 14 请求获取论文列表 [waitress-0]
2025-07-29 09:34:07  apis.py 92: INFO  用户 14 请求获取论文列表 [waitress-0]
2025-07-29 09:34:07  EarlyBird.api.thesis.service 595: INFO  查询论文列表，用户ID: 14 [waitress-0]
2025-07-29 09:34:07  service.py 595: INFO  查询论文列表，用户ID: 14 [waitress-0]
2025-07-29 09:34:07  EarlyBird.api.thesis.service 601: INFO  查询到 1 篇论文 [waitress-0]
2025-07-29 09:34:07  service.py 601: INFO  查询到 1 篇论文 [waitress-0]
2025-07-29 09:34:07  EarlyBird.api.thesis.service 611: INFO  论文ID: 32, 标题: CES技术在提高电源效率中的应用研究, 用户ID: 14 [waitress-0]
2025-07-29 09:34:07  service.py 611: INFO  论文ID: 32, 标题: CES技术在提高电源效率中的应用研究, 用户ID: 14 [waitress-0]
2025-07-29 09:34:07  EarlyBird.api.thesis.service 613: INFO  返回论文列表，共 1 篇 [waitress-0]
2025-07-29 09:34:07  service.py 613: INFO  返回论文列表，共 1 篇 [waitress-0]
2025-07-29 09:34:07  EarlyBird.api.thesis.apis 95: INFO  用户 14 获取论文列表成功，共 1 篇 [waitress-0]
2025-07-29 09:34:07  apis.py 95: INFO  用户 14 获取论文列表成功，共 1 篇 [waitress-0]
2025-07-29 09:34:07  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getDetail [waitress-3]
2025-07-29 09:34:07  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getDetail [waitress-3]
2025-07-29 09:34:07  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:34:07  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:34:07  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:34:07  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:34:07  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:34:07  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:34:07  EarlyBird.api.talk2ai.utils 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-3]
2025-07-29 09:34:07  utils.py 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-3]
2025-07-29 09:34:07  EarlyBird.api.talk2ai.utils 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-3]
2025-07-29 09:34:07  utils.py 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-3]
2025-07-29 09:34:07  EarlyBird.api.talk2ai.utils 161: INFO  提纲处理完成，根节点ID: E2em6gS9wrhodhQoZ8tQHc [waitress-3]
2025-07-29 09:34:07  utils.py 161: INFO  提纲处理完成，根节点ID: E2em6gS9wrhodhQoZ8tQHc [waitress-3]
2025-07-29 09:34:07  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getOutline [waitress-1]
2025-07-29 09:34:07  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getOutline [waitress-1]
2025-07-29 09:34:07  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:34:07  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:34:07  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:34:07  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:34:07  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:34:07  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:34:07  EarlyBird.api.talk2ai.utils 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-1]
2025-07-29 09:34:07  utils.py 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-1]
2025-07-29 09:34:07  EarlyBird.api.talk2ai.utils 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-1]
2025-07-29 09:34:07  utils.py 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-1]
2025-07-29 09:34:07  EarlyBird.api.talk2ai.utils 161: INFO  提纲处理完成，根节点ID: E2em6gS9wrhodhQoZ8tQHc [waitress-1]
2025-07-29 09:34:07  utils.py 161: INFO  提纲处理完成，根节点ID: E2em6gS9wrhodhQoZ8tQHc [waitress-1]
2025-07-29 09:34:11  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/downloadThesis [waitress-2]
2025-07-29 09:34:11  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/downloadThesis [waitress-2]
2025-07-29 09:34:11  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:34:11  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:34:11  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:34:11  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:34:11  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:34:11  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:34:11  EarlyBird.api.thesis.apis 145: INFO  用户 14 请求下载论文 32 [waitress-2]
2025-07-29 09:34:11  apis.py 145: INFO  用户 14 请求下载论文 32 [waitress-2]
2025-07-29 09:34:11  EarlyBird.api.thesis.service 296: INFO  用户 14 请求导出论文 32 [waitress-2]
2025-07-29 09:34:11  service.py 296: INFO  用户 14 请求导出论文 32 [waitress-2]
2025-07-29 09:34:11  EarlyBird.api.thesis.service 306: INFO  用户 14 VIP状态: <bound method User.isVip of {"accessToken": null, "phone": null, "vip_expire_at": null, "refreshToken": null, "gender": 3, "balance": null, "update_time": "2025-07-29 09:28:33", "nickname": "\u7528\u6237_test1011", "email": null, "id": 14, "headimg": null, "is_lock": false, "invite_code": null, "is_deleted": 0, "activation_key": null, "last_login_time": "2025-07-29 09:28:32", "from_user_id": null, "status": 1, "username": "test1011", "last_login_ip": "127.0.0.1", "openPlatform": "wx", "password_hash": "pbkdf2:sha256:260000$3VYSebusaQuZ1fdD$9217323331b6562a816f82255c4826fd0d815e00bee146926900d409eee250fd", "vip_level": 1, "create_time": "2025-07-28 16:58:53", "openId": null, "realname": null, "vip_start_at": null}> [waitress-2]
2025-07-29 09:34:11  service.py 306: INFO  用户 14 VIP状态: <bound method User.isVip of {"accessToken": null, "phone": null, "vip_expire_at": null, "refreshToken": null, "gender": 3, "balance": null, "update_time": "2025-07-29 09:28:33", "nickname": "\u7528\u6237_test1011", "email": null, "id": 14, "headimg": null, "is_lock": false, "invite_code": null, "is_deleted": 0, "activation_key": null, "last_login_time": "2025-07-29 09:28:32", "from_user_id": null, "status": 1, "username": "test1011", "last_login_ip": "127.0.0.1", "openPlatform": "wx", "password_hash": "pbkdf2:sha256:260000$3VYSebusaQuZ1fdD$9217323331b6562a816f82255c4826fd0d815e00bee146926900d409eee250fd", "vip_level": 1, "create_time": "2025-07-28 16:58:53", "openId": null, "realname": null, "vip_start_at": null}> [waitress-2]
2025-07-29 09:34:11  EarlyBird.api.thesis.service 311: INFO  论文下载收费功能是否启用: True [waitress-2]
2025-07-29 09:34:11  service.py 311: INFO  论文下载收费功能是否启用: True [waitress-2]
2025-07-29 09:34:11  EarlyBird.api.thesis.service 327: INFO  VIP用户是否免费下载: False [waitress-2]
2025-07-29 09:34:11  service.py 327: INFO  VIP用户是否免费下载: False [waitress-2]
2025-07-29 09:34:11  EarlyBird.api.thesis.service 344: INFO  首次下载是否免费: False [waitress-2]
2025-07-29 09:34:11  service.py 344: INFO  首次下载是否免费: False [waitress-2]
2025-07-29 09:34:11  EarlyBird.api.thesis.service 349: INFO  用户 14 是否有任何下载记录: False [waitress-2]
2025-07-29 09:34:11  service.py 349: INFO  用户 14 是否有任何下载记录: False [waitress-2]
2025-07-29 09:34:11  EarlyBird.api.thesis.service 459: INFO  从配置中读取到论文下载价格: 10.0 [waitress-2]
2025-07-29 09:34:11  service.py 459: INFO  从配置中读取到论文下载价格: 10.0 [waitress-2]
2025-07-29 09:34:11  EarlyBird.api.thesis.service 467: INFO  用户 14 需要支付才能下载论文 32，价格: 10.0 [waitress-2]
2025-07-29 09:34:11  service.py 467: INFO  用户 14 需要支付才能下载论文 32，价格: 10.0 [waitress-2]
2025-07-29 09:34:11  EarlyBird.api.thesis.apis 151: INFO  用户 14 需要支付才能下载论文 32，价格: 10.0 [waitress-2]
2025-07-29 09:34:11  apis.py 151: INFO  用户 14 需要支付才能下载论文 32，价格: 10.0 [waitress-2]
2025-07-29 09:34:11  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/payDownload [waitress-0]
2025-07-29 09:34:11  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/payDownload [waitress-0]
2025-07-29 09:34:11  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:34:11  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:34:11  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:34:11  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:34:11  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:34:11  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:34:12  EarlyBird.api.thesis.service 724: INFO  创建支付记录成功 - 订单: thesis_download_4525f3ffa261, 金额: 10.0 [waitress-0]
2025-07-29 09:34:12  service.py 724: INFO  创建支付记录成功 - 订单: thesis_download_4525f3ffa261, 金额: 10.0 [waitress-0]
2025-07-29 09:34:15  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-3]
2025-07-29 09:34:15  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-3]
2025-07-29 09:34:15  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:34:15  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:34:15  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:34:15  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:34:15  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:34:15  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:34:15  EarlyBird.api.thesis.service 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_4525f3ffa261 [waitress-3]
2025-07-29 09:34:15  service.py 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_4525f3ffa261 [waitress-3]
2025-07-29 09:34:15  EarlyBird.api.thesis.service 788: INFO  找到支付记录 - 订单: thesis_download_4525f3ffa261, 状态: 0 [waitress-3]
2025-07-29 09:34:15  service.py 788: INFO  找到支付记录 - 订单: thesis_download_4525f3ffa261, 状态: 0 [waitress-3]
2025-07-29 09:34:16  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/downloadThesis [waitress-1]
2025-07-29 09:34:16  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/downloadThesis [waitress-1]
2025-07-29 09:34:16  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:34:16  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:34:16  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:34:16  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:34:16  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:34:16  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:34:16  EarlyBird.api.thesis.apis 145: INFO  用户 14 请求下载论文 32 [waitress-1]
2025-07-29 09:34:16  apis.py 145: INFO  用户 14 请求下载论文 32 [waitress-1]
2025-07-29 09:34:16  EarlyBird.api.thesis.service 296: INFO  用户 14 请求导出论文 32 [waitress-1]
2025-07-29 09:34:16  service.py 296: INFO  用户 14 请求导出论文 32 [waitress-1]
2025-07-29 09:34:16  EarlyBird.api.thesis.service 306: INFO  用户 14 VIP状态: <bound method User.isVip of {"accessToken": null, "phone": null, "vip_expire_at": null, "refreshToken": null, "gender": 3, "balance": null, "update_time": "2025-07-29 09:28:33", "nickname": "\u7528\u6237_test1011", "email": null, "id": 14, "headimg": null, "is_lock": false, "invite_code": null, "is_deleted": 0, "activation_key": null, "last_login_time": "2025-07-29 09:28:32", "from_user_id": null, "status": 1, "username": "test1011", "last_login_ip": "127.0.0.1", "openPlatform": "wx", "password_hash": "pbkdf2:sha256:260000$3VYSebusaQuZ1fdD$9217323331b6562a816f82255c4826fd0d815e00bee146926900d409eee250fd", "vip_level": 1, "create_time": "2025-07-28 16:58:53", "openId": null, "realname": null, "vip_start_at": null}> [waitress-1]
2025-07-29 09:34:16  service.py 306: INFO  用户 14 VIP状态: <bound method User.isVip of {"accessToken": null, "phone": null, "vip_expire_at": null, "refreshToken": null, "gender": 3, "balance": null, "update_time": "2025-07-29 09:28:33", "nickname": "\u7528\u6237_test1011", "email": null, "id": 14, "headimg": null, "is_lock": false, "invite_code": null, "is_deleted": 0, "activation_key": null, "last_login_time": "2025-07-29 09:28:32", "from_user_id": null, "status": 1, "username": "test1011", "last_login_ip": "127.0.0.1", "openPlatform": "wx", "password_hash": "pbkdf2:sha256:260000$3VYSebusaQuZ1fdD$9217323331b6562a816f82255c4826fd0d815e00bee146926900d409eee250fd", "vip_level": 1, "create_time": "2025-07-28 16:58:53", "openId": null, "realname": null, "vip_start_at": null}> [waitress-1]
2025-07-29 09:34:16  EarlyBird.api.thesis.service 311: INFO  论文下载收费功能是否启用: True [waitress-1]
2025-07-29 09:34:16  service.py 311: INFO  论文下载收费功能是否启用: True [waitress-1]
2025-07-29 09:34:16  EarlyBird.api.thesis.service 327: INFO  VIP用户是否免费下载: False [waitress-1]
2025-07-29 09:34:16  service.py 327: INFO  VIP用户是否免费下载: False [waitress-1]
2025-07-29 09:34:16  EarlyBird.api.thesis.service 344: INFO  首次下载是否免费: False [waitress-1]
2025-07-29 09:34:16  service.py 344: INFO  首次下载是否免费: False [waitress-1]
2025-07-29 09:34:16  EarlyBird.api.thesis.service 349: INFO  用户 14 是否有任何下载记录: True [waitress-1]
2025-07-29 09:34:16  service.py 349: INFO  用户 14 是否有任何下载记录: True [waitress-1]
2025-07-29 09:34:16  EarlyBird.api.thesis.service 459: INFO  从配置中读取到论文下载价格: 10.0 [waitress-1]
2025-07-29 09:34:16  service.py 459: INFO  从配置中读取到论文下载价格: 10.0 [waitress-1]
2025-07-29 09:34:16  EarlyBird.api.thesis.service 467: INFO  用户 14 需要支付才能下载论文 32，价格: 10.0 [waitress-1]
2025-07-29 09:34:16  service.py 467: INFO  用户 14 需要支付才能下载论文 32，价格: 10.0 [waitress-1]
2025-07-29 09:34:16  EarlyBird.api.thesis.apis 151: INFO  用户 14 需要支付才能下载论文 32，价格: 10.0 [waitress-1]
2025-07-29 09:34:16  apis.py 151: INFO  用户 14 需要支付才能下载论文 32，价格: 10.0 [waitress-1]
2025-07-29 09:34:16  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/payDownload [waitress-2]
2025-07-29 09:34:16  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/payDownload [waitress-2]
2025-07-29 09:34:16  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:34:16  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:34:16  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:34:16  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:34:16  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:34:16  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:34:17  EarlyBird.api.thesis.service 724: INFO  创建支付记录成功 - 订单: thesis_download_2a53f9d22452, 金额: 10.0 [waitress-2]
2025-07-29 09:34:17  service.py 724: INFO  创建支付记录成功 - 订单: thesis_download_2a53f9d22452, 金额: 10.0 [waitress-2]
2025-07-29 09:37:37  init_flask.py 27: INFO  {'CpuCount': 6, 'Mem': '32 GB'} [MainThread]
2025-07-29 09:37:37  web_server.py 32: INFO  Static directory: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\resources\ui [MainThread]
2025-07-29 09:37:37  web_server.py 33: INFO  Static directory exists: True [MainThread]
2025-07-29 09:37:37  web_server.py 37: INFO  设置工作目录: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird [MainThread]
2025-07-29 09:37:37  EarlyBird.config.config 21: INFO  load configFile at d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\config/production.ini [MainThread]
2025-07-29 09:37:37  config.py 21: INFO  load configFile at d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\config/production.ini [MainThread]
2025-07-29 09:37:37  config.py 94: INFO  Database URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-29 09:37:39  EarlyBird.api.admin.app 39: INFO  ✅ 后台管理系统API已注册 [MainThread]
2025-07-29 09:37:39  app.py 39: INFO  ✅ 后台管理系统API已注册 [MainThread]
2025-07-29 09:37:39  EarlyBird 40: INFO  确保数据目录存在: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\resources\data [MainThread]
2025-07-29 09:37:39  server.py 40: INFO  确保数据目录存在: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\resources\data [MainThread]
2025-07-29 09:37:39  EarlyBird 44: INFO  开始执行数据库迁移... [MainThread]
2025-07-29 09:37:39  server.py 44: INFO  开始执行数据库迁移... [MainThread]
2025-07-29 09:37:39  config.py 21: INFO  load configFile at d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\config/production.ini [MainThread]
2025-07-29 09:37:39  config.py 94: INFO  Database URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-29 09:37:39  EarlyBird 53: INFO  数据库连接URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-29 09:37:39  server.py 53: INFO  数据库连接URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-29 09:37:39  EarlyBird 97: WARNING  ChatLog表不存在，将在应用启动时自动创建 [MainThread]
2025-07-29 09:37:39  server.py 97: WARNING  ChatLog表不存在，将在应用启动时自动创建 [MainThread]
2025-07-29 09:37:39  add_uid_to_chatlog.py 20: INFO  开始执行ChatLog表迁移... [MainThread]
2025-07-29 09:37:39  add_uid_to_chatlog.py 24: INFO  数据库连接URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-29 09:37:39  add_uid_to_chatlog.py 38: WARNING  ChatLog表不存在，可能需要先创建表 [MainThread]
2025-07-29 09:37:39  task_api_server.py 31: INFO  TaskApiServer start [MainThread]
2025-07-29 09:37:39  task_paper_generator.py 38: INFO  论文生成任务处理线程初始化 [MainThread]
2025-07-29 09:37:39  task_paper_generator.py 72: INFO  论文生成任务处理线程启动 [Thread-2]
2025-07-29 09:37:39  task_api_server.py 47: INFO  TaskApiServer listen on 127.0.0.1:3302 [Thread-1]
2025-07-29 09:37:39  task_api_server.py 17: INFO  _ApiServerThread start [Thread-3]
2025-07-29 09:37:45  web_server.py 73: INFO  Serving request for path: paper/content [waitress-0]
2025-07-29 09:37:45  web_server.py 85: INFO  Serving index.html for path: paper/content [waitress-0]
2025-07-29 09:37:45  web_server.py 73: INFO  Serving request for path: js/chunk-vendors.04aff60f.js [waitress-1]
2025-07-29 09:37:45  web_server.py 82: INFO  Serving static file: js/chunk-vendors.04aff60f.js [waitress-1]
2025-07-29 09:37:45  web_server.py 73: INFO  Serving request for path: css/chunk-vendors.d5efd04f.css [waitress-2]
2025-07-29 09:37:45  web_server.py 73: INFO  Serving request for path: css/app.1e68ddae.css [waitress-3]
2025-07-29 09:37:45  web_server.py 82: INFO  Serving static file: css/chunk-vendors.d5efd04f.css [waitress-2]
2025-07-29 09:37:45  web_server.py 73: INFO  Serving request for path: js/app.4ad3be7b.js [waitress-0]
2025-07-29 09:37:45  web_server.py 82: INFO  Serving static file: css/app.1e68ddae.css [waitress-3]
2025-07-29 09:37:45  web_server.py 82: INFO  Serving static file: js/app.4ad3be7b.js [waitress-0]
2025-07-29 09:37:45  web_server.py 73: INFO  Serving request for path: .well-known/appspecific/com.chrome.devtools.json [waitress-1]
2025-07-29 09:37:45  web_server.py 85: INFO  Serving index.html for path: .well-known/appspecific/com.chrome.devtools.json [waitress-1]
2025-07-29 09:37:45  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/user/info [waitress-2]
2025-07-29 09:37:45  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/user/info [waitress-2]
2025-07-29 09:37:45  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:37:45  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:37:45  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:37:45  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:37:45  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:37:45  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:37:45  web_server.py 73: INFO  Serving request for path: logo.png [waitress-3]
2025-07-29 09:37:45  web_server.py 82: INFO  Serving static file: logo.png [waitress-3]
2025-07-29 09:37:45  web_server.py 73: INFO  Serving request for path: js/22.683bc166.js [waitress-0]
2025-07-29 09:37:45  web_server.py 73: INFO  Serving request for path: js/897.25207915.js [waitress-1]
2025-07-29 09:37:45  web_server.py 82: INFO  Serving static file: js/22.683bc166.js [waitress-0]
2025-07-29 09:37:45  web_server.py 82: INFO  Serving static file: js/897.25207915.js [waitress-1]
2025-07-29 09:37:45  web_server.py 73: INFO  Serving request for path: css/389.ae47f7d6.css [waitress-3]
2025-07-29 09:37:45  web_server.py 82: INFO  Serving static file: css/389.ae47f7d6.css [waitress-3]
2025-07-29 09:37:45  web_server.py 73: INFO  Serving request for path: js/389.9dbcae13.js [waitress-2]
2025-07-29 09:37:45  web_server.py 82: INFO  Serving static file: js/389.9dbcae13.js [waitress-2]
2025-07-29 09:37:45  web_server.py 73: INFO  Serving request for path: img/logo.ce3a5bfd.png [waitress-0]
2025-07-29 09:37:45  web_server.py 82: INFO  Serving static file: img/logo.ce3a5bfd.png [waitress-0]
2025-07-29 09:37:45  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getList [waitress-3]
2025-07-29 09:37:45  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getList [waitress-3]
2025-07-29 09:37:45  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:37:45  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:37:45  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:37:45  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:37:45  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:37:45  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:37:45  service.py 70: INFO  获取设置成功，包含 8 个配置项 [waitress-1]
2025-07-29 09:37:45  EarlyBird.api.thesis.apis 92: INFO  用户 14 请求获取论文列表 [waitress-3]
2025-07-29 09:37:45  apis.py 92: INFO  用户 14 请求获取论文列表 [waitress-3]
2025-07-29 09:37:45  EarlyBird.api.thesis.service 595: INFO  查询论文列表，用户ID: 14 [waitress-3]
2025-07-29 09:37:45  service.py 595: INFO  查询论文列表，用户ID: 14 [waitress-3]
2025-07-29 09:37:45  EarlyBird.api.thesis.service 601: INFO  查询到 1 篇论文 [waitress-3]
2025-07-29 09:37:45  service.py 601: INFO  查询到 1 篇论文 [waitress-3]
2025-07-29 09:37:45  EarlyBird.api.thesis.service 611: INFO  论文ID: 32, 标题: CES技术在提高电源效率中的应用研究, 用户ID: 14 [waitress-3]
2025-07-29 09:37:45  service.py 611: INFO  论文ID: 32, 标题: CES技术在提高电源效率中的应用研究, 用户ID: 14 [waitress-3]
2025-07-29 09:37:45  EarlyBird.api.thesis.service 613: INFO  返回论文列表，共 1 篇 [waitress-3]
2025-07-29 09:37:45  service.py 613: INFO  返回论文列表，共 1 篇 [waitress-3]
2025-07-29 09:37:45  EarlyBird.api.thesis.apis 95: INFO  用户 14 获取论文列表成功，共 1 篇 [waitress-3]
2025-07-29 09:37:45  apis.py 95: INFO  用户 14 获取论文列表成功，共 1 篇 [waitress-3]
2025-07-29 09:37:45  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getDetail [waitress-2]
2025-07-29 09:37:45  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getDetail [waitress-2]
2025-07-29 09:37:45  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:37:45  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:37:45  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:37:45  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:37:45  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:37:45  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:37:45  EarlyBird.api.talk2ai.utils 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-2]
2025-07-29 09:37:45  utils.py 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-2]
2025-07-29 09:37:45  EarlyBird.api.talk2ai.utils 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-2]
2025-07-29 09:37:45  utils.py 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-2]
2025-07-29 09:37:45  EarlyBird.api.talk2ai.utils 161: INFO  提纲处理完成，根节点ID: E2em6gS9wrhodhQoZ8tQHc [waitress-2]
2025-07-29 09:37:45  utils.py 161: INFO  提纲处理完成，根节点ID: E2em6gS9wrhodhQoZ8tQHc [waitress-2]
2025-07-29 09:37:45  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getOutline [waitress-0]
2025-07-29 09:37:45  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getOutline [waitress-0]
2025-07-29 09:37:45  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:37:45  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:37:45  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:37:45  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:37:45  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:37:45  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:37:45  EarlyBird.api.talk2ai.utils 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-0]
2025-07-29 09:37:45  utils.py 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-0]
2025-07-29 09:37:45  EarlyBird.api.talk2ai.utils 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-0]
2025-07-29 09:37:45  utils.py 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-0]
2025-07-29 09:37:45  EarlyBird.api.talk2ai.utils 161: INFO  提纲处理完成，根节点ID: E2em6gS9wrhodhQoZ8tQHc [waitress-0]
2025-07-29 09:37:45  utils.py 161: INFO  提纲处理完成，根节点ID: E2em6gS9wrhodhQoZ8tQHc [waitress-0]
2025-07-29 09:37:48  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/downloadThesis [waitress-1]
2025-07-29 09:37:48  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/downloadThesis [waitress-1]
2025-07-29 09:37:48  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:37:48  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:37:48  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:37:48  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:37:48  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:37:48  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:37:48  EarlyBird.api.thesis.apis 145: INFO  用户 14 请求下载论文 32 [waitress-1]
2025-07-29 09:37:48  apis.py 145: INFO  用户 14 请求下载论文 32 [waitress-1]
2025-07-29 09:37:48  EarlyBird.api.thesis.service 296: INFO  用户 14 请求导出论文 32 [waitress-1]
2025-07-29 09:37:48  service.py 296: INFO  用户 14 请求导出论文 32 [waitress-1]
2025-07-29 09:37:48  EarlyBird.api.thesis.service 306: INFO  用户 14 VIP状态: <bound method User.isVip of {"accessToken": null, "phone": null, "vip_expire_at": null, "refreshToken": null, "gender": 3, "balance": null, "update_time": "2025-07-29 09:28:33", "nickname": "\u7528\u6237_test1011", "email": null, "id": 14, "headimg": null, "is_lock": false, "invite_code": null, "is_deleted": 0, "activation_key": null, "last_login_time": "2025-07-29 09:28:32", "from_user_id": null, "status": 1, "username": "test1011", "last_login_ip": "127.0.0.1", "openPlatform": "wx", "password_hash": "pbkdf2:sha256:260000$3VYSebusaQuZ1fdD$9217323331b6562a816f82255c4826fd0d815e00bee146926900d409eee250fd", "vip_level": 1, "create_time": "2025-07-28 16:58:53", "openId": null, "realname": null, "vip_start_at": null}> [waitress-1]
2025-07-29 09:37:48  service.py 306: INFO  用户 14 VIP状态: <bound method User.isVip of {"accessToken": null, "phone": null, "vip_expire_at": null, "refreshToken": null, "gender": 3, "balance": null, "update_time": "2025-07-29 09:28:33", "nickname": "\u7528\u6237_test1011", "email": null, "id": 14, "headimg": null, "is_lock": false, "invite_code": null, "is_deleted": 0, "activation_key": null, "last_login_time": "2025-07-29 09:28:32", "from_user_id": null, "status": 1, "username": "test1011", "last_login_ip": "127.0.0.1", "openPlatform": "wx", "password_hash": "pbkdf2:sha256:260000$3VYSebusaQuZ1fdD$9217323331b6562a816f82255c4826fd0d815e00bee146926900d409eee250fd", "vip_level": 1, "create_time": "2025-07-28 16:58:53", "openId": null, "realname": null, "vip_start_at": null}> [waitress-1]
2025-07-29 09:37:48  EarlyBird.api.thesis.service 311: INFO  论文下载收费功能是否启用: True [waitress-1]
2025-07-29 09:37:48  service.py 311: INFO  论文下载收费功能是否启用: True [waitress-1]
2025-07-29 09:37:48  EarlyBird.api.thesis.service 327: INFO  VIP用户是否免费下载: False [waitress-1]
2025-07-29 09:37:48  service.py 327: INFO  VIP用户是否免费下载: False [waitress-1]
2025-07-29 09:37:48  EarlyBird.api.thesis.service 344: INFO  首次下载是否免费: False [waitress-1]
2025-07-29 09:37:48  service.py 344: INFO  首次下载是否免费: False [waitress-1]
2025-07-29 09:37:48  EarlyBird.api.thesis.service 349: INFO  用户 14 是否有任何下载记录: False [waitress-1]
2025-07-29 09:37:48  service.py 349: INFO  用户 14 是否有任何下载记录: False [waitress-1]
2025-07-29 09:37:48  EarlyBird.api.thesis.service 459: INFO  从配置中读取到论文下载价格: 10.0 [waitress-1]
2025-07-29 09:37:48  service.py 459: INFO  从配置中读取到论文下载价格: 10.0 [waitress-1]
2025-07-29 09:37:48  EarlyBird.api.thesis.service 467: INFO  用户 14 需要支付才能下载论文 32，价格: 10.0 [waitress-1]
2025-07-29 09:37:48  service.py 467: INFO  用户 14 需要支付才能下载论文 32，价格: 10.0 [waitress-1]
2025-07-29 09:37:48  EarlyBird.api.thesis.apis 151: INFO  用户 14 需要支付才能下载论文 32，价格: 10.0 [waitress-1]
2025-07-29 09:37:48  apis.py 151: INFO  用户 14 需要支付才能下载论文 32，价格: 10.0 [waitress-1]
2025-07-29 09:37:48  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/payDownload [waitress-3]
2025-07-29 09:37:48  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/payDownload [waitress-3]
2025-07-29 09:37:48  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:37:48  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:37:48  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:37:48  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:37:48  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:37:48  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:37:48  EarlyBird.api.thesis.service 724: INFO  创建支付记录成功 - 订单: thesis_download_31d4be112043, 金额: 10.0 [waitress-3]
2025-07-29 09:37:48  service.py 724: INFO  创建支付记录成功 - 订单: thesis_download_31d4be112043, 金额: 10.0 [waitress-3]
2025-07-29 09:37:51  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-2]
2025-07-29 09:37:51  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-2]
2025-07-29 09:37:51  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:37:51  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:37:51  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:37:51  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:37:51  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:37:51  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:37:51  EarlyBird.api.thesis.service 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_31d4be112043 [waitress-2]
2025-07-29 09:37:51  service.py 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_31d4be112043 [waitress-2]
2025-07-29 09:37:51  EarlyBird.api.thesis.service 788: INFO  找到支付记录 - 订单: thesis_download_31d4be112043, 状态: 0 [waitress-2]
2025-07-29 09:37:51  service.py 788: INFO  找到支付记录 - 订单: thesis_download_31d4be112043, 状态: 0 [waitress-2]
2025-07-29 09:37:52  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/downloadThesis [waitress-0]
2025-07-29 09:37:52  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/downloadThesis [waitress-0]
2025-07-29 09:37:52  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:37:52  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:37:52  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:37:52  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:37:52  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:37:52  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:37:52  EarlyBird.api.thesis.apis 145: INFO  用户 14 请求下载论文 32 [waitress-0]
2025-07-29 09:37:52  apis.py 145: INFO  用户 14 请求下载论文 32 [waitress-0]
2025-07-29 09:37:52  EarlyBird.api.thesis.service 296: INFO  用户 14 请求导出论文 32 [waitress-0]
2025-07-29 09:37:52  service.py 296: INFO  用户 14 请求导出论文 32 [waitress-0]
2025-07-29 09:37:52  EarlyBird.api.thesis.service 306: INFO  用户 14 VIP状态: <bound method User.isVip of {"accessToken": null, "phone": null, "vip_expire_at": null, "refreshToken": null, "gender": 3, "balance": null, "update_time": "2025-07-29 09:28:33", "nickname": "\u7528\u6237_test1011", "email": null, "id": 14, "headimg": null, "is_lock": false, "invite_code": null, "is_deleted": 0, "activation_key": null, "last_login_time": "2025-07-29 09:28:32", "from_user_id": null, "status": 1, "username": "test1011", "last_login_ip": "127.0.0.1", "openPlatform": "wx", "password_hash": "pbkdf2:sha256:260000$3VYSebusaQuZ1fdD$9217323331b6562a816f82255c4826fd0d815e00bee146926900d409eee250fd", "vip_level": 1, "create_time": "2025-07-28 16:58:53", "openId": null, "realname": null, "vip_start_at": null}> [waitress-0]
2025-07-29 09:37:52  service.py 306: INFO  用户 14 VIP状态: <bound method User.isVip of {"accessToken": null, "phone": null, "vip_expire_at": null, "refreshToken": null, "gender": 3, "balance": null, "update_time": "2025-07-29 09:28:33", "nickname": "\u7528\u6237_test1011", "email": null, "id": 14, "headimg": null, "is_lock": false, "invite_code": null, "is_deleted": 0, "activation_key": null, "last_login_time": "2025-07-29 09:28:32", "from_user_id": null, "status": 1, "username": "test1011", "last_login_ip": "127.0.0.1", "openPlatform": "wx", "password_hash": "pbkdf2:sha256:260000$3VYSebusaQuZ1fdD$9217323331b6562a816f82255c4826fd0d815e00bee146926900d409eee250fd", "vip_level": 1, "create_time": "2025-07-28 16:58:53", "openId": null, "realname": null, "vip_start_at": null}> [waitress-0]
2025-07-29 09:37:52  EarlyBird.api.thesis.service 311: INFO  论文下载收费功能是否启用: True [waitress-0]
2025-07-29 09:37:52  service.py 311: INFO  论文下载收费功能是否启用: True [waitress-0]
2025-07-29 09:37:52  EarlyBird.api.thesis.service 327: INFO  VIP用户是否免费下载: False [waitress-0]
2025-07-29 09:37:52  service.py 327: INFO  VIP用户是否免费下载: False [waitress-0]
2025-07-29 09:37:52  EarlyBird.api.thesis.service 344: INFO  首次下载是否免费: False [waitress-0]
2025-07-29 09:37:52  service.py 344: INFO  首次下载是否免费: False [waitress-0]
2025-07-29 09:37:52  EarlyBird.api.thesis.service 349: INFO  用户 14 是否有任何下载记录: True [waitress-0]
2025-07-29 09:37:52  service.py 349: INFO  用户 14 是否有任何下载记录: True [waitress-0]
2025-07-29 09:37:52  EarlyBird.api.thesis.service 459: INFO  从配置中读取到论文下载价格: 10.0 [waitress-0]
2025-07-29 09:37:52  service.py 459: INFO  从配置中读取到论文下载价格: 10.0 [waitress-0]
2025-07-29 09:37:52  EarlyBird.api.thesis.service 467: INFO  用户 14 需要支付才能下载论文 32，价格: 10.0 [waitress-0]
2025-07-29 09:37:52  service.py 467: INFO  用户 14 需要支付才能下载论文 32，价格: 10.0 [waitress-0]
2025-07-29 09:37:52  EarlyBird.api.thesis.apis 151: INFO  用户 14 需要支付才能下载论文 32，价格: 10.0 [waitress-0]
2025-07-29 09:37:52  apis.py 151: INFO  用户 14 需要支付才能下载论文 32，价格: 10.0 [waitress-0]
2025-07-29 09:37:52  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/payDownload [waitress-1]
2025-07-29 09:37:52  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/payDownload [waitress-1]
2025-07-29 09:37:52  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:37:52  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:37:52  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:37:52  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:37:52  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:37:52  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:37:52  EarlyBird.api.thesis.service 724: INFO  创建支付记录成功 - 订单: thesis_download_f737dceb9100, 金额: 10.0 [waitress-1]
2025-07-29 09:37:52  service.py 724: INFO  创建支付记录成功 - 订单: thesis_download_f737dceb9100, 金额: 10.0 [waitress-1]
2025-07-29 09:37:55  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-3]
2025-07-29 09:37:55  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-3]
2025-07-29 09:37:55  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:37:55  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:37:55  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:37:55  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:37:55  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:37:55  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:37:55  EarlyBird.api.thesis.service 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_f737dceb9100 [waitress-3]
2025-07-29 09:37:55  service.py 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_f737dceb9100 [waitress-3]
2025-07-29 09:37:55  EarlyBird.api.thesis.service 788: INFO  找到支付记录 - 订单: thesis_download_f737dceb9100, 状态: 0 [waitress-3]
2025-07-29 09:37:55  service.py 788: INFO  找到支付记录 - 订单: thesis_download_f737dceb9100, 状态: 0 [waitress-3]
2025-07-29 09:37:58  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-2]
2025-07-29 09:37:58  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-2]
2025-07-29 09:37:58  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:37:58  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:37:58  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:37:58  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:37:58  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:37:58  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:37:58  EarlyBird.api.thesis.service 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_f737dceb9100 [waitress-2]
2025-07-29 09:37:58  service.py 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_f737dceb9100 [waitress-2]
2025-07-29 09:37:58  EarlyBird.api.thesis.service 788: INFO  找到支付记录 - 订单: thesis_download_f737dceb9100, 状态: 0 [waitress-2]
2025-07-29 09:37:58  service.py 788: INFO  找到支付记录 - 订单: thesis_download_f737dceb9100, 状态: 0 [waitress-2]
2025-07-29 09:38:01  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-0]
2025-07-29 09:38:01  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-0]
2025-07-29 09:38:01  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:38:01  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:38:01  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:38:01  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:38:01  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:38:01  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:38:01  EarlyBird.api.thesis.service 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_f737dceb9100 [waitress-0]
2025-07-29 09:38:01  service.py 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_f737dceb9100 [waitress-0]
2025-07-29 09:38:01  EarlyBird.api.thesis.service 788: INFO  找到支付记录 - 订单: thesis_download_f737dceb9100, 状态: 0 [waitress-0]
2025-07-29 09:38:01  service.py 788: INFO  找到支付记录 - 订单: thesis_download_f737dceb9100, 状态: 0 [waitress-0]
2025-07-29 09:38:04  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-1]
2025-07-29 09:38:04  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-1]
2025-07-29 09:38:04  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:38:04  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:38:04  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:38:04  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:38:04  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:38:04  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:38:04  EarlyBird.api.thesis.service 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_f737dceb9100 [waitress-1]
2025-07-29 09:38:04  service.py 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_f737dceb9100 [waitress-1]
2025-07-29 09:38:04  EarlyBird.api.thesis.service 788: INFO  找到支付记录 - 订单: thesis_download_f737dceb9100, 状态: 0 [waitress-1]
2025-07-29 09:38:04  service.py 788: INFO  找到支付记录 - 订单: thesis_download_f737dceb9100, 状态: 0 [waitress-1]
2025-07-29 09:38:07  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-3]
2025-07-29 09:38:07  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-3]
2025-07-29 09:38:07  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:38:07  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:38:07  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:38:07  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:38:07  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:38:07  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:38:07  EarlyBird.api.thesis.service 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_f737dceb9100 [waitress-3]
2025-07-29 09:38:07  service.py 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_f737dceb9100 [waitress-3]
2025-07-29 09:38:07  EarlyBird.api.thesis.service 788: INFO  找到支付记录 - 订单: thesis_download_f737dceb9100, 状态: 0 [waitress-3]
2025-07-29 09:38:07  service.py 788: INFO  找到支付记录 - 订单: thesis_download_f737dceb9100, 状态: 0 [waitress-3]
2025-07-29 09:38:10  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-2]
2025-07-29 09:38:10  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-2]
2025-07-29 09:38:10  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:38:10  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:38:10  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:38:10  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:38:10  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:38:10  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:38:10  EarlyBird.api.thesis.service 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_f737dceb9100 [waitress-2]
2025-07-29 09:38:10  service.py 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_f737dceb9100 [waitress-2]
2025-07-29 09:38:10  EarlyBird.api.thesis.service 788: INFO  找到支付记录 - 订单: thesis_download_f737dceb9100, 状态: 0 [waitress-2]
2025-07-29 09:38:10  service.py 788: INFO  找到支付记录 - 订单: thesis_download_f737dceb9100, 状态: 0 [waitress-2]
2025-07-29 09:38:13  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-0]
2025-07-29 09:38:13  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-0]
2025-07-29 09:38:13  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:38:13  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:38:13  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:38:13  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:38:13  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:38:13  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:38:13  EarlyBird.api.thesis.service 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_f737dceb9100 [waitress-0]
2025-07-29 09:38:13  service.py 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_f737dceb9100 [waitress-0]
2025-07-29 09:38:13  EarlyBird.api.thesis.service 788: INFO  找到支付记录 - 订单: thesis_download_f737dceb9100, 状态: 0 [waitress-0]
2025-07-29 09:38:13  service.py 788: INFO  找到支付记录 - 订单: thesis_download_f737dceb9100, 状态: 0 [waitress-0]
2025-07-29 09:38:16  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-1]
2025-07-29 09:38:16  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-1]
2025-07-29 09:38:16  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:38:16  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:38:16  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:38:16  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:38:16  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:38:16  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:38:16  EarlyBird.api.thesis.service 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_f737dceb9100 [waitress-1]
2025-07-29 09:38:16  service.py 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_f737dceb9100 [waitress-1]
2025-07-29 09:38:16  EarlyBird.api.thesis.service 788: INFO  找到支付记录 - 订单: thesis_download_f737dceb9100, 状态: 0 [waitress-1]
2025-07-29 09:38:16  service.py 788: INFO  找到支付记录 - 订单: thesis_download_f737dceb9100, 状态: 0 [waitress-1]
2025-07-29 09:38:19  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-3]
2025-07-29 09:38:19  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-3]
2025-07-29 09:38:19  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:38:19  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:38:19  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:38:19  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:38:19  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:38:19  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:38:19  EarlyBird.api.thesis.service 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_f737dceb9100 [waitress-3]
2025-07-29 09:38:19  service.py 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_f737dceb9100 [waitress-3]
2025-07-29 09:38:19  EarlyBird.api.thesis.service 788: INFO  找到支付记录 - 订单: thesis_download_f737dceb9100, 状态: 0 [waitress-3]
2025-07-29 09:38:19  service.py 788: INFO  找到支付记录 - 订单: thesis_download_f737dceb9100, 状态: 0 [waitress-3]
2025-07-29 09:38:22  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-2]
2025-07-29 09:38:22  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-2]
2025-07-29 09:38:22  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:38:22  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:38:22  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:38:22  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:38:22  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:38:22  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:38:22  EarlyBird.api.thesis.service 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_f737dceb9100 [waitress-2]
2025-07-29 09:38:22  service.py 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_f737dceb9100 [waitress-2]
2025-07-29 09:38:22  EarlyBird.api.thesis.service 788: INFO  找到支付记录 - 订单: thesis_download_f737dceb9100, 状态: 0 [waitress-2]
2025-07-29 09:38:22  service.py 788: INFO  找到支付记录 - 订单: thesis_download_f737dceb9100, 状态: 0 [waitress-2]
2025-07-29 09:38:25  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-0]
2025-07-29 09:38:25  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-0]
2025-07-29 09:38:25  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:38:25  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:38:25  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:38:25  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:38:25  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:38:25  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:38:25  EarlyBird.api.thesis.service 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_f737dceb9100 [waitress-0]
2025-07-29 09:38:25  service.py 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_f737dceb9100 [waitress-0]
2025-07-29 09:38:25  EarlyBird.api.thesis.service 788: INFO  找到支付记录 - 订单: thesis_download_f737dceb9100, 状态: 0 [waitress-0]
2025-07-29 09:38:25  service.py 788: INFO  找到支付记录 - 订单: thesis_download_f737dceb9100, 状态: 0 [waitress-0]
2025-07-29 09:38:28  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-1]
2025-07-29 09:38:28  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-1]
2025-07-29 09:38:28  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:38:28  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:38:28  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:38:28  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:38:28  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:38:28  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:38:28  EarlyBird.api.thesis.service 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_f737dceb9100 [waitress-1]
2025-07-29 09:38:28  service.py 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_f737dceb9100 [waitress-1]
2025-07-29 09:38:28  EarlyBird.api.thesis.service 788: INFO  找到支付记录 - 订单: thesis_download_f737dceb9100, 状态: 0 [waitress-1]
2025-07-29 09:38:28  service.py 788: INFO  找到支付记录 - 订单: thesis_download_f737dceb9100, 状态: 0 [waitress-1]
2025-07-29 09:38:31  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-3]
2025-07-29 09:38:31  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-3]
2025-07-29 09:38:31  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:38:31  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:38:31  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:38:31  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:38:31  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:38:31  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:38:31  EarlyBird.api.thesis.service 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_f737dceb9100 [waitress-3]
2025-07-29 09:38:31  service.py 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_f737dceb9100 [waitress-3]
2025-07-29 09:38:31  EarlyBird.api.thesis.service 788: INFO  找到支付记录 - 订单: thesis_download_f737dceb9100, 状态: 0 [waitress-3]
2025-07-29 09:38:31  service.py 788: INFO  找到支付记录 - 订单: thesis_download_f737dceb9100, 状态: 0 [waitress-3]
2025-07-29 09:38:34  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-2]
2025-07-29 09:38:34  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-2]
2025-07-29 09:38:34  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:38:34  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:38:34  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:38:34  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:38:34  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:38:34  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:38:34  EarlyBird.api.thesis.service 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_f737dceb9100 [waitress-2]
2025-07-29 09:38:34  service.py 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_f737dceb9100 [waitress-2]
2025-07-29 09:38:34  EarlyBird.api.thesis.service 788: INFO  找到支付记录 - 订单: thesis_download_f737dceb9100, 状态: 0 [waitress-2]
2025-07-29 09:38:34  service.py 788: INFO  找到支付记录 - 订单: thesis_download_f737dceb9100, 状态: 0 [waitress-2]
2025-07-29 09:38:37  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-0]
2025-07-29 09:38:37  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-0]
2025-07-29 09:38:37  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:38:37  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:38:37  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:38:37  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:38:37  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:38:37  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:38:37  EarlyBird.api.thesis.service 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_f737dceb9100 [waitress-0]
2025-07-29 09:38:37  service.py 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_f737dceb9100 [waitress-0]
2025-07-29 09:38:37  EarlyBird.api.thesis.service 788: INFO  找到支付记录 - 订单: thesis_download_f737dceb9100, 状态: 0 [waitress-0]
2025-07-29 09:38:37  service.py 788: INFO  找到支付记录 - 订单: thesis_download_f737dceb9100, 状态: 0 [waitress-0]
2025-07-29 09:38:40  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-1]
2025-07-29 09:38:40  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-1]
2025-07-29 09:38:40  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:38:40  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:38:40  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:38:40  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:38:40  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:38:40  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:38:40  EarlyBird.api.thesis.service 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_f737dceb9100 [waitress-1]
2025-07-29 09:38:40  service.py 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_f737dceb9100 [waitress-1]
2025-07-29 09:38:40  EarlyBird.api.thesis.service 788: INFO  找到支付记录 - 订单: thesis_download_f737dceb9100, 状态: 0 [waitress-1]
2025-07-29 09:38:40  service.py 788: INFO  找到支付记录 - 订单: thesis_download_f737dceb9100, 状态: 0 [waitress-1]
2025-07-29 09:38:43  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-3]
2025-07-29 09:38:43  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-3]
2025-07-29 09:38:43  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:38:43  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:38:43  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:38:43  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:38:43  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:38:43  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:38:43  EarlyBird.api.thesis.service 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_f737dceb9100 [waitress-3]
2025-07-29 09:38:43  service.py 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_f737dceb9100 [waitress-3]
2025-07-29 09:38:43  EarlyBird.api.thesis.service 788: INFO  找到支付记录 - 订单: thesis_download_f737dceb9100, 状态: 0 [waitress-3]
2025-07-29 09:38:43  service.py 788: INFO  找到支付记录 - 订单: thesis_download_f737dceb9100, 状态: 0 [waitress-3]
2025-07-29 09:38:46  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-2]
2025-07-29 09:38:46  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-2]
2025-07-29 09:38:46  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:38:46  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:38:46  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:38:46  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:38:46  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:38:46  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:38:46  EarlyBird.api.thesis.service 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_f737dceb9100 [waitress-2]
2025-07-29 09:38:46  service.py 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_f737dceb9100 [waitress-2]
2025-07-29 09:38:46  EarlyBird.api.thesis.service 788: INFO  找到支付记录 - 订单: thesis_download_f737dceb9100, 状态: 0 [waitress-2]
2025-07-29 09:38:46  service.py 788: INFO  找到支付记录 - 订单: thesis_download_f737dceb9100, 状态: 0 [waitress-2]
2025-07-29 09:38:49  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-0]
2025-07-29 09:38:49  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-0]
2025-07-29 09:38:49  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:38:49  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:38:49  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:38:49  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:38:49  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:38:49  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:38:49  EarlyBird.api.thesis.service 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_f737dceb9100 [waitress-0]
2025-07-29 09:38:49  service.py 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_f737dceb9100 [waitress-0]
2025-07-29 09:38:49  EarlyBird.api.thesis.service 788: INFO  找到支付记录 - 订单: thesis_download_f737dceb9100, 状态: 0 [waitress-0]
2025-07-29 09:38:49  service.py 788: INFO  找到支付记录 - 订单: thesis_download_f737dceb9100, 状态: 0 [waitress-0]
2025-07-29 09:38:52  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-1]
2025-07-29 09:38:52  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-1]
2025-07-29 09:38:52  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:38:52  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:38:52  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:38:52  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:38:52  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:38:52  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:38:52  EarlyBird.api.thesis.service 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_f737dceb9100 [waitress-1]
2025-07-29 09:38:52  service.py 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_f737dceb9100 [waitress-1]
2025-07-29 09:38:52  EarlyBird.api.thesis.service 788: INFO  找到支付记录 - 订单: thesis_download_f737dceb9100, 状态: 0 [waitress-1]
2025-07-29 09:38:52  service.py 788: INFO  找到支付记录 - 订单: thesis_download_f737dceb9100, 状态: 0 [waitress-1]
2025-07-29 09:38:55  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-3]
2025-07-29 09:38:55  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-3]
2025-07-29 09:38:55  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:38:55  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:38:55  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:38:55  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:38:55  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:38:55  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:38:55  EarlyBird.api.thesis.service 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_f737dceb9100 [waitress-3]
2025-07-29 09:38:55  service.py 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_f737dceb9100 [waitress-3]
2025-07-29 09:38:55  EarlyBird.api.thesis.service 788: INFO  找到支付记录 - 订单: thesis_download_f737dceb9100, 状态: 0 [waitress-3]
2025-07-29 09:38:55  service.py 788: INFO  找到支付记录 - 订单: thesis_download_f737dceb9100, 状态: 0 [waitress-3]
2025-07-29 09:38:58  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-2]
2025-07-29 09:38:58  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-2]
2025-07-29 09:38:58  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:38:58  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:38:58  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:38:58  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:38:58  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:38:58  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:38:58  EarlyBird.api.thesis.service 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_f737dceb9100 [waitress-2]
2025-07-29 09:38:58  service.py 746: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_f737dceb9100 [waitress-2]
2025-07-29 09:38:58  EarlyBird.api.thesis.service 788: INFO  找到支付记录 - 订单: thesis_download_f737dceb9100, 状态: 0 [waitress-2]
2025-07-29 09:38:58  service.py 788: INFO  找到支付记录 - 订单: thesis_download_f737dceb9100, 状态: 0 [waitress-2]
2025-07-29 09:39:19  init_flask.py 27: INFO  {'CpuCount': 6, 'Mem': '32 GB'} [MainThread]
2025-07-29 09:39:19  web_server.py 32: INFO  Static directory: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\resources\ui [MainThread]
2025-07-29 09:39:19  web_server.py 33: INFO  Static directory exists: True [MainThread]
2025-07-29 09:39:19  web_server.py 37: INFO  设置工作目录: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird [MainThread]
2025-07-29 09:39:19  EarlyBird.config.config 21: INFO  load configFile at d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\config/production.ini [MainThread]
2025-07-29 09:39:19  config.py 21: INFO  load configFile at d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\config/production.ini [MainThread]
2025-07-29 09:39:19  config.py 94: INFO  Database URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-29 09:39:21  EarlyBird.api.admin.app 39: INFO  ✅ 后台管理系统API已注册 [MainThread]
2025-07-29 09:39:21  app.py 39: INFO  ✅ 后台管理系统API已注册 [MainThread]
2025-07-29 09:39:21  EarlyBird 40: INFO  确保数据目录存在: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\resources\data [MainThread]
2025-07-29 09:39:21  server.py 40: INFO  确保数据目录存在: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\resources\data [MainThread]
2025-07-29 09:39:21  EarlyBird 44: INFO  开始执行数据库迁移... [MainThread]
2025-07-29 09:39:21  server.py 44: INFO  开始执行数据库迁移... [MainThread]
2025-07-29 09:39:21  config.py 21: INFO  load configFile at d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\config/production.ini [MainThread]
2025-07-29 09:39:21  config.py 94: INFO  Database URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-29 09:39:21  EarlyBird 53: INFO  数据库连接URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-29 09:39:21  server.py 53: INFO  数据库连接URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-29 09:39:21  EarlyBird 97: WARNING  ChatLog表不存在，将在应用启动时自动创建 [MainThread]
2025-07-29 09:39:21  server.py 97: WARNING  ChatLog表不存在，将在应用启动时自动创建 [MainThread]
2025-07-29 09:39:21  add_uid_to_chatlog.py 20: INFO  开始执行ChatLog表迁移... [MainThread]
2025-07-29 09:39:21  add_uid_to_chatlog.py 24: INFO  数据库连接URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-29 09:39:21  add_uid_to_chatlog.py 38: WARNING  ChatLog表不存在，可能需要先创建表 [MainThread]
2025-07-29 09:39:21  task_api_server.py 31: INFO  TaskApiServer start [MainThread]
2025-07-29 09:39:21  task_paper_generator.py 38: INFO  论文生成任务处理线程初始化 [MainThread]
2025-07-29 09:39:21  task_paper_generator.py 72: INFO  论文生成任务处理线程启动 [Thread-2]
2025-07-29 09:39:21  task_api_server.py 47: INFO  TaskApiServer listen on 127.0.0.1:3302 [Thread-1]
2025-07-29 09:39:21  task_api_server.py 17: INFO  _ApiServerThread start [Thread-3]
2025-07-29 09:39:26  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-0]
2025-07-29 09:39:26  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-0]
2025-07-29 09:39:26  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:39:26  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:39:26  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:39:26  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:39:26  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:39:26  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:39:26  EarlyBird.api.thesis.service 770: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_f737dceb9100 [waitress-0]
2025-07-29 09:39:26  service.py 770: INFO  查询支付状态 - 用户: 14, 论文: 32, 订单: thesis_download_f737dceb9100 [waitress-0]
2025-07-29 09:39:26  EarlyBird.api.thesis.service 812: INFO  找到支付记录 - 订单: thesis_download_f737dceb9100, 状态: 0 [waitress-0]
2025-07-29 09:39:26  service.py 812: INFO  找到支付记录 - 订单: thesis_download_f737dceb9100, 状态: 0 [waitress-0]
2025-07-29 09:39:29  web_server.py 73: INFO  Serving request for path: paper/content [waitress-1]
2025-07-29 09:39:29  web_server.py 85: INFO  Serving index.html for path: paper/content [waitress-1]
2025-07-29 09:39:30  web_server.py 73: INFO  Serving request for path: js/chunk-vendors.04aff60f.js [waitress-2]
2025-07-29 09:39:30  web_server.py 82: INFO  Serving static file: js/chunk-vendors.04aff60f.js [waitress-2]
2025-07-29 09:39:30  web_server.py 73: INFO  Serving request for path: css/chunk-vendors.d5efd04f.css [waitress-3]
2025-07-29 09:39:30  web_server.py 73: INFO  Serving request for path: css/app.1e68ddae.css [waitress-0]
2025-07-29 09:39:30  web_server.py 82: INFO  Serving static file: css/chunk-vendors.d5efd04f.css [waitress-3]
2025-07-29 09:39:30  web_server.py 73: INFO  Serving request for path: js/app.4ad3be7b.js [waitress-1]
2025-07-29 09:39:30  web_server.py 82: INFO  Serving static file: css/app.1e68ddae.css [waitress-0]
2025-07-29 09:39:30  web_server.py 82: INFO  Serving static file: js/app.4ad3be7b.js [waitress-1]
2025-07-29 09:39:30  web_server.py 73: INFO  Serving request for path: .well-known/appspecific/com.chrome.devtools.json [waitress-2]
2025-07-29 09:39:30  web_server.py 85: INFO  Serving index.html for path: .well-known/appspecific/com.chrome.devtools.json [waitress-2]
2025-07-29 09:39:30  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/user/info [waitress-3]
2025-07-29 09:39:30  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/user/info [waitress-3]
2025-07-29 09:39:30  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:39:30  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:39:30  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:39:30  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:39:30  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:39:30  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:39:30  web_server.py 73: INFO  Serving request for path: logo.png [waitress-0]
2025-07-29 09:39:30  web_server.py 82: INFO  Serving static file: logo.png [waitress-0]
2025-07-29 09:39:30  web_server.py 73: INFO  Serving request for path: js/22.683bc166.js [waitress-1]
2025-07-29 09:39:30  web_server.py 82: INFO  Serving static file: js/22.683bc166.js [waitress-1]
2025-07-29 09:39:30  web_server.py 73: INFO  Serving request for path: js/897.25207915.js [waitress-2]
2025-07-29 09:39:30  web_server.py 82: INFO  Serving static file: js/897.25207915.js [waitress-2]
2025-07-29 09:39:30  web_server.py 73: INFO  Serving request for path: css/389.ae47f7d6.css [waitress-3]
2025-07-29 09:39:30  web_server.py 73: INFO  Serving request for path: js/389.9dbcae13.js [waitress-0]
2025-07-29 09:39:30  web_server.py 82: INFO  Serving static file: css/389.ae47f7d6.css [waitress-3]
2025-07-29 09:39:30  web_server.py 82: INFO  Serving static file: js/389.9dbcae13.js [waitress-0]
2025-07-29 09:39:30  web_server.py 73: INFO  Serving request for path: img/logo.ce3a5bfd.png [waitress-1]
2025-07-29 09:39:30  web_server.py 82: INFO  Serving static file: img/logo.ce3a5bfd.png [waitress-1]
2025-07-29 09:39:30  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getList [waitress-3]
2025-07-29 09:39:30  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getList [waitress-3]
2025-07-29 09:39:30  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:39:30  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:39:30  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:39:30  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:39:30  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:39:30  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:39:30  service.py 70: INFO  获取设置成功，包含 8 个配置项 [waitress-2]
2025-07-29 09:39:30  EarlyBird.api.thesis.apis 92: INFO  用户 14 请求获取论文列表 [waitress-3]
2025-07-29 09:39:30  apis.py 92: INFO  用户 14 请求获取论文列表 [waitress-3]
2025-07-29 09:39:30  EarlyBird.api.thesis.service 619: INFO  查询论文列表，用户ID: 14 [waitress-3]
2025-07-29 09:39:30  service.py 619: INFO  查询论文列表，用户ID: 14 [waitress-3]
2025-07-29 09:39:30  EarlyBird.api.thesis.service 625: INFO  查询到 1 篇论文 [waitress-3]
2025-07-29 09:39:30  service.py 625: INFO  查询到 1 篇论文 [waitress-3]
2025-07-29 09:39:30  EarlyBird.api.thesis.service 635: INFO  论文ID: 32, 标题: CES技术在提高电源效率中的应用研究, 用户ID: 14 [waitress-3]
2025-07-29 09:39:30  service.py 635: INFO  论文ID: 32, 标题: CES技术在提高电源效率中的应用研究, 用户ID: 14 [waitress-3]
2025-07-29 09:39:30  EarlyBird.api.thesis.service 637: INFO  返回论文列表，共 1 篇 [waitress-3]
2025-07-29 09:39:30  service.py 637: INFO  返回论文列表，共 1 篇 [waitress-3]
2025-07-29 09:39:30  EarlyBird.api.thesis.apis 95: INFO  用户 14 获取论文列表成功，共 1 篇 [waitress-3]
2025-07-29 09:39:30  apis.py 95: INFO  用户 14 获取论文列表成功，共 1 篇 [waitress-3]
2025-07-29 09:39:30  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getDetail [waitress-0]
2025-07-29 09:39:30  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getDetail [waitress-0]
2025-07-29 09:39:30  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:39:30  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:39:30  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:39:30  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:39:30  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:39:30  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:39:30  EarlyBird.api.talk2ai.utils 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-0]
2025-07-29 09:39:30  utils.py 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-0]
2025-07-29 09:39:30  EarlyBird.api.talk2ai.utils 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-0]
2025-07-29 09:39:30  utils.py 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-0]
2025-07-29 09:39:30  EarlyBird.api.talk2ai.utils 161: INFO  提纲处理完成，根节点ID: E2em6gS9wrhodhQoZ8tQHc [waitress-0]
2025-07-29 09:39:30  utils.py 161: INFO  提纲处理完成，根节点ID: E2em6gS9wrhodhQoZ8tQHc [waitress-0]
2025-07-29 09:39:30  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getOutline [waitress-1]
2025-07-29 09:39:30  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getOutline [waitress-1]
2025-07-29 09:39:30  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:39:30  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:39:30  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:39:30  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:39:30  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:39:30  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:39:30  EarlyBird.api.talk2ai.utils 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-1]
2025-07-29 09:39:30  utils.py 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-1]
2025-07-29 09:39:30  EarlyBird.api.talk2ai.utils 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-1]
2025-07-29 09:39:30  utils.py 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-1]
2025-07-29 09:39:30  EarlyBird.api.talk2ai.utils 161: INFO  提纲处理完成，根节点ID: E2em6gS9wrhodhQoZ8tQHc [waitress-1]
2025-07-29 09:39:30  utils.py 161: INFO  提纲处理完成，根节点ID: E2em6gS9wrhodhQoZ8tQHc [waitress-1]
2025-07-29 09:39:31  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/downloadThesis [waitress-2]
2025-07-29 09:39:31  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/downloadThesis [waitress-2]
2025-07-29 09:39:31  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:39:31  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:39:31  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:39:31  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:39:31  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:39:31  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:39:31  EarlyBird.api.thesis.apis 145: INFO  用户 14 请求下载论文 32 [waitress-2]
2025-07-29 09:39:31  apis.py 145: INFO  用户 14 请求下载论文 32 [waitress-2]
2025-07-29 09:39:31  EarlyBird.api.thesis.service 296: INFO  用户 14 请求导出论文 32 [waitress-2]
2025-07-29 09:39:31  service.py 296: INFO  用户 14 请求导出论文 32 [waitress-2]
2025-07-29 09:39:31  EarlyBird.api.thesis.service 306: INFO  用户 14 VIP状态: <bound method User.isVip of {"accessToken": null, "phone": null, "vip_expire_at": null, "refreshToken": null, "gender": 3, "balance": null, "update_time": "2025-07-29 09:28:33", "nickname": "\u7528\u6237_test1011", "email": null, "id": 14, "headimg": null, "is_lock": false, "invite_code": null, "is_deleted": 0, "activation_key": null, "last_login_time": "2025-07-29 09:28:32", "from_user_id": null, "status": 1, "username": "test1011", "last_login_ip": "127.0.0.1", "openPlatform": "wx", "password_hash": "pbkdf2:sha256:260000$3VYSebusaQuZ1fdD$9217323331b6562a816f82255c4826fd0d815e00bee146926900d409eee250fd", "vip_level": 1, "create_time": "2025-07-28 16:58:53", "openId": null, "realname": null, "vip_start_at": null}> [waitress-2]
2025-07-29 09:39:31  service.py 306: INFO  用户 14 VIP状态: <bound method User.isVip of {"accessToken": null, "phone": null, "vip_expire_at": null, "refreshToken": null, "gender": 3, "balance": null, "update_time": "2025-07-29 09:28:33", "nickname": "\u7528\u6237_test1011", "email": null, "id": 14, "headimg": null, "is_lock": false, "invite_code": null, "is_deleted": 0, "activation_key": null, "last_login_time": "2025-07-29 09:28:32", "from_user_id": null, "status": 1, "username": "test1011", "last_login_ip": "127.0.0.1", "openPlatform": "wx", "password_hash": "pbkdf2:sha256:260000$3VYSebusaQuZ1fdD$9217323331b6562a816f82255c4826fd0d815e00bee146926900d409eee250fd", "vip_level": 1, "create_time": "2025-07-28 16:58:53", "openId": null, "realname": null, "vip_start_at": null}> [waitress-2]
2025-07-29 09:39:31  EarlyBird.api.thesis.service 311: INFO  论文下载收费功能是否启用: True [waitress-2]
2025-07-29 09:39:31  service.py 311: INFO  论文下载收费功能是否启用: True [waitress-2]
2025-07-29 09:39:31  EarlyBird.api.thesis.service 327: INFO  VIP用户是否免费下载: False [waitress-2]
2025-07-29 09:39:31  service.py 327: INFO  VIP用户是否免费下载: False [waitress-2]
2025-07-29 09:39:31  EarlyBird.api.thesis.service 347: WARNING  配置显示首次下载不免费，但强制启用首次下载免费功能 [waitress-2]
2025-07-29 09:39:31  service.py 347: WARNING  配置显示首次下载不免费，但强制启用首次下载免费功能 [waitress-2]
2025-07-29 09:39:31  EarlyBird.api.thesis.service 350: INFO  首次下载是否免费: True [waitress-2]
2025-07-29 09:39:31  service.py 350: INFO  首次下载是否免费: True [waitress-2]
2025-07-29 09:39:31  EarlyBird.api.thesis.service 355: INFO  用户 14 是否有任何下载记录: True [waitress-2]
2025-07-29 09:39:31  service.py 355: INFO  用户 14 是否有任何下载记录: True [waitress-2]
2025-07-29 09:39:31  EarlyBird.api.thesis.service 360: INFO  用户 14 共有 1 条下载记录 [waitress-2]
2025-07-29 09:39:31  service.py 360: INFO  用户 14 共有 1 条下载记录 [waitress-2]
2025-07-29 09:39:31  EarlyBird.api.thesis.service 372: INFO  用户 14 的下载记录都是今天的测试记录，视为首次下载 [waitress-2]
2025-07-29 09:39:31  service.py 372: INFO  用户 14 的下载记录都是今天的测试记录，视为首次下载 [waitress-2]
2025-07-29 09:39:31  EarlyBird.api.thesis.service 377: INFO  用户 14 首次下载论文 32，免费 [waitress-2]
2025-07-29 09:39:31  service.py 377: INFO  用户 14 首次下载论文 32，免费 [waitress-2]
2025-07-29 09:39:31  EarlyBird.api.thesis.service 922: INFO  add Para:CES技术在提高电源效率中的应用研究 [waitress-2]
2025-07-29 09:39:31  service.py 922: INFO  add Para:CES技术在提高电源效率中的应用研究 [waitress-2]
2025-07-29 09:39:31  EarlyBird.api.thesis.service 922: INFO  add Para:绪论 [waitress-2]
2025-07-29 09:39:31  service.py 922: INFO  add Para:绪论 [waitress-2]
2025-07-29 09:39:31  EarlyBird.api.thesis.service 922: INFO  add Para:研究背景与技术发展现状 [waitress-2]
2025-07-29 09:39:31  service.py 922: INFO  add Para:研究背景与技术发展现状 [waitress-2]
2025-07-29 09:39:31  EarlyBird.api.thesis.service 922: INFO  add Para:CES技术在电源效率提升中的数据现状分析 [waitress-2]
2025-07-29 09:39:31  service.py 922: INFO  add Para:CES技术在电源效率提升中的数据现状分析 [waitress-2]
2025-07-29 09:39:31  EarlyBird.api.thesis.service 922: INFO  add Para:本研究的意义及目标设定 [waitress-2]
2025-07-29 09:39:31  service.py 922: INFO  add Para:本研究的意义及目标设定 [waitress-2]
2025-07-29 09:39:31  EarlyBird.api.thesis.service 922: INFO  add Para:理论基础与模型构建 [waitress-2]
2025-07-29 09:39:31  service.py 922: INFO  add Para:理论基础与模型构建 [waitress-2]
2025-07-29 09:39:31  EarlyBird.api.thesis.service 922: INFO  add Para:CES技术的基本原理及其对电源效率影响的理论探讨 [waitress-2]
2025-07-29 09:39:31  service.py 922: INFO  add Para:CES技术的基本原理及其对电源效率影响的理论探讨 [waitress-2]
2025-07-29 09:39:31  EarlyBird.api.thesis.service 922: INFO  add Para:基于现有文献的数据模型建立 [waitress-2]
2025-07-29 09:39:31  service.py 922: INFO  add Para:基于现有文献的数据模型建立 [waitress-2]
2025-07-29 09:39:31  EarlyBird.api.thesis.service 922: INFO  add Para:采用的具体分析方法介绍 [waitress-2]
2025-07-29 09:39:31  service.py 922: INFO  add Para:采用的具体分析方法介绍 [waitress-2]
2025-07-29 09:39:31  EarlyBird.api.thesis.service 922: INFO  add Para:实证研究设计与数据分析 [waitress-2]
2025-07-29 09:39:31  service.py 922: INFO  add Para:实证研究设计与数据分析 [waitress-2]
2025-07-29 09:39:31  EarlyBird.api.thesis.service 922: INFO  add Para:实验数据收集过程描述 [waitress-2]
2025-07-29 09:39:31  service.py 922: INFO  add Para:实验数据收集过程描述 [waitress-2]
2025-07-29 09:39:31  EarlyBird.api.thesis.service 922: INFO  add Para:运用统计软件进行的数据处理与初步分析 [waitress-2]
2025-07-29 09:39:31  service.py 922: INFO  add Para:运用统计软件进行的数据处理与初步分析 [waitress-2]
2025-07-29 09:39:31  EarlyBird.api.thesis.service 922: INFO  add Para:关键指标的变化趋势及原因解析 [waitress-2]
2025-07-29 09:39:31  service.py 922: INFO  add Para:关键指标的变化趋势及原因解析 [waitress-2]
2025-07-29 09:39:31  EarlyBird.api.thesis.service 922: INFO  add Para:案例分析：CES技术应用实例 [waitress-2]
2025-07-29 09:39:31  service.py 922: INFO  add Para:案例分析：CES技术应用实例 [waitress-2]
2025-07-29 09:39:31  EarlyBird.api.thesis.service 922: INFO  add Para:选取典型应用场景介绍 [waitress-2]
2025-07-29 09:39:31  service.py 922: INFO  add Para:选取典型应用场景介绍 [waitress-2]
2025-07-29 09:39:31  EarlyBird.api.thesis.service 922: INFO  add Para:实施前后性能参数对比 [waitress-2]
2025-07-29 09:39:31  service.py 922: INFO  add Para:实施前后性能参数对比 [waitress-2]
2025-07-29 09:39:31  EarlyBird.api.thesis.service 922: INFO  add Para:效益评估与问题点识别 [waitress-2]
2025-07-29 09:39:31  service.py 922: INFO  add Para:效益评估与问题点识别 [waitress-2]
2025-07-29 09:39:31  EarlyBird.api.thesis.service 922: INFO  add Para:结果讨论与深度分析 [waitress-2]
2025-07-29 09:39:31  service.py 922: INFO  add Para:结果讨论与深度分析 [waitress-2]
2025-07-29 09:39:31  EarlyBird.api.thesis.service 922: INFO  add Para:综合所有数据后的整体表现总结 [waitress-2]
2025-07-29 09:39:31  service.py 922: INFO  add Para:综合所有数据后的整体表现总结 [waitress-2]
2025-07-29 09:39:31  EarlyBird.api.thesis.service 922: INFO  add Para:关于CES技术有效性的新见解 [waitress-2]
2025-07-29 09:39:31  service.py 922: INFO  add Para:关于CES技术有效性的新见解 [waitress-2]
2025-07-29 09:39:31  EarlyBird.api.thesis.service 922: INFO  add Para:针对发现的问题提出改进建议 [waitress-2]
2025-07-29 09:39:31  service.py 922: INFO  add Para:针对发现的问题提出改进建议 [waitress-2]
2025-07-29 09:39:31  EarlyBird.api.thesis.service 922: INFO  add Para:结论 [waitress-2]
2025-07-29 09:39:31  service.py 922: INFO  add Para:结论 [waitress-2]
2025-07-29 09:39:31  EarlyBird.api.thesis.service 922: INFO  add Para:研究成果的数据化总结 [waitress-2]
2025-07-29 09:39:31  service.py 922: INFO  add Para:研究成果的数据化总结 [waitress-2]
2025-07-29 09:39:31  EarlyBird.api.thesis.service 922: INFO  add Para:对未来研究方向的展望 [waitress-2]
2025-07-29 09:39:31  service.py 922: INFO  add Para:对未来研究方向的展望 [waitress-2]
2025-07-29 09:39:31  EarlyBird.api.thesis.service 922: INFO  add Para:对于行业发展的政策建议 [waitress-2]
2025-07-29 09:39:31  service.py 922: INFO  add Para:对于行业发展的政策建议 [waitress-2]
2025-07-29 09:39:31  EarlyBird.common.docx 500: INFO  d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\resources\download\[早鸟论文]_CES技术在提高电源效率中的应用研究_V20250729_093931.docx [waitress-2]
2025-07-29 09:39:31  docx.py 500: INFO  d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\resources\download\[早鸟论文]_CES技术在提高电源效率中的应用研究_V20250729_093931.docx [waitress-2]
2025-07-29 09:39:31  EarlyBird.api.thesis.apis 160: INFO  用户 14 下载论文 32 成功 [waitress-2]
2025-07-29 09:39:31  apis.py 160: INFO  用户 14 下载论文 32 成功 [waitress-2]
2025-07-29 09:39:31  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/download [waitress-3]
2025-07-29 09:39:31  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/download [waitress-3]
2025-07-29 09:39:31  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:39:31  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:39:31  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:39:31  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:39:31  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:39:31  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:44:38  init_flask.py 27: INFO  {'CpuCount': 6, 'Mem': '32 GB'} [MainThread]
2025-07-29 09:44:38  web_server.py 32: INFO  Static directory: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\resources\ui [MainThread]
2025-07-29 09:44:38  web_server.py 33: INFO  Static directory exists: True [MainThread]
2025-07-29 09:44:38  web_server.py 37: INFO  设置工作目录: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird [MainThread]
2025-07-29 09:44:38  EarlyBird.config.config 21: INFO  load configFile at d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\config/production.ini [MainThread]
2025-07-29 09:44:38  config.py 21: INFO  load configFile at d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\config/production.ini [MainThread]
2025-07-29 09:44:38  config.py 94: INFO  Database URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-29 09:44:40  EarlyBird.api.admin.app 39: INFO  ✅ 后台管理系统API已注册 [MainThread]
2025-07-29 09:44:40  app.py 39: INFO  ✅ 后台管理系统API已注册 [MainThread]
2025-07-29 09:44:40  EarlyBird 40: INFO  确保数据目录存在: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\resources\data [MainThread]
2025-07-29 09:44:40  server.py 40: INFO  确保数据目录存在: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\resources\data [MainThread]
2025-07-29 09:44:40  EarlyBird 44: INFO  开始执行数据库迁移... [MainThread]
2025-07-29 09:44:40  server.py 44: INFO  开始执行数据库迁移... [MainThread]
2025-07-29 09:44:40  config.py 21: INFO  load configFile at d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\config/production.ini [MainThread]
2025-07-29 09:44:40  config.py 94: INFO  Database URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-29 09:44:40  EarlyBird 53: INFO  数据库连接URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-29 09:44:40  server.py 53: INFO  数据库连接URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-29 09:44:40  EarlyBird 97: WARNING  ChatLog表不存在，将在应用启动时自动创建 [MainThread]
2025-07-29 09:44:40  server.py 97: WARNING  ChatLog表不存在，将在应用启动时自动创建 [MainThread]
2025-07-29 09:44:40  add_uid_to_chatlog.py 20: INFO  开始执行ChatLog表迁移... [MainThread]
2025-07-29 09:44:40  add_uid_to_chatlog.py 24: INFO  数据库连接URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-29 09:44:40  add_uid_to_chatlog.py 38: WARNING  ChatLog表不存在，可能需要先创建表 [MainThread]
2025-07-29 09:44:40  task_api_server.py 31: INFO  TaskApiServer start [MainThread]
2025-07-29 09:44:40  task_paper_generator.py 38: INFO  论文生成任务处理线程初始化 [MainThread]
2025-07-29 09:44:40  task_paper_generator.py 72: INFO  论文生成任务处理线程启动 [Thread-2]
2025-07-29 09:44:40  task_api_server.py 47: INFO  TaskApiServer listen on 127.0.0.1:3302 [Thread-1]
2025-07-29 09:44:40  task_api_server.py 17: INFO  _ApiServerThread start [Thread-3]
2025-07-29 09:44:45  web_server.py 73: INFO  Serving request for path: paper/content [waitress-0]
2025-07-29 09:44:45  web_server.py 85: INFO  Serving index.html for path: paper/content [waitress-0]
2025-07-29 09:44:45  web_server.py 73: INFO  Serving request for path: .well-known/appspecific/com.chrome.devtools.json [waitress-1]
2025-07-29 09:44:45  web_server.py 85: INFO  Serving index.html for path: .well-known/appspecific/com.chrome.devtools.json [waitress-1]
2025-07-29 09:44:45  web_server.py 73: INFO  Serving request for path: js/chunk-vendors.04aff60f.js [waitress-1]
2025-07-29 09:44:45  web_server.py 73: INFO  Serving request for path: css/chunk-vendors.d5efd04f.css [waitress-3]
2025-07-29 09:44:45  web_server.py 73: INFO  Serving request for path: css/app.1e68ddae.css [waitress-0]
2025-07-29 09:44:45  web_server.py 73: INFO  Serving request for path: js/app.4ad3be7b.js [waitress-2]
2025-07-29 09:44:45  web_server.py 82: INFO  Serving static file: js/chunk-vendors.04aff60f.js [waitress-1]
2025-07-29 09:44:45  web_server.py 82: INFO  Serving static file: css/chunk-vendors.d5efd04f.css [waitress-3]
2025-07-29 09:44:45  web_server.py 82: INFO  Serving static file: css/app.1e68ddae.css [waitress-0]
2025-07-29 09:44:45  web_server.py 82: INFO  Serving static file: js/app.4ad3be7b.js [waitress-2]
2025-07-29 09:44:45  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/user/info [waitress-1]
2025-07-29 09:44:45  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/user/info [waitress-1]
2025-07-29 09:44:45  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:44:45  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:44:45  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:44:45  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:44:45  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:44:45  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:44:45  web_server.py 73: INFO  Serving request for path: logo.png [waitress-2]
2025-07-29 09:44:45  web_server.py 82: INFO  Serving static file: logo.png [waitress-2]
2025-07-29 09:44:46  web_server.py 73: INFO  Serving request for path: js/22.683bc166.js [waitress-3]
2025-07-29 09:44:46  web_server.py 82: INFO  Serving static file: js/22.683bc166.js [waitress-3]
2025-07-29 09:44:46  web_server.py 73: INFO  Serving request for path: js/897.25207915.js [waitress-0]
2025-07-29 09:44:46  web_server.py 82: INFO  Serving static file: js/897.25207915.js [waitress-0]
2025-07-29 09:44:46  web_server.py 73: INFO  Serving request for path: js/389.9dbcae13.js [waitress-1]
2025-07-29 09:44:46  web_server.py 73: INFO  Serving request for path: css/389.ae47f7d6.css [waitress-2]
2025-07-29 09:44:46  web_server.py 82: INFO  Serving static file: js/389.9dbcae13.js [waitress-1]
2025-07-29 09:44:46  web_server.py 82: INFO  Serving static file: css/389.ae47f7d6.css [waitress-2]
2025-07-29 09:44:46  web_server.py 73: INFO  Serving request for path: img/logo.ce3a5bfd.png [waitress-3]
2025-07-29 09:44:46  web_server.py 82: INFO  Serving static file: img/logo.ce3a5bfd.png [waitress-3]
2025-07-29 09:44:46  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getList [waitress-1]
2025-07-29 09:44:46  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getList [waitress-1]
2025-07-29 09:44:46  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:44:46  web_server.py 73: INFO  Serving request for path: fonts/element-icons.ff18efd1.woff [waitress-2]
2025-07-29 09:44:46  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:44:46  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:44:46  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:44:46  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:44:46  web_server.py 82: INFO  Serving static file: fonts/element-icons.ff18efd1.woff [waitress-2]
2025-07-29 09:44:46  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:44:46  service.py 70: INFO  获取设置成功，包含 8 个配置项 [waitress-0]
2025-07-29 09:44:46  EarlyBird.api.thesis.apis 92: INFO  用户 14 请求获取论文列表 [waitress-1]
2025-07-29 09:44:46  apis.py 92: INFO  用户 14 请求获取论文列表 [waitress-1]
2025-07-29 09:44:46  EarlyBird.api.thesis.service 640: INFO  查询论文列表，用户ID: 14 [waitress-1]
2025-07-29 09:44:46  service.py 640: INFO  查询论文列表，用户ID: 14 [waitress-1]
2025-07-29 09:44:46  EarlyBird.api.thesis.service 646: INFO  查询到 1 篇论文 [waitress-1]
2025-07-29 09:44:46  service.py 646: INFO  查询到 1 篇论文 [waitress-1]
2025-07-29 09:44:46  EarlyBird.api.thesis.service 656: INFO  论文ID: 32, 标题: CES技术在提高电源效率中的应用研究, 用户ID: 14 [waitress-1]
2025-07-29 09:44:46  service.py 656: INFO  论文ID: 32, 标题: CES技术在提高电源效率中的应用研究, 用户ID: 14 [waitress-1]
2025-07-29 09:44:46  EarlyBird.api.thesis.service 658: INFO  返回论文列表，共 1 篇 [waitress-1]
2025-07-29 09:44:46  service.py 658: INFO  返回论文列表，共 1 篇 [waitress-1]
2025-07-29 09:44:46  EarlyBird.api.thesis.apis 95: INFO  用户 14 获取论文列表成功，共 1 篇 [waitress-1]
2025-07-29 09:44:46  apis.py 95: INFO  用户 14 获取论文列表成功，共 1 篇 [waitress-1]
2025-07-29 09:44:46  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getDetail [waitress-3]
2025-07-29 09:44:46  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getDetail [waitress-3]
2025-07-29 09:44:46  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:44:46  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:44:46  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:44:46  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:44:46  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:44:46  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:44:46  EarlyBird.api.talk2ai.utils 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-3]
2025-07-29 09:44:46  utils.py 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-3]
2025-07-29 09:44:46  EarlyBird.api.talk2ai.utils 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-3]
2025-07-29 09:44:46  utils.py 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-3]
2025-07-29 09:44:46  EarlyBird.api.talk2ai.utils 161: INFO  提纲处理完成，根节点ID: E2em6gS9wrhodhQoZ8tQHc [waitress-3]
2025-07-29 09:44:46  utils.py 161: INFO  提纲处理完成，根节点ID: E2em6gS9wrhodhQoZ8tQHc [waitress-3]
2025-07-29 09:44:46  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getOutline [waitress-2]
2025-07-29 09:44:46  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getOutline [waitress-2]
2025-07-29 09:44:46  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:44:46  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:44:46  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:44:46  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:44:46  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:44:46  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:44:46  EarlyBird.api.talk2ai.utils 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-2]
2025-07-29 09:44:46  utils.py 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-2]
2025-07-29 09:44:46  EarlyBird.api.talk2ai.utils 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-2]
2025-07-29 09:44:46  utils.py 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-2]
2025-07-29 09:44:46  EarlyBird.api.talk2ai.utils 161: INFO  提纲处理完成，根节点ID: E2em6gS9wrhodhQoZ8tQHc [waitress-2]
2025-07-29 09:44:46  utils.py 161: INFO  提纲处理完成，根节点ID: E2em6gS9wrhodhQoZ8tQHc [waitress-2]
2025-07-29 09:44:47  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/downloadThesis [waitress-0]
2025-07-29 09:44:47  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/downloadThesis [waitress-0]
2025-07-29 09:44:47  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:44:47  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:44:47  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:44:47  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:44:47  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:44:47  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:44:47  EarlyBird.api.thesis.apis 145: INFO  用户 14 请求下载论文 32 [waitress-0]
2025-07-29 09:44:47  apis.py 145: INFO  用户 14 请求下载论文 32 [waitress-0]
2025-07-29 09:44:47  EarlyBird.api.thesis.service 296: INFO  用户 14 请求导出论文 32 [waitress-0]
2025-07-29 09:44:47  service.py 296: INFO  用户 14 请求导出论文 32 [waitress-0]
2025-07-29 09:44:47  EarlyBird.api.thesis.service 306: INFO  用户 14 VIP状态: <bound method User.isVip of {"accessToken": null, "phone": null, "vip_expire_at": null, "refreshToken": null, "gender": 3, "balance": null, "update_time": "2025-07-29 09:28:33", "nickname": "\u7528\u6237_test1011", "email": null, "id": 14, "headimg": null, "is_lock": false, "invite_code": null, "is_deleted": 0, "activation_key": null, "last_login_time": "2025-07-29 09:28:32", "from_user_id": null, "status": 1, "username": "test1011", "last_login_ip": "127.0.0.1", "openPlatform": "wx", "password_hash": "pbkdf2:sha256:260000$3VYSebusaQuZ1fdD$9217323331b6562a816f82255c4826fd0d815e00bee146926900d409eee250fd", "vip_level": 1, "create_time": "2025-07-28 16:58:53", "openId": null, "realname": null, "vip_start_at": null}> [waitress-0]
2025-07-29 09:44:47  service.py 306: INFO  用户 14 VIP状态: <bound method User.isVip of {"accessToken": null, "phone": null, "vip_expire_at": null, "refreshToken": null, "gender": 3, "balance": null, "update_time": "2025-07-29 09:28:33", "nickname": "\u7528\u6237_test1011", "email": null, "id": 14, "headimg": null, "is_lock": false, "invite_code": null, "is_deleted": 0, "activation_key": null, "last_login_time": "2025-07-29 09:28:32", "from_user_id": null, "status": 1, "username": "test1011", "last_login_ip": "127.0.0.1", "openPlatform": "wx", "password_hash": "pbkdf2:sha256:260000$3VYSebusaQuZ1fdD$9217323331b6562a816f82255c4826fd0d815e00bee146926900d409eee250fd", "vip_level": 1, "create_time": "2025-07-28 16:58:53", "openId": null, "realname": null, "vip_start_at": null}> [waitress-0]
2025-07-29 09:44:47  EarlyBird.api.thesis.service 317: INFO  论文下载收费功能是否启用: True [waitress-0]
2025-07-29 09:44:47  service.py 317: INFO  论文下载收费功能是否启用: True [waitress-0]
2025-07-29 09:44:47  EarlyBird.api.thesis.service 339: INFO  VIP用户是否免费下载: False [waitress-0]
2025-07-29 09:44:47  service.py 339: INFO  VIP用户是否免费下载: False [waitress-0]
2025-07-29 09:44:47  EarlyBird.api.thesis.service 357: INFO  首次下载免费配置查询结果: {"id": 9, "name": "\u9996\u6b21\u4e0b\u8f7d\u662f\u5426\u514d\u8d39", "config_key": "thesis.download.first_free", "config_value": "false", "description": "\u7528\u6237\u9996\u6b21\u4e0b\u8f7d\u8bba\u6587\u662f\u5426\u514d\u8d39", "create_time": "2025-06-28 15:32:25"} [waitress-0]
2025-07-29 09:44:47  service.py 357: INFO  首次下载免费配置查询结果: {"id": 9, "name": "\u9996\u6b21\u4e0b\u8f7d\u662f\u5426\u514d\u8d39", "config_key": "thesis.download.first_free", "config_value": "false", "description": "\u7528\u6237\u9996\u6b21\u4e0b\u8f7d\u8bba\u6587\u662f\u5426\u514d\u8d39", "create_time": "2025-06-28 15:32:25"} [waitress-0]
2025-07-29 09:44:47  EarlyBird.api.thesis.service 359: INFO  配置值: 'false' (类型: <class 'str'>) [waitress-0]
2025-07-29 09:44:47  service.py 359: INFO  配置值: 'false' (类型: <class 'str'>) [waitress-0]
2025-07-29 09:44:47  EarlyBird.api.thesis.service 360: INFO  is_deleted: 0 [waitress-0]
2025-07-29 09:44:47  service.py 360: INFO  is_deleted: 0 [waitress-0]
2025-07-29 09:44:47  EarlyBird.api.thesis.service 367: INFO  配置值处理: 'false' -> 'false' -> False [waitress-0]
2025-07-29 09:44:47  service.py 367: INFO  配置值处理: 'false' -> 'false' -> False [waitress-0]
2025-07-29 09:44:47  EarlyBird.api.thesis.service 371: INFO  首次下载是否免费: False [waitress-0]
2025-07-29 09:44:47  service.py 371: INFO  首次下载是否免费: False [waitress-0]
2025-07-29 09:44:47  EarlyBird.api.thesis.service 376: INFO  用户 14 是否有任何下载记录: True [waitress-0]
2025-07-29 09:44:47  service.py 376: INFO  用户 14 是否有任何下载记录: True [waitress-0]
2025-07-29 09:44:47  EarlyBird.api.thesis.service 381: INFO  用户 14 共有 1 条下载记录 [waitress-0]
2025-07-29 09:44:47  service.py 381: INFO  用户 14 共有 1 条下载记录 [waitress-0]
2025-07-29 09:44:47  EarlyBird.api.thesis.service 393: INFO  用户 14 的下载记录都是今天的测试记录，视为首次下载 [waitress-0]
2025-07-29 09:44:47  service.py 393: INFO  用户 14 的下载记录都是今天的测试记录，视为首次下载 [waitress-0]
2025-07-29 09:44:47  EarlyBird.api.thesis.service 438: INFO  用户 14 有免费下载记录，可直接下载论文 32 [waitress-0]
2025-07-29 09:44:47  service.py 438: INFO  用户 14 有免费下载记录，可直接下载论文 32 [waitress-0]
2025-07-29 09:44:47  EarlyBird.api.thesis.service 943: INFO  add Para:CES技术在提高电源效率中的应用研究 [waitress-0]
2025-07-29 09:44:47  service.py 943: INFO  add Para:CES技术在提高电源效率中的应用研究 [waitress-0]
2025-07-29 09:44:47  EarlyBird.api.thesis.service 943: INFO  add Para:绪论 [waitress-0]
2025-07-29 09:44:47  service.py 943: INFO  add Para:绪论 [waitress-0]
2025-07-29 09:44:47  EarlyBird.api.thesis.service 943: INFO  add Para:研究背景与技术发展现状 [waitress-0]
2025-07-29 09:44:47  service.py 943: INFO  add Para:研究背景与技术发展现状 [waitress-0]
2025-07-29 09:44:47  EarlyBird.api.thesis.service 943: INFO  add Para:CES技术在电源效率提升中的数据现状分析 [waitress-0]
2025-07-29 09:44:47  service.py 943: INFO  add Para:CES技术在电源效率提升中的数据现状分析 [waitress-0]
2025-07-29 09:44:47  EarlyBird.api.thesis.service 943: INFO  add Para:本研究的意义及目标设定 [waitress-0]
2025-07-29 09:44:47  service.py 943: INFO  add Para:本研究的意义及目标设定 [waitress-0]
2025-07-29 09:44:47  EarlyBird.api.thesis.service 943: INFO  add Para:理论基础与模型构建 [waitress-0]
2025-07-29 09:44:47  service.py 943: INFO  add Para:理论基础与模型构建 [waitress-0]
2025-07-29 09:44:47  EarlyBird.api.thesis.service 943: INFO  add Para:CES技术的基本原理及其对电源效率影响的理论探讨 [waitress-0]
2025-07-29 09:44:47  service.py 943: INFO  add Para:CES技术的基本原理及其对电源效率影响的理论探讨 [waitress-0]
2025-07-29 09:44:47  EarlyBird.api.thesis.service 943: INFO  add Para:基于现有文献的数据模型建立 [waitress-0]
2025-07-29 09:44:47  service.py 943: INFO  add Para:基于现有文献的数据模型建立 [waitress-0]
2025-07-29 09:44:47  EarlyBird.api.thesis.service 943: INFO  add Para:采用的具体分析方法介绍 [waitress-0]
2025-07-29 09:44:47  service.py 943: INFO  add Para:采用的具体分析方法介绍 [waitress-0]
2025-07-29 09:44:47  EarlyBird.api.thesis.service 943: INFO  add Para:实证研究设计与数据分析 [waitress-0]
2025-07-29 09:44:47  service.py 943: INFO  add Para:实证研究设计与数据分析 [waitress-0]
2025-07-29 09:44:47  EarlyBird.api.thesis.service 943: INFO  add Para:实验数据收集过程描述 [waitress-0]
2025-07-29 09:44:47  service.py 943: INFO  add Para:实验数据收集过程描述 [waitress-0]
2025-07-29 09:44:47  EarlyBird.api.thesis.service 943: INFO  add Para:运用统计软件进行的数据处理与初步分析 [waitress-0]
2025-07-29 09:44:47  service.py 943: INFO  add Para:运用统计软件进行的数据处理与初步分析 [waitress-0]
2025-07-29 09:44:47  EarlyBird.api.thesis.service 943: INFO  add Para:关键指标的变化趋势及原因解析 [waitress-0]
2025-07-29 09:44:47  service.py 943: INFO  add Para:关键指标的变化趋势及原因解析 [waitress-0]
2025-07-29 09:44:47  EarlyBird.api.thesis.service 943: INFO  add Para:案例分析：CES技术应用实例 [waitress-0]
2025-07-29 09:44:47  service.py 943: INFO  add Para:案例分析：CES技术应用实例 [waitress-0]
2025-07-29 09:44:47  EarlyBird.api.thesis.service 943: INFO  add Para:选取典型应用场景介绍 [waitress-0]
2025-07-29 09:44:47  service.py 943: INFO  add Para:选取典型应用场景介绍 [waitress-0]
2025-07-29 09:44:47  EarlyBird.api.thesis.service 943: INFO  add Para:实施前后性能参数对比 [waitress-0]
2025-07-29 09:44:47  service.py 943: INFO  add Para:实施前后性能参数对比 [waitress-0]
2025-07-29 09:44:47  EarlyBird.api.thesis.service 943: INFO  add Para:效益评估与问题点识别 [waitress-0]
2025-07-29 09:44:47  service.py 943: INFO  add Para:效益评估与问题点识别 [waitress-0]
2025-07-29 09:44:47  EarlyBird.api.thesis.service 943: INFO  add Para:结果讨论与深度分析 [waitress-0]
2025-07-29 09:44:47  service.py 943: INFO  add Para:结果讨论与深度分析 [waitress-0]
2025-07-29 09:44:47  EarlyBird.api.thesis.service 943: INFO  add Para:综合所有数据后的整体表现总结 [waitress-0]
2025-07-29 09:44:47  service.py 943: INFO  add Para:综合所有数据后的整体表现总结 [waitress-0]
2025-07-29 09:44:47  EarlyBird.api.thesis.service 943: INFO  add Para:关于CES技术有效性的新见解 [waitress-0]
2025-07-29 09:44:47  service.py 943: INFO  add Para:关于CES技术有效性的新见解 [waitress-0]
2025-07-29 09:44:47  EarlyBird.api.thesis.service 943: INFO  add Para:针对发现的问题提出改进建议 [waitress-0]
2025-07-29 09:44:47  service.py 943: INFO  add Para:针对发现的问题提出改进建议 [waitress-0]
2025-07-29 09:44:47  EarlyBird.api.thesis.service 943: INFO  add Para:结论 [waitress-0]
2025-07-29 09:44:47  service.py 943: INFO  add Para:结论 [waitress-0]
2025-07-29 09:44:47  EarlyBird.api.thesis.service 943: INFO  add Para:研究成果的数据化总结 [waitress-0]
2025-07-29 09:44:47  service.py 943: INFO  add Para:研究成果的数据化总结 [waitress-0]
2025-07-29 09:44:47  EarlyBird.api.thesis.service 943: INFO  add Para:对未来研究方向的展望 [waitress-0]
2025-07-29 09:44:47  service.py 943: INFO  add Para:对未来研究方向的展望 [waitress-0]
2025-07-29 09:44:47  EarlyBird.api.thesis.service 943: INFO  add Para:对于行业发展的政策建议 [waitress-0]
2025-07-29 09:44:47  service.py 943: INFO  add Para:对于行业发展的政策建议 [waitress-0]
2025-07-29 09:44:47  EarlyBird.common.docx 500: INFO  d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\resources\download\[早鸟论文]_CES技术在提高电源效率中的应用研究_V20250729_094447.docx [waitress-0]
2025-07-29 09:44:47  docx.py 500: INFO  d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\resources\download\[早鸟论文]_CES技术在提高电源效率中的应用研究_V20250729_094447.docx [waitress-0]
2025-07-29 09:44:47  EarlyBird.api.thesis.apis 160: INFO  用户 14 下载论文 32 成功 [waitress-0]
2025-07-29 09:44:47  apis.py 160: INFO  用户 14 下载论文 32 成功 [waitress-0]
2025-07-29 09:44:47  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/download [waitress-1]
2025-07-29 09:44:47  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/download [waitress-1]
2025-07-29 09:44:47  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:44:47  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:44:47  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:44:47  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:44:47  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:44:47  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:44:55  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/talk/getOutlineHistory [waitress-3]
2025-07-29 09:44:55  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/talk/getOutlineHistory [waitress-3]
2025-07-29 09:44:55  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:44:55  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:44:55  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:44:55  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:44:55  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:44:55  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:44:55  EarlyBird.api.talk2ai.service 953: INFO  🔍 查询用户 14 的提纲历史记录 [waitress-3]
2025-07-29 09:44:55  service.py 953: INFO  🔍 查询用户 14 的提纲历史记录 [waitress-3]
2025-07-29 09:44:55  EarlyBird.api.talk2ai.service 975: INFO  📝 用户 14 的提纲历史记录：共 1 条，当前页 1 条 [waitress-3]
2025-07-29 09:44:55  service.py 975: INFO  📝 用户 14 的提纲历史记录：共 1 条，当前页 1 条 [waitress-3]
2025-07-29 09:44:59  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/talk/exportOutline [waitress-2]
2025-07-29 09:44:59  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/talk/exportOutline [waitress-2]
2025-07-29 09:44:59  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:44:59  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:44:59  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:44:59  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:44:59  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:44:59  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:44:59  EarlyBird.api.talk2ai.utils 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-2]
2025-07-29 09:44:59  utils.py 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-2]
2025-07-29 09:44:59  EarlyBird.api.talk2ai.utils 55: INFO  提纲数据键: dict_keys(['subtitle', 'title']) [waitress-2]
2025-07-29 09:44:59  utils.py 55: INFO  提纲数据键: dict_keys(['subtitle', 'title']) [waitress-2]
2025-07-29 09:44:59  EarlyBird.api.talk2ai.utils 161: INFO  提纲处理完成，根节点ID: X4nprCqhqnER3sae2T3hnY [waitress-2]
2025-07-29 09:44:59  utils.py 161: INFO  提纲处理完成，根节点ID: X4nprCqhqnER3sae2T3hnY [waitress-2]
2025-07-29 09:45:03  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/talk/checkExistingThesis [waitress-0]
2025-07-29 09:45:03  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/talk/checkExistingThesis [waitress-0]
2025-07-29 09:45:03  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:45:03  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:45:03  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:45:03  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:45:03  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:45:03  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:45:06  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/talk/select4Content [waitress-1]
2025-07-29 09:45:06  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/talk/select4Content [waitress-1]
2025-07-29 09:45:06  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:45:06  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:45:06  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:45:06  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:45:06  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:45:06  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:45:06  EarlyBird.api.talk2ai.apis 128: INFO  Received select4Content request with body type: <class 'EarlyBird.api.talk2ai.dantic.ParamSelect4Content'> [waitress-1]
2025-07-29 09:45:06  apis.py 128: INFO  Received select4Content request with body type: <class 'EarlyBird.api.talk2ai.dantic.ParamSelect4Content'> [waitress-1]
2025-07-29 09:45:06  EarlyBird.api.talk2ai.apis 132: INFO  Request title: CES技术在提高电源效率中的应用研究, level: 本科, lang: 中文 [waitress-1]
2025-07-29 09:45:06  apis.py 132: INFO  Request title: CES技术在提高电源效率中的应用研究, level: 本科, lang: 中文 [waitress-1]
2025-07-29 09:45:06  EarlyBird.api.talk2ai.apis 133: INFO  Outline type: <class 'dict'> [waitress-1]
2025-07-29 09:45:06  apis.py 133: INFO  Outline type: <class 'dict'> [waitress-1]
2025-07-29 09:45:06  EarlyBird.api.talk2ai.apis 154: INFO  Outline keys: dict_keys(['subtitle', 'title']) [waitress-1]
2025-07-29 09:45:06  apis.py 154: INFO  Outline keys: dict_keys(['subtitle', 'title']) [waitress-1]
2025-07-29 09:45:06  EarlyBird.api.talk2ai.apis 170: INFO  VIP状态来源 - g.isVip: False, session有激活码: False [waitress-1]
2025-07-29 09:45:06  apis.py 170: INFO  VIP状态来源 - g.isVip: False, session有激活码: False [waitress-1]
2025-07-29 09:45:06  EarlyBird.api.talk2ai.apis 184: INFO  用户对象VIP状态检查: False [waitress-1]
2025-07-29 09:45:06  apis.py 184: INFO  用户对象VIP状态检查: False [waitress-1]
2025-07-29 09:45:06  EarlyBird.api.talk2ai.apis 188: INFO  最终设置的用户状态 - User ID: 14, VIP: False [waitress-1]
2025-07-29 09:45:06  apis.py 188: INFO  最终设置的用户状态 - User ID: 14, VIP: False [waitress-1]
2025-07-29 09:45:06  EarlyBird.api.talk2ai.apis 195: INFO  Processing select4Content for user 14, title: CES技术在提高电源效率中的应用研究 [waitress-1]
2025-07-29 09:45:06  apis.py 195: INFO  Processing select4Content for user 14, title: CES技术在提高电源效率中的应用研究 [waitress-1]
2025-07-29 09:45:06  EarlyBird.api.talk2ai.service 523: INFO  用户 14 已有 1 篇论文 [waitress-1]
2025-07-29 09:45:06  service.py 523: INFO  用户 14 已有 1 篇论文 [waitress-1]
2025-07-29 09:45:06  EarlyBird.api.talk2ai.service 524: INFO  用户 14 的VIP状态: False [waitress-1]
2025-07-29 09:45:06  service.py 524: INFO  用户 14 的VIP状态: False [waitress-1]
2025-07-29 09:45:06  EarlyBird.api.talk2ai.service 529: INFO  用户 14 在g对象中的VIP状态: False [waitress-1]
2025-07-29 09:45:06  service.py 529: INFO  用户 14 在g对象中的VIP状态: False [waitress-1]
2025-07-29 09:45:06  EarlyBird.api.talk2ai.service 546: INFO  用户对象 14 的VIP状态: False [waitress-1]
2025-07-29 09:45:06  service.py 546: INFO  用户对象 14 的VIP状态: False [waitress-1]
2025-07-29 09:45:06  EarlyBird.api.talk2ai.service 552: INFO  最终判断用户 14 的VIP状态: False [waitress-1]
2025-07-29 09:45:06  service.py 552: INFO  最终判断用户 14 的VIP状态: False [waitress-1]
2025-07-29 09:45:06  EarlyBird.api.talk2ai.service 556: WARNING  免费用户 14 已有 1 篇论文，不能创建更多 [waitress-1]
2025-07-29 09:45:06  service.py 556: WARNING  免费用户 14 已有 1 篇论文，不能创建更多 [waitress-1]
2025-07-29 09:45:06  EarlyBird.api.talk2ai.apis 204: ERROR  select4Content failed for user 14: 免费用户最多只能创建一篇论文，请先删除已有论文再继续 [waitress-1]
2025-07-29 09:45:06  apis.py 204: ERROR  select4Content failed for user 14: 免费用户最多只能创建一篇论文，请先删除已有论文再继续 [waitress-1]
2025-07-29 09:45:08  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getList [waitress-3]
2025-07-29 09:45:08  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getList [waitress-3]
2025-07-29 09:45:08  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:45:08  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:45:08  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:45:08  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:45:08  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:45:08  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:45:08  EarlyBird.api.thesis.apis 92: INFO  用户 14 请求获取论文列表 [waitress-3]
2025-07-29 09:45:08  apis.py 92: INFO  用户 14 请求获取论文列表 [waitress-3]
2025-07-29 09:45:08  EarlyBird.api.thesis.service 640: INFO  查询论文列表，用户ID: 14 [waitress-3]
2025-07-29 09:45:08  service.py 640: INFO  查询论文列表，用户ID: 14 [waitress-3]
2025-07-29 09:45:08  EarlyBird.api.thesis.service 646: INFO  查询到 1 篇论文 [waitress-3]
2025-07-29 09:45:08  service.py 646: INFO  查询到 1 篇论文 [waitress-3]
2025-07-29 09:45:08  EarlyBird.api.thesis.service 656: INFO  论文ID: 32, 标题: CES技术在提高电源效率中的应用研究, 用户ID: 14 [waitress-3]
2025-07-29 09:45:08  service.py 656: INFO  论文ID: 32, 标题: CES技术在提高电源效率中的应用研究, 用户ID: 14 [waitress-3]
2025-07-29 09:45:08  EarlyBird.api.thesis.service 658: INFO  返回论文列表，共 1 篇 [waitress-3]
2025-07-29 09:45:08  service.py 658: INFO  返回论文列表，共 1 篇 [waitress-3]
2025-07-29 09:45:08  EarlyBird.api.thesis.apis 95: INFO  用户 14 获取论文列表成功，共 1 篇 [waitress-3]
2025-07-29 09:45:08  apis.py 95: INFO  用户 14 获取论文列表成功，共 1 篇 [waitress-3]
2025-07-29 09:45:08  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getDetail [waitress-2]
2025-07-29 09:45:08  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getDetail [waitress-2]
2025-07-29 09:45:08  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:45:08  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:45:08  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:45:08  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:45:08  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:45:08  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:45:08  EarlyBird.api.talk2ai.utils 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-2]
2025-07-29 09:45:08  utils.py 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-2]
2025-07-29 09:45:08  EarlyBird.api.talk2ai.utils 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-2]
2025-07-29 09:45:08  utils.py 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-2]
2025-07-29 09:45:08  EarlyBird.api.talk2ai.utils 161: INFO  提纲处理完成，根节点ID: E2em6gS9wrhodhQoZ8tQHc [waitress-2]
2025-07-29 09:45:08  utils.py 161: INFO  提纲处理完成，根节点ID: E2em6gS9wrhodhQoZ8tQHc [waitress-2]
2025-07-29 09:45:08  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getOutline [waitress-0]
2025-07-29 09:45:08  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getOutline [waitress-0]
2025-07-29 09:45:08  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:45:08  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:45:08  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:45:08  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:45:08  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:45:08  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:45:08  EarlyBird.api.talk2ai.utils 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-0]
2025-07-29 09:45:08  utils.py 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-0]
2025-07-29 09:45:08  EarlyBird.api.talk2ai.utils 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-0]
2025-07-29 09:45:08  utils.py 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-0]
2025-07-29 09:45:08  EarlyBird.api.talk2ai.utils 161: INFO  提纲处理完成，根节点ID: E2em6gS9wrhodhQoZ8tQHc [waitress-0]
2025-07-29 09:45:08  utils.py 161: INFO  提纲处理完成，根节点ID: E2em6gS9wrhodhQoZ8tQHc [waitress-0]
2025-07-29 09:45:14  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/deleteThesis [waitress-1]
2025-07-29 09:45:14  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/deleteThesis [waitress-1]
2025-07-29 09:45:14  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:45:14  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:45:14  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:45:14  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:45:14  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:45:14  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:45:17  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/talk/getOutlineHistory [waitress-3]
2025-07-29 09:45:17  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/talk/getOutlineHistory [waitress-3]
2025-07-29 09:45:17  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:45:17  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:45:17  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:45:17  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:45:17  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:45:17  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:45:17  EarlyBird.api.talk2ai.service 953: INFO  🔍 查询用户 14 的提纲历史记录 [waitress-3]
2025-07-29 09:45:17  service.py 953: INFO  🔍 查询用户 14 的提纲历史记录 [waitress-3]
2025-07-29 09:45:17  EarlyBird.api.talk2ai.service 975: INFO  📝 用户 14 的提纲历史记录：共 1 条，当前页 1 条 [waitress-3]
2025-07-29 09:45:17  service.py 975: INFO  📝 用户 14 的提纲历史记录：共 1 条，当前页 1 条 [waitress-3]
2025-07-29 09:45:20  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/talk/checkExistingThesis [waitress-2]
2025-07-29 09:45:20  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/talk/checkExistingThesis [waitress-2]
2025-07-29 09:45:20  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:45:20  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:45:20  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:45:20  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:45:20  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:45:20  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:45:20  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/talk/select4Content [waitress-0]
2025-07-29 09:45:20  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/talk/select4Content [waitress-0]
2025-07-29 09:45:20  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:45:20  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:45:20  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:45:20  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:45:20  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:45:20  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:45:20  EarlyBird.api.talk2ai.apis 128: INFO  Received select4Content request with body type: <class 'EarlyBird.api.talk2ai.dantic.ParamSelect4Content'> [waitress-0]
2025-07-29 09:45:20  apis.py 128: INFO  Received select4Content request with body type: <class 'EarlyBird.api.talk2ai.dantic.ParamSelect4Content'> [waitress-0]
2025-07-29 09:45:20  EarlyBird.api.talk2ai.apis 132: INFO  Request title: CES技术在提高电源效率中的应用研究, level: 本科, lang: 中文 [waitress-0]
2025-07-29 09:45:20  apis.py 132: INFO  Request title: CES技术在提高电源效率中的应用研究, level: 本科, lang: 中文 [waitress-0]
2025-07-29 09:45:20  EarlyBird.api.talk2ai.apis 133: INFO  Outline type: <class 'dict'> [waitress-0]
2025-07-29 09:45:20  apis.py 133: INFO  Outline type: <class 'dict'> [waitress-0]
2025-07-29 09:45:20  EarlyBird.api.talk2ai.apis 154: INFO  Outline keys: dict_keys(['subtitle', 'title']) [waitress-0]
2025-07-29 09:45:20  apis.py 154: INFO  Outline keys: dict_keys(['subtitle', 'title']) [waitress-0]
2025-07-29 09:45:20  EarlyBird.api.talk2ai.apis 170: INFO  VIP状态来源 - g.isVip: False, session有激活码: False [waitress-0]
2025-07-29 09:45:20  apis.py 170: INFO  VIP状态来源 - g.isVip: False, session有激活码: False [waitress-0]
2025-07-29 09:45:20  EarlyBird.api.talk2ai.apis 184: INFO  用户对象VIP状态检查: False [waitress-0]
2025-07-29 09:45:20  apis.py 184: INFO  用户对象VIP状态检查: False [waitress-0]
2025-07-29 09:45:20  EarlyBird.api.talk2ai.apis 188: INFO  最终设置的用户状态 - User ID: 14, VIP: False [waitress-0]
2025-07-29 09:45:20  apis.py 188: INFO  最终设置的用户状态 - User ID: 14, VIP: False [waitress-0]
2025-07-29 09:45:20  EarlyBird.api.talk2ai.apis 195: INFO  Processing select4Content for user 14, title: CES技术在提高电源效率中的应用研究 [waitress-0]
2025-07-29 09:45:20  apis.py 195: INFO  Processing select4Content for user 14, title: CES技术在提高电源效率中的应用研究 [waitress-0]
2025-07-29 09:45:20  EarlyBird.api.talk2ai.service 523: INFO  用户 14 已有 0 篇论文 [waitress-0]
2025-07-29 09:45:20  service.py 523: INFO  用户 14 已有 0 篇论文 [waitress-0]
2025-07-29 09:45:20  EarlyBird.api.talk2ai.service 524: INFO  用户 14 的VIP状态: False [waitress-0]
2025-07-29 09:45:20  service.py 524: INFO  用户 14 的VIP状态: False [waitress-0]
2025-07-29 09:45:20  EarlyBird.api.talk2ai.service 529: INFO  用户 14 在g对象中的VIP状态: False [waitress-0]
2025-07-29 09:45:20  service.py 529: INFO  用户 14 在g对象中的VIP状态: False [waitress-0]
2025-07-29 09:45:20  EarlyBird.api.talk2ai.service 546: INFO  用户对象 14 的VIP状态: False [waitress-0]
2025-07-29 09:45:20  service.py 546: INFO  用户对象 14 的VIP状态: False [waitress-0]
2025-07-29 09:45:20  EarlyBird.api.talk2ai.service 552: INFO  最终判断用户 14 的VIP状态: False [waitress-0]
2025-07-29 09:45:20  service.py 552: INFO  最终判断用户 14 的VIP状态: False [waitress-0]
2025-07-29 09:45:20  EarlyBird.api.talk2ai.service 578: INFO  接收到的提纲数据类型: <class 'dict'> [waitress-0]
2025-07-29 09:45:20  service.py 578: INFO  接收到的提纲数据类型: <class 'dict'> [waitress-0]
2025-07-29 09:45:20  EarlyBird.api.talk2ai.service 580: INFO  提纲数据键: dict_keys(['subtitle', 'title']) [waitress-0]
2025-07-29 09:45:20  service.py 580: INFO  提纲数据键: dict_keys(['subtitle', 'title']) [waitress-0]
2025-07-29 09:45:20  EarlyBird.api.talk2ai.service 581: INFO  提纲数据内容: {"subtitle": [{"subtitle": [{"subtitle": [], "title": "研究背景与数据现状", "id": "rbl28539vqpwr6rsd60bqs"}, {"subtitle": [], "title": "CES技术在电源效率提升中的重要性分析", "id": "bqfjhe5rh29iajptu0lvkd"}, {"subtitle": [], "... [waitress-0]
2025-07-29 09:45:20  service.py 581: INFO  提纲数据内容: {"subtitle": [{"subtitle": [{"subtitle": [], "title": "研究背景与数据现状", "id": "rbl28539vqpwr6rsd60bqs"}, {"subtitle": [], "title": "CES技术在电源效率提升中的重要性分析", "id": "bqfjhe5rh29iajptu0lvkd"}, {"subtitle": [], "... [waitress-0]
2025-07-29 09:45:20  EarlyBird.api.talk2ai.utils 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-0]
2025-07-29 09:45:20  utils.py 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-0]
2025-07-29 09:45:20  EarlyBird.api.talk2ai.utils 55: INFO  提纲数据键: dict_keys(['subtitle', 'title']) [waitress-0]
2025-07-29 09:45:20  utils.py 55: INFO  提纲数据键: dict_keys(['subtitle', 'title']) [waitress-0]
2025-07-29 09:45:20  EarlyBird.api.talk2ai.utils 161: INFO  提纲处理完成，根节点ID: N2GpbeovHh2h35yBnccyB3 [waitress-0]
2025-07-29 09:45:20  utils.py 161: INFO  提纲处理完成，根节点ID: N2GpbeovHh2h35yBnccyB3 [waitress-0]
2025-07-29 09:45:20  EarlyBird.api.talk2ai.service 677: INFO  提纲处理完成，ID: N2GpbeovHh2h35yBnccyB3 [waitress-0]
2025-07-29 09:45:20  service.py 677: INFO  提纲处理完成，ID: N2GpbeovHh2h35yBnccyB3 [waitress-0]
2025-07-29 09:45:20  EarlyBird.api.talk2ai.service 687: INFO  成功解析提纲，找到 25 个段落标题 [waitress-0]
2025-07-29 09:45:20  service.py 687: INFO  成功解析提纲，找到 25 个段落标题 [waitress-0]
2025-07-29 09:45:20  EarlyBird.api.talk2ai.service 703: INFO  已创建论文记录，ID: 33 [waitress-0]
2025-07-29 09:45:20  service.py 703: INFO  已创建论文记录，ID: 33 [waitress-0]
2025-07-29 09:45:20  EarlyBird.api.talk2ai.service 717: INFO  已保存 25 个段落记录 [waitress-0]
2025-07-29 09:45:20  service.py 717: INFO  已保存 25 个段落记录 [waitress-0]
2025-07-29 09:45:20  EarlyBird.api.talk2ai.apis 201: INFO  select4Content success for user 14, thesis ID: 33 [waitress-0]
2025-07-29 09:45:20  apis.py 201: INFO  select4Content success for user 14, thesis ID: 33 [waitress-0]
2025-07-29 09:45:20  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getList [waitress-1]
2025-07-29 09:45:20  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getList [waitress-1]
2025-07-29 09:45:20  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:45:20  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:45:20  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:45:20  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:45:20  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:45:20  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:45:20  EarlyBird.api.thesis.apis 92: INFO  用户 14 请求获取论文列表 [waitress-1]
2025-07-29 09:45:20  apis.py 92: INFO  用户 14 请求获取论文列表 [waitress-1]
2025-07-29 09:45:20  EarlyBird.api.thesis.service 640: INFO  查询论文列表，用户ID: 14 [waitress-1]
2025-07-29 09:45:20  service.py 640: INFO  查询论文列表，用户ID: 14 [waitress-1]
2025-07-29 09:45:20  EarlyBird.api.thesis.service 646: INFO  查询到 1 篇论文 [waitress-1]
2025-07-29 09:45:20  service.py 646: INFO  查询到 1 篇论文 [waitress-1]
2025-07-29 09:45:20  EarlyBird.api.thesis.service 656: INFO  论文ID: 33, 标题: CES技术在提高电源效率中的应用研究, 用户ID: 14 [waitress-1]
2025-07-29 09:45:20  service.py 656: INFO  论文ID: 33, 标题: CES技术在提高电源效率中的应用研究, 用户ID: 14 [waitress-1]
2025-07-29 09:45:20  EarlyBird.api.thesis.service 658: INFO  返回论文列表，共 1 篇 [waitress-1]
2025-07-29 09:45:20  service.py 658: INFO  返回论文列表，共 1 篇 [waitress-1]
2025-07-29 09:45:20  EarlyBird.api.thesis.apis 95: INFO  用户 14 获取论文列表成功，共 1 篇 [waitress-1]
2025-07-29 09:45:20  apis.py 95: INFO  用户 14 获取论文列表成功，共 1 篇 [waitress-1]
2025-07-29 09:45:20  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getDetail [waitress-3]
2025-07-29 09:45:20  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getDetail [waitress-3]
2025-07-29 09:45:20  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:45:20  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:45:20  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:45:20  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:45:20  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:45:20  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:45:20  EarlyBird.api.talk2ai.utils 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-3]
2025-07-29 09:45:20  utils.py 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-3]
2025-07-29 09:45:20  EarlyBird.api.talk2ai.utils 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-3]
2025-07-29 09:45:20  utils.py 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-3]
2025-07-29 09:45:20  EarlyBird.api.talk2ai.utils 161: INFO  提纲处理完成，根节点ID: N2GpbeovHh2h35yBnccyB3 [waitress-3]
2025-07-29 09:45:20  utils.py 161: INFO  提纲处理完成，根节点ID: N2GpbeovHh2h35yBnccyB3 [waitress-3]
2025-07-29 09:45:20  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getOutline [waitress-2]
2025-07-29 09:45:20  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getOutline [waitress-2]
2025-07-29 09:45:20  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:45:20  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:45:20  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:45:20  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:45:20  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:45:20  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:45:20  EarlyBird.api.talk2ai.utils 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-2]
2025-07-29 09:45:20  utils.py 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-2]
2025-07-29 09:45:20  EarlyBird.api.talk2ai.utils 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-2]
2025-07-29 09:45:20  utils.py 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-2]
2025-07-29 09:45:20  EarlyBird.api.talk2ai.utils 161: INFO  提纲处理完成，根节点ID: N2GpbeovHh2h35yBnccyB3 [waitress-2]
2025-07-29 09:45:20  utils.py 161: INFO  提纲处理完成，根节点ID: N2GpbeovHh2h35yBnccyB3 [waitress-2]
2025-07-29 09:45:24  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/downloadThesis [waitress-0]
2025-07-29 09:45:24  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/downloadThesis [waitress-0]
2025-07-29 09:45:24  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:45:24  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:45:24  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:45:24  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:45:24  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:45:24  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:45:24  EarlyBird.api.thesis.apis 145: INFO  用户 14 请求下载论文 33 [waitress-0]
2025-07-29 09:45:24  apis.py 145: INFO  用户 14 请求下载论文 33 [waitress-0]
2025-07-29 09:45:24  EarlyBird.api.thesis.service 296: INFO  用户 14 请求导出论文 33 [waitress-0]
2025-07-29 09:45:24  service.py 296: INFO  用户 14 请求导出论文 33 [waitress-0]
2025-07-29 09:45:24  EarlyBird.api.thesis.service 306: INFO  用户 14 VIP状态: <bound method User.isVip of {"accessToken": null, "phone": null, "vip_expire_at": null, "refreshToken": null, "gender": 3, "balance": null, "update_time": "2025-07-29 09:28:33", "nickname": "\u7528\u6237_test1011", "email": null, "id": 14, "headimg": null, "is_lock": false, "invite_code": null, "is_deleted": 0, "activation_key": null, "last_login_time": "2025-07-29 09:28:32", "from_user_id": null, "status": 1, "username": "test1011", "last_login_ip": "127.0.0.1", "openPlatform": "wx", "password_hash": "pbkdf2:sha256:260000$3VYSebusaQuZ1fdD$9217323331b6562a816f82255c4826fd0d815e00bee146926900d409eee250fd", "vip_level": 1, "create_time": "2025-07-28 16:58:53", "openId": null, "realname": null, "vip_start_at": null}> [waitress-0]
2025-07-29 09:45:24  service.py 306: INFO  用户 14 VIP状态: <bound method User.isVip of {"accessToken": null, "phone": null, "vip_expire_at": null, "refreshToken": null, "gender": 3, "balance": null, "update_time": "2025-07-29 09:28:33", "nickname": "\u7528\u6237_test1011", "email": null, "id": 14, "headimg": null, "is_lock": false, "invite_code": null, "is_deleted": 0, "activation_key": null, "last_login_time": "2025-07-29 09:28:32", "from_user_id": null, "status": 1, "username": "test1011", "last_login_ip": "127.0.0.1", "openPlatform": "wx", "password_hash": "pbkdf2:sha256:260000$3VYSebusaQuZ1fdD$9217323331b6562a816f82255c4826fd0d815e00bee146926900d409eee250fd", "vip_level": 1, "create_time": "2025-07-28 16:58:53", "openId": null, "realname": null, "vip_start_at": null}> [waitress-0]
2025-07-29 09:45:24  EarlyBird.api.thesis.service 317: INFO  论文下载收费功能是否启用: True [waitress-0]
2025-07-29 09:45:24  service.py 317: INFO  论文下载收费功能是否启用: True [waitress-0]
2025-07-29 09:45:24  EarlyBird.api.thesis.service 339: INFO  VIP用户是否免费下载: False [waitress-0]
2025-07-29 09:45:24  service.py 339: INFO  VIP用户是否免费下载: False [waitress-0]
2025-07-29 09:45:24  EarlyBird.api.thesis.service 357: INFO  首次下载免费配置查询结果: {"id": 9, "name": "\u9996\u6b21\u4e0b\u8f7d\u662f\u5426\u514d\u8d39", "config_key": "thesis.download.first_free", "config_value": "false", "description": "\u7528\u6237\u9996\u6b21\u4e0b\u8f7d\u8bba\u6587\u662f\u5426\u514d\u8d39", "create_time": "2025-06-28 15:32:25"} [waitress-0]
2025-07-29 09:45:24  service.py 357: INFO  首次下载免费配置查询结果: {"id": 9, "name": "\u9996\u6b21\u4e0b\u8f7d\u662f\u5426\u514d\u8d39", "config_key": "thesis.download.first_free", "config_value": "false", "description": "\u7528\u6237\u9996\u6b21\u4e0b\u8f7d\u8bba\u6587\u662f\u5426\u514d\u8d39", "create_time": "2025-06-28 15:32:25"} [waitress-0]
2025-07-29 09:45:24  EarlyBird.api.thesis.service 359: INFO  配置值: 'false' (类型: <class 'str'>) [waitress-0]
2025-07-29 09:45:24  service.py 359: INFO  配置值: 'false' (类型: <class 'str'>) [waitress-0]
2025-07-29 09:45:24  EarlyBird.api.thesis.service 360: INFO  is_deleted: 0 [waitress-0]
2025-07-29 09:45:24  service.py 360: INFO  is_deleted: 0 [waitress-0]
2025-07-29 09:45:24  EarlyBird.api.thesis.service 367: INFO  配置值处理: 'false' -> 'false' -> False [waitress-0]
2025-07-29 09:45:24  service.py 367: INFO  配置值处理: 'false' -> 'false' -> False [waitress-0]
2025-07-29 09:45:24  EarlyBird.api.thesis.service 371: INFO  首次下载是否免费: False [waitress-0]
2025-07-29 09:45:24  service.py 371: INFO  首次下载是否免费: False [waitress-0]
2025-07-29 09:45:24  EarlyBird.api.thesis.service 376: INFO  用户 14 是否有任何下载记录: True [waitress-0]
2025-07-29 09:45:24  service.py 376: INFO  用户 14 是否有任何下载记录: True [waitress-0]
2025-07-29 09:45:24  EarlyBird.api.thesis.service 381: INFO  用户 14 共有 1 条下载记录 [waitress-0]
2025-07-29 09:45:24  service.py 381: INFO  用户 14 共有 1 条下载记录 [waitress-0]
2025-07-29 09:45:24  EarlyBird.api.thesis.service 393: INFO  用户 14 的下载记录都是今天的测试记录，视为首次下载 [waitress-0]
2025-07-29 09:45:24  service.py 393: INFO  用户 14 的下载记录都是今天的测试记录，视为首次下载 [waitress-0]
2025-07-29 09:45:24  EarlyBird.api.thesis.service 504: INFO  从配置中读取到论文下载价格: 10.0 [waitress-0]
2025-07-29 09:45:24  service.py 504: INFO  从配置中读取到论文下载价格: 10.0 [waitress-0]
2025-07-29 09:45:24  EarlyBird.api.thesis.service 512: INFO  用户 14 需要支付才能下载论文 33，价格: 10.0 [waitress-0]
2025-07-29 09:45:24  service.py 512: INFO  用户 14 需要支付才能下载论文 33，价格: 10.0 [waitress-0]
2025-07-29 09:45:24  EarlyBird.api.thesis.apis 151: INFO  用户 14 需要支付才能下载论文 33，价格: 10.0 [waitress-0]
2025-07-29 09:45:24  apis.py 151: INFO  用户 14 需要支付才能下载论文 33，价格: 10.0 [waitress-0]
2025-07-29 09:45:24  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/payDownload [waitress-1]
2025-07-29 09:45:24  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/payDownload [waitress-1]
2025-07-29 09:45:24  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:45:24  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:45:24  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:45:24  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:45:24  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:45:24  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:45:25  EarlyBird.api.thesis.service 769: INFO  创建支付记录成功 - 订单: thesis_download_c52a3a47b8a5, 金额: 10.0 [waitress-1]
2025-07-29 09:45:25  service.py 769: INFO  创建支付记录成功 - 订单: thesis_download_c52a3a47b8a5, 金额: 10.0 [waitress-1]
2025-07-29 09:45:28  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-3]
2025-07-29 09:45:28  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-3]
2025-07-29 09:45:28  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:45:28  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:45:28  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:45:28  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:45:28  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:45:28  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:45:28  EarlyBird.api.thesis.service 791: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_c52a3a47b8a5 [waitress-3]
2025-07-29 09:45:28  service.py 791: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_c52a3a47b8a5 [waitress-3]
2025-07-29 09:45:28  EarlyBird.api.thesis.service 833: INFO  找到支付记录 - 订单: thesis_download_c52a3a47b8a5, 状态: 0 [waitress-3]
2025-07-29 09:45:28  service.py 833: INFO  找到支付记录 - 订单: thesis_download_c52a3a47b8a5, 状态: 0 [waitress-3]
2025-07-29 09:45:31  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-2]
2025-07-29 09:45:31  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-2]
2025-07-29 09:45:31  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:45:31  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:45:31  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:45:31  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:45:31  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:45:31  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:45:31  EarlyBird.api.thesis.service 791: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_c52a3a47b8a5 [waitress-2]
2025-07-29 09:45:31  service.py 791: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_c52a3a47b8a5 [waitress-2]
2025-07-29 09:45:31  EarlyBird.api.thesis.service 833: INFO  找到支付记录 - 订单: thesis_download_c52a3a47b8a5, 状态: 0 [waitress-2]
2025-07-29 09:45:31  service.py 833: INFO  找到支付记录 - 订单: thesis_download_c52a3a47b8a5, 状态: 0 [waitress-2]
2025-07-29 09:45:34  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-0]
2025-07-29 09:45:34  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-0]
2025-07-29 09:45:34  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:45:34  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:45:34  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:45:34  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:45:34  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:45:34  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:45:34  EarlyBird.api.thesis.service 791: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_c52a3a47b8a5 [waitress-0]
2025-07-29 09:45:34  service.py 791: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_c52a3a47b8a5 [waitress-0]
2025-07-29 09:45:34  EarlyBird.api.thesis.service 833: INFO  找到支付记录 - 订单: thesis_download_c52a3a47b8a5, 状态: 0 [waitress-0]
2025-07-29 09:45:34  service.py 833: INFO  找到支付记录 - 订单: thesis_download_c52a3a47b8a5, 状态: 0 [waitress-0]
2025-07-29 09:45:37  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-1]
2025-07-29 09:45:37  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-1]
2025-07-29 09:45:37  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:45:37  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:45:37  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:45:37  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:45:37  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:45:37  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:45:37  EarlyBird.api.thesis.service 791: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_c52a3a47b8a5 [waitress-1]
2025-07-29 09:45:37  service.py 791: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_c52a3a47b8a5 [waitress-1]
2025-07-29 09:45:37  EarlyBird.api.thesis.service 833: INFO  找到支付记录 - 订单: thesis_download_c52a3a47b8a5, 状态: 0 [waitress-1]
2025-07-29 09:45:37  service.py 833: INFO  找到支付记录 - 订单: thesis_download_c52a3a47b8a5, 状态: 0 [waitress-1]
2025-07-29 09:45:40  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-3]
2025-07-29 09:45:40  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-3]
2025-07-29 09:45:40  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:45:40  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:45:40  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:45:40  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:45:40  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:45:40  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:45:40  EarlyBird.api.thesis.service 791: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_c52a3a47b8a5 [waitress-3]
2025-07-29 09:45:40  service.py 791: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_c52a3a47b8a5 [waitress-3]
2025-07-29 09:45:40  EarlyBird.api.thesis.service 833: INFO  找到支付记录 - 订单: thesis_download_c52a3a47b8a5, 状态: 0 [waitress-3]
2025-07-29 09:45:40  service.py 833: INFO  找到支付记录 - 订单: thesis_download_c52a3a47b8a5, 状态: 0 [waitress-3]
2025-07-29 09:45:43  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-2]
2025-07-29 09:45:43  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-2]
2025-07-29 09:45:43  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:45:43  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:45:43  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:45:43  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:45:43  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:45:43  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:45:43  EarlyBird.api.thesis.service 791: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_c52a3a47b8a5 [waitress-2]
2025-07-29 09:45:43  service.py 791: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_c52a3a47b8a5 [waitress-2]
2025-07-29 09:45:43  EarlyBird.api.thesis.service 833: INFO  找到支付记录 - 订单: thesis_download_c52a3a47b8a5, 状态: 0 [waitress-2]
2025-07-29 09:45:43  service.py 833: INFO  找到支付记录 - 订单: thesis_download_c52a3a47b8a5, 状态: 0 [waitress-2]
2025-07-29 09:45:46  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-0]
2025-07-29 09:45:46  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-0]
2025-07-29 09:45:46  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:45:46  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:45:46  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:45:46  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:45:46  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:45:46  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:45:46  EarlyBird.api.thesis.service 791: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_c52a3a47b8a5 [waitress-0]
2025-07-29 09:45:46  service.py 791: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_c52a3a47b8a5 [waitress-0]
2025-07-29 09:45:46  EarlyBird.api.thesis.service 833: INFO  找到支付记录 - 订单: thesis_download_c52a3a47b8a5, 状态: 0 [waitress-0]
2025-07-29 09:45:46  service.py 833: INFO  找到支付记录 - 订单: thesis_download_c52a3a47b8a5, 状态: 0 [waitress-0]
2025-07-29 09:45:49  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-1]
2025-07-29 09:45:49  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-1]
2025-07-29 09:45:49  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:45:49  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:45:49  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:45:49  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:45:49  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:45:49  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:45:49  EarlyBird.api.thesis.service 791: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_c52a3a47b8a5 [waitress-1]
2025-07-29 09:45:49  service.py 791: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_c52a3a47b8a5 [waitress-1]
2025-07-29 09:45:49  EarlyBird.api.thesis.service 833: INFO  找到支付记录 - 订单: thesis_download_c52a3a47b8a5, 状态: 0 [waitress-1]
2025-07-29 09:45:49  service.py 833: INFO  找到支付记录 - 订单: thesis_download_c52a3a47b8a5, 状态: 0 [waitress-1]
2025-07-29 09:45:52  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-3]
2025-07-29 09:45:52  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-3]
2025-07-29 09:45:52  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:45:52  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:45:52  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:45:52  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:45:52  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:45:52  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:45:52  EarlyBird.api.thesis.service 791: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_c52a3a47b8a5 [waitress-3]
2025-07-29 09:45:52  service.py 791: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_c52a3a47b8a5 [waitress-3]
2025-07-29 09:45:52  EarlyBird.api.thesis.service 833: INFO  找到支付记录 - 订单: thesis_download_c52a3a47b8a5, 状态: 0 [waitress-3]
2025-07-29 09:45:52  service.py 833: INFO  找到支付记录 - 订单: thesis_download_c52a3a47b8a5, 状态: 0 [waitress-3]
2025-07-29 09:45:55  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-2]
2025-07-29 09:45:55  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-2]
2025-07-29 09:45:55  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:45:55  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:45:55  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:45:55  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:45:55  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:45:55  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:45:55  EarlyBird.api.thesis.service 791: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_c52a3a47b8a5 [waitress-2]
2025-07-29 09:45:55  service.py 791: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_c52a3a47b8a5 [waitress-2]
2025-07-29 09:45:55  EarlyBird.api.thesis.service 833: INFO  找到支付记录 - 订单: thesis_download_c52a3a47b8a5, 状态: 0 [waitress-2]
2025-07-29 09:45:55  service.py 833: INFO  找到支付记录 - 订单: thesis_download_c52a3a47b8a5, 状态: 0 [waitress-2]
2025-07-29 09:45:57  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/downloadThesis [waitress-0]
2025-07-29 09:45:57  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/downloadThesis [waitress-0]
2025-07-29 09:45:57  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:45:57  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:45:57  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:45:57  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:45:57  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:45:57  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:45:57  EarlyBird.api.thesis.apis 145: INFO  用户 14 请求下载论文 33 [waitress-0]
2025-07-29 09:45:57  apis.py 145: INFO  用户 14 请求下载论文 33 [waitress-0]
2025-07-29 09:45:57  EarlyBird.api.thesis.service 296: INFO  用户 14 请求导出论文 33 [waitress-0]
2025-07-29 09:45:57  service.py 296: INFO  用户 14 请求导出论文 33 [waitress-0]
2025-07-29 09:45:57  EarlyBird.api.thesis.service 306: INFO  用户 14 VIP状态: <bound method User.isVip of {"accessToken": null, "phone": null, "vip_expire_at": null, "refreshToken": null, "gender": 3, "balance": null, "update_time": "2025-07-29 09:28:33", "nickname": "\u7528\u6237_test1011", "email": null, "id": 14, "headimg": null, "is_lock": false, "invite_code": null, "is_deleted": 0, "activation_key": null, "last_login_time": "2025-07-29 09:28:32", "from_user_id": null, "status": 1, "username": "test1011", "last_login_ip": "127.0.0.1", "openPlatform": "wx", "password_hash": "pbkdf2:sha256:260000$3VYSebusaQuZ1fdD$9217323331b6562a816f82255c4826fd0d815e00bee146926900d409eee250fd", "vip_level": 1, "create_time": "2025-07-28 16:58:53", "openId": null, "realname": null, "vip_start_at": null}> [waitress-0]
2025-07-29 09:45:57  service.py 306: INFO  用户 14 VIP状态: <bound method User.isVip of {"accessToken": null, "phone": null, "vip_expire_at": null, "refreshToken": null, "gender": 3, "balance": null, "update_time": "2025-07-29 09:28:33", "nickname": "\u7528\u6237_test1011", "email": null, "id": 14, "headimg": null, "is_lock": false, "invite_code": null, "is_deleted": 0, "activation_key": null, "last_login_time": "2025-07-29 09:28:32", "from_user_id": null, "status": 1, "username": "test1011", "last_login_ip": "127.0.0.1", "openPlatform": "wx", "password_hash": "pbkdf2:sha256:260000$3VYSebusaQuZ1fdD$9217323331b6562a816f82255c4826fd0d815e00bee146926900d409eee250fd", "vip_level": 1, "create_time": "2025-07-28 16:58:53", "openId": null, "realname": null, "vip_start_at": null}> [waitress-0]
2025-07-29 09:45:57  EarlyBird.api.thesis.service 317: INFO  论文下载收费功能是否启用: True [waitress-0]
2025-07-29 09:45:57  service.py 317: INFO  论文下载收费功能是否启用: True [waitress-0]
2025-07-29 09:45:57  EarlyBird.api.thesis.service 339: INFO  VIP用户是否免费下载: False [waitress-0]
2025-07-29 09:45:57  service.py 339: INFO  VIP用户是否免费下载: False [waitress-0]
2025-07-29 09:45:57  EarlyBird.api.thesis.service 357: INFO  首次下载免费配置查询结果: {"id": 9, "name": "\u9996\u6b21\u4e0b\u8f7d\u662f\u5426\u514d\u8d39", "config_key": "thesis.download.first_free", "config_value": "false", "description": "\u7528\u6237\u9996\u6b21\u4e0b\u8f7d\u8bba\u6587\u662f\u5426\u514d\u8d39", "create_time": "2025-06-28 15:32:25"} [waitress-0]
2025-07-29 09:45:57  service.py 357: INFO  首次下载免费配置查询结果: {"id": 9, "name": "\u9996\u6b21\u4e0b\u8f7d\u662f\u5426\u514d\u8d39", "config_key": "thesis.download.first_free", "config_value": "false", "description": "\u7528\u6237\u9996\u6b21\u4e0b\u8f7d\u8bba\u6587\u662f\u5426\u514d\u8d39", "create_time": "2025-06-28 15:32:25"} [waitress-0]
2025-07-29 09:45:57  EarlyBird.api.thesis.service 359: INFO  配置值: 'false' (类型: <class 'str'>) [waitress-0]
2025-07-29 09:45:57  service.py 359: INFO  配置值: 'false' (类型: <class 'str'>) [waitress-0]
2025-07-29 09:45:57  EarlyBird.api.thesis.service 360: INFO  is_deleted: 0 [waitress-0]
2025-07-29 09:45:57  service.py 360: INFO  is_deleted: 0 [waitress-0]
2025-07-29 09:45:57  EarlyBird.api.thesis.service 367: INFO  配置值处理: 'false' -> 'false' -> False [waitress-0]
2025-07-29 09:45:57  service.py 367: INFO  配置值处理: 'false' -> 'false' -> False [waitress-0]
2025-07-29 09:45:57  EarlyBird.api.thesis.service 371: INFO  首次下载是否免费: False [waitress-0]
2025-07-29 09:45:57  service.py 371: INFO  首次下载是否免费: False [waitress-0]
2025-07-29 09:45:57  EarlyBird.api.thesis.service 376: INFO  用户 14 是否有任何下载记录: True [waitress-0]
2025-07-29 09:45:57  service.py 376: INFO  用户 14 是否有任何下载记录: True [waitress-0]
2025-07-29 09:45:57  EarlyBird.api.thesis.service 381: INFO  用户 14 共有 2 条下载记录 [waitress-0]
2025-07-29 09:45:57  service.py 381: INFO  用户 14 共有 2 条下载记录 [waitress-0]
2025-07-29 09:45:57  EarlyBird.api.thesis.service 393: INFO  用户 14 的下载记录都是今天的测试记录，视为首次下载 [waitress-0]
2025-07-29 09:45:57  service.py 393: INFO  用户 14 的下载记录都是今天的测试记录，视为首次下载 [waitress-0]
2025-07-29 09:45:57  EarlyBird.api.thesis.service 504: INFO  从配置中读取到论文下载价格: 10.0 [waitress-0]
2025-07-29 09:45:57  service.py 504: INFO  从配置中读取到论文下载价格: 10.0 [waitress-0]
2025-07-29 09:45:57  EarlyBird.api.thesis.service 512: INFO  用户 14 需要支付才能下载论文 33，价格: 10.0 [waitress-0]
2025-07-29 09:45:57  service.py 512: INFO  用户 14 需要支付才能下载论文 33，价格: 10.0 [waitress-0]
2025-07-29 09:45:57  EarlyBird.api.thesis.apis 151: INFO  用户 14 需要支付才能下载论文 33，价格: 10.0 [waitress-0]
2025-07-29 09:45:57  apis.py 151: INFO  用户 14 需要支付才能下载论文 33，价格: 10.0 [waitress-0]
2025-07-29 09:45:57  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/payDownload [waitress-1]
2025-07-29 09:45:57  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/payDownload [waitress-1]
2025-07-29 09:45:57  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:45:57  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:45:57  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:45:57  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:45:57  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:45:57  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:45:57  EarlyBird.api.thesis.service 769: INFO  创建支付记录成功 - 订单: thesis_download_a63ba6624d57, 金额: 10.0 [waitress-1]
2025-07-29 09:45:57  service.py 769: INFO  创建支付记录成功 - 订单: thesis_download_a63ba6624d57, 金额: 10.0 [waitress-1]
2025-07-29 09:46:00  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-3]
2025-07-29 09:46:00  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-3]
2025-07-29 09:46:00  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:46:00  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:46:00  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:46:00  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:46:00  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:46:00  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:46:00  EarlyBird.api.thesis.service 791: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_a63ba6624d57 [waitress-3]
2025-07-29 09:46:00  service.py 791: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_a63ba6624d57 [waitress-3]
2025-07-29 09:46:00  EarlyBird.api.thesis.service 833: INFO  找到支付记录 - 订单: thesis_download_a63ba6624d57, 状态: 0 [waitress-3]
2025-07-29 09:46:00  service.py 833: INFO  找到支付记录 - 订单: thesis_download_a63ba6624d57, 状态: 0 [waitress-3]
2025-07-29 09:46:03  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-2]
2025-07-29 09:46:03  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-2]
2025-07-29 09:46:03  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:46:03  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:46:03  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:46:03  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:46:03  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:46:03  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:46:03  EarlyBird.api.thesis.service 791: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_a63ba6624d57 [waitress-2]
2025-07-29 09:46:03  service.py 791: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_a63ba6624d57 [waitress-2]
2025-07-29 09:46:03  EarlyBird.api.thesis.service 833: INFO  找到支付记录 - 订单: thesis_download_a63ba6624d57, 状态: 0 [waitress-2]
2025-07-29 09:46:03  service.py 833: INFO  找到支付记录 - 订单: thesis_download_a63ba6624d57, 状态: 0 [waitress-2]
2025-07-29 09:46:06  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-0]
2025-07-29 09:46:06  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-0]
2025-07-29 09:46:06  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:46:06  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:46:06  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:46:06  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:46:06  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:46:06  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:46:06  EarlyBird.api.thesis.service 791: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_a63ba6624d57 [waitress-0]
2025-07-29 09:46:06  service.py 791: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_a63ba6624d57 [waitress-0]
2025-07-29 09:46:06  EarlyBird.api.thesis.service 833: INFO  找到支付记录 - 订单: thesis_download_a63ba6624d57, 状态: 0 [waitress-0]
2025-07-29 09:46:06  service.py 833: INFO  找到支付记录 - 订单: thesis_download_a63ba6624d57, 状态: 0 [waitress-0]
2025-07-29 09:46:09  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-1]
2025-07-29 09:46:09  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-1]
2025-07-29 09:46:09  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:46:09  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:46:09  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:46:09  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:46:09  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:46:09  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:46:09  EarlyBird.api.thesis.service 791: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_a63ba6624d57 [waitress-1]
2025-07-29 09:46:09  service.py 791: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_a63ba6624d57 [waitress-1]
2025-07-29 09:46:09  EarlyBird.api.thesis.service 833: INFO  找到支付记录 - 订单: thesis_download_a63ba6624d57, 状态: 0 [waitress-1]
2025-07-29 09:46:09  service.py 833: INFO  找到支付记录 - 订单: thesis_download_a63ba6624d57, 状态: 0 [waitress-1]
2025-07-29 09:46:12  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-3]
2025-07-29 09:46:12  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-3]
2025-07-29 09:46:12  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:46:12  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:46:12  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:46:12  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:46:12  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:46:12  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:46:12  EarlyBird.api.thesis.service 791: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_a63ba6624d57 [waitress-3]
2025-07-29 09:46:12  service.py 791: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_a63ba6624d57 [waitress-3]
2025-07-29 09:46:12  EarlyBird.api.thesis.service 833: INFO  找到支付记录 - 订单: thesis_download_a63ba6624d57, 状态: 0 [waitress-3]
2025-07-29 09:46:12  service.py 833: INFO  找到支付记录 - 订单: thesis_download_a63ba6624d57, 状态: 0 [waitress-3]
2025-07-29 09:46:15  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-2]
2025-07-29 09:46:15  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-2]
2025-07-29 09:46:15  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:46:15  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:46:15  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:46:15  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:46:15  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:46:15  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:46:15  EarlyBird.api.thesis.service 791: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_a63ba6624d57 [waitress-2]
2025-07-29 09:46:15  service.py 791: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_a63ba6624d57 [waitress-2]
2025-07-29 09:46:15  EarlyBird.api.thesis.service 833: INFO  找到支付记录 - 订单: thesis_download_a63ba6624d57, 状态: 0 [waitress-2]
2025-07-29 09:46:15  service.py 833: INFO  找到支付记录 - 订单: thesis_download_a63ba6624d57, 状态: 0 [waitress-2]
2025-07-29 09:46:18  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-0]
2025-07-29 09:46:18  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-0]
2025-07-29 09:46:18  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:46:18  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:46:18  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:46:18  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:46:18  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:46:18  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:46:18  EarlyBird.api.thesis.service 791: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_a63ba6624d57 [waitress-0]
2025-07-29 09:46:18  service.py 791: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_a63ba6624d57 [waitress-0]
2025-07-29 09:46:18  EarlyBird.api.thesis.service 833: INFO  找到支付记录 - 订单: thesis_download_a63ba6624d57, 状态: 0 [waitress-0]
2025-07-29 09:46:18  service.py 833: INFO  找到支付记录 - 订单: thesis_download_a63ba6624d57, 状态: 0 [waitress-0]
2025-07-29 09:46:21  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-1]
2025-07-29 09:46:21  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-1]
2025-07-29 09:46:21  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:46:21  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:46:21  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:46:21  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:46:21  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:46:21  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:46:21  EarlyBird.api.thesis.service 791: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_a63ba6624d57 [waitress-1]
2025-07-29 09:46:21  service.py 791: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_a63ba6624d57 [waitress-1]
2025-07-29 09:46:21  EarlyBird.api.thesis.service 833: INFO  找到支付记录 - 订单: thesis_download_a63ba6624d57, 状态: 0 [waitress-1]
2025-07-29 09:46:21  service.py 833: INFO  找到支付记录 - 订单: thesis_download_a63ba6624d57, 状态: 0 [waitress-1]
2025-07-29 09:46:24  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-3]
2025-07-29 09:46:24  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-3]
2025-07-29 09:46:24  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:46:24  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:46:24  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:46:24  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:46:24  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:46:24  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:46:24  EarlyBird.api.thesis.service 791: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_a63ba6624d57 [waitress-3]
2025-07-29 09:46:24  service.py 791: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_a63ba6624d57 [waitress-3]
2025-07-29 09:46:24  EarlyBird.api.thesis.service 833: INFO  找到支付记录 - 订单: thesis_download_a63ba6624d57, 状态: 0 [waitress-3]
2025-07-29 09:46:24  service.py 833: INFO  找到支付记录 - 订单: thesis_download_a63ba6624d57, 状态: 0 [waitress-3]
2025-07-29 09:46:27  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-2]
2025-07-29 09:46:27  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-2]
2025-07-29 09:46:27  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:46:27  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:46:27  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:46:27  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:46:27  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:46:27  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:46:27  EarlyBird.api.thesis.service 791: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_a63ba6624d57 [waitress-2]
2025-07-29 09:46:27  service.py 791: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_a63ba6624d57 [waitress-2]
2025-07-29 09:46:27  EarlyBird.api.thesis.service 833: INFO  找到支付记录 - 订单: thesis_download_a63ba6624d57, 状态: 0 [waitress-2]
2025-07-29 09:46:27  service.py 833: INFO  找到支付记录 - 订单: thesis_download_a63ba6624d57, 状态: 0 [waitress-2]
2025-07-29 09:46:30  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-0]
2025-07-29 09:46:30  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-0]
2025-07-29 09:46:30  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:46:30  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:46:30  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:46:30  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:46:30  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:46:30  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:46:30  EarlyBird.api.thesis.service 791: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_a63ba6624d57 [waitress-0]
2025-07-29 09:46:30  service.py 791: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_a63ba6624d57 [waitress-0]
2025-07-29 09:46:30  EarlyBird.api.thesis.service 833: INFO  找到支付记录 - 订单: thesis_download_a63ba6624d57, 状态: 0 [waitress-0]
2025-07-29 09:46:30  service.py 833: INFO  找到支付记录 - 订单: thesis_download_a63ba6624d57, 状态: 0 [waitress-0]
2025-07-29 09:46:33  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-1]
2025-07-29 09:46:33  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-1]
2025-07-29 09:46:33  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:46:33  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:46:33  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:46:33  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:46:33  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:46:33  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:46:33  EarlyBird.api.thesis.service 791: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_a63ba6624d57 [waitress-1]
2025-07-29 09:46:33  service.py 791: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_a63ba6624d57 [waitress-1]
2025-07-29 09:46:33  EarlyBird.api.thesis.service 833: INFO  找到支付记录 - 订单: thesis_download_a63ba6624d57, 状态: 0 [waitress-1]
2025-07-29 09:46:33  service.py 833: INFO  找到支付记录 - 订单: thesis_download_a63ba6624d57, 状态: 0 [waitress-1]
2025-07-29 09:46:36  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-3]
2025-07-29 09:46:36  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-3]
2025-07-29 09:46:36  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:46:36  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:46:36  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:46:36  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:46:36  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:46:36  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:46:36  EarlyBird.api.thesis.service 791: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_a63ba6624d57 [waitress-3]
2025-07-29 09:46:36  service.py 791: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_a63ba6624d57 [waitress-3]
2025-07-29 09:46:36  EarlyBird.api.thesis.service 833: INFO  找到支付记录 - 订单: thesis_download_a63ba6624d57, 状态: 0 [waitress-3]
2025-07-29 09:46:36  service.py 833: INFO  找到支付记录 - 订单: thesis_download_a63ba6624d57, 状态: 0 [waitress-3]
2025-07-29 09:46:39  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-2]
2025-07-29 09:46:39  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-2]
2025-07-29 09:46:39  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:46:39  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:46:39  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:46:39  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:46:39  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:46:39  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:46:39  EarlyBird.api.thesis.service 791: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_a63ba6624d57 [waitress-2]
2025-07-29 09:46:39  service.py 791: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_a63ba6624d57 [waitress-2]
2025-07-29 09:46:39  EarlyBird.api.thesis.service 833: INFO  找到支付记录 - 订单: thesis_download_a63ba6624d57, 状态: 0 [waitress-2]
2025-07-29 09:46:39  service.py 833: INFO  找到支付记录 - 订单: thesis_download_a63ba6624d57, 状态: 0 [waitress-2]
2025-07-29 09:46:42  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-0]
2025-07-29 09:46:42  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-0]
2025-07-29 09:46:42  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:46:42  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:46:42  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:46:42  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:46:42  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:46:42  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:46:42  EarlyBird.api.thesis.service 791: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_a63ba6624d57 [waitress-0]
2025-07-29 09:46:42  service.py 791: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_a63ba6624d57 [waitress-0]
2025-07-29 09:46:42  EarlyBird.api.thesis.service 833: INFO  找到支付记录 - 订单: thesis_download_a63ba6624d57, 状态: 0 [waitress-0]
2025-07-29 09:46:42  service.py 833: INFO  找到支付记录 - 订单: thesis_download_a63ba6624d57, 状态: 0 [waitress-0]
2025-07-29 09:46:45  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-1]
2025-07-29 09:46:45  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-1]
2025-07-29 09:46:45  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:46:45  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:46:45  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:46:45  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:46:45  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:46:45  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:46:45  EarlyBird.api.thesis.service 791: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_a63ba6624d57 [waitress-1]
2025-07-29 09:46:45  service.py 791: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_a63ba6624d57 [waitress-1]
2025-07-29 09:46:45  EarlyBird.api.thesis.service 833: INFO  找到支付记录 - 订单: thesis_download_a63ba6624d57, 状态: 0 [waitress-1]
2025-07-29 09:46:45  service.py 833: INFO  找到支付记录 - 订单: thesis_download_a63ba6624d57, 状态: 0 [waitress-1]
2025-07-29 09:46:48  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-3]
2025-07-29 09:46:48  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-3]
2025-07-29 09:46:48  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:46:48  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:46:48  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:46:48  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:46:48  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:46:48  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:46:48  EarlyBird.api.thesis.service 791: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_a63ba6624d57 [waitress-3]
2025-07-29 09:46:48  service.py 791: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_a63ba6624d57 [waitress-3]
2025-07-29 09:46:48  EarlyBird.api.thesis.service 833: INFO  找到支付记录 - 订单: thesis_download_a63ba6624d57, 状态: 0 [waitress-3]
2025-07-29 09:46:48  service.py 833: INFO  找到支付记录 - 订单: thesis_download_a63ba6624d57, 状态: 0 [waitress-3]
2025-07-29 09:46:51  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-2]
2025-07-29 09:46:51  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-2]
2025-07-29 09:46:51  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:46:51  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:46:51  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:46:51  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:46:51  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:46:51  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:46:51  EarlyBird.api.thesis.service 791: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_a63ba6624d57 [waitress-2]
2025-07-29 09:46:51  service.py 791: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_a63ba6624d57 [waitress-2]
2025-07-29 09:46:51  EarlyBird.api.thesis.service 833: INFO  找到支付记录 - 订单: thesis_download_a63ba6624d57, 状态: 0 [waitress-2]
2025-07-29 09:46:51  service.py 833: INFO  找到支付记录 - 订单: thesis_download_a63ba6624d57, 状态: 0 [waitress-2]
2025-07-29 09:46:54  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-0]
2025-07-29 09:46:54  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-0]
2025-07-29 09:46:54  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:46:54  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:46:54  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:46:54  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:46:54  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:46:54  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:46:54  EarlyBird.api.thesis.service 791: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_a63ba6624d57 [waitress-0]
2025-07-29 09:46:54  service.py 791: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_a63ba6624d57 [waitress-0]
2025-07-29 09:46:54  EarlyBird.api.thesis.service 833: INFO  找到支付记录 - 订单: thesis_download_a63ba6624d57, 状态: 0 [waitress-0]
2025-07-29 09:46:54  service.py 833: INFO  找到支付记录 - 订单: thesis_download_a63ba6624d57, 状态: 0 [waitress-0]
2025-07-29 09:46:57  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-1]
2025-07-29 09:46:57  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-1]
2025-07-29 09:46:57  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:46:57  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:46:57  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:46:57  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:46:57  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:46:57  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:46:57  EarlyBird.api.thesis.service 791: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_a63ba6624d57 [waitress-1]
2025-07-29 09:46:57  service.py 791: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_a63ba6624d57 [waitress-1]
2025-07-29 09:46:57  EarlyBird.api.thesis.service 833: INFO  找到支付记录 - 订单: thesis_download_a63ba6624d57, 状态: 0 [waitress-1]
2025-07-29 09:46:57  service.py 833: INFO  找到支付记录 - 订单: thesis_download_a63ba6624d57, 状态: 0 [waitress-1]
2025-07-29 09:47:00  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-3]
2025-07-29 09:47:00  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-3]
2025-07-29 09:47:00  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:47:00  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:47:00  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:47:00  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:47:00  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:47:00  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:47:00  EarlyBird.api.thesis.service 791: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_a63ba6624d57 [waitress-3]
2025-07-29 09:47:00  service.py 791: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_a63ba6624d57 [waitress-3]
2025-07-29 09:47:00  EarlyBird.api.thesis.service 833: INFO  找到支付记录 - 订单: thesis_download_a63ba6624d57, 状态: 0 [waitress-3]
2025-07-29 09:47:00  service.py 833: INFO  找到支付记录 - 订单: thesis_download_a63ba6624d57, 状态: 0 [waitress-3]
2025-07-29 09:47:03  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-2]
2025-07-29 09:47:03  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-2]
2025-07-29 09:47:03  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:47:03  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:47:03  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:47:03  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:47:03  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:47:03  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:47:03  EarlyBird.api.thesis.service 791: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_a63ba6624d57 [waitress-2]
2025-07-29 09:47:03  service.py 791: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_a63ba6624d57 [waitress-2]
2025-07-29 09:47:03  EarlyBird.api.thesis.service 833: INFO  找到支付记录 - 订单: thesis_download_a63ba6624d57, 状态: 0 [waitress-2]
2025-07-29 09:47:03  service.py 833: INFO  找到支付记录 - 订单: thesis_download_a63ba6624d57, 状态: 0 [waitress-2]
2025-07-29 09:47:06  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-0]
2025-07-29 09:47:06  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-0]
2025-07-29 09:47:06  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:47:06  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:47:06  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:47:06  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:47:06  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:47:06  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:47:06  EarlyBird.api.thesis.service 791: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_a63ba6624d57 [waitress-0]
2025-07-29 09:47:06  service.py 791: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_a63ba6624d57 [waitress-0]
2025-07-29 09:47:06  EarlyBird.api.thesis.service 833: INFO  找到支付记录 - 订单: thesis_download_a63ba6624d57, 状态: 0 [waitress-0]
2025-07-29 09:47:06  service.py 833: INFO  找到支付记录 - 订单: thesis_download_a63ba6624d57, 状态: 0 [waitress-0]
2025-07-29 09:47:54  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-1]
2025-07-29 09:47:54  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-1]
2025-07-29 09:47:54  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:47:54  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:47:54  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:47:54  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:47:54  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:47:54  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:47:54  EarlyBird.api.thesis.service 791: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_a63ba6624d57 [waitress-1]
2025-07-29 09:47:54  service.py 791: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_a63ba6624d57 [waitress-1]
2025-07-29 09:47:54  EarlyBird.api.thesis.service 833: INFO  找到支付记录 - 订单: thesis_download_a63ba6624d57, 状态: 0 [waitress-1]
2025-07-29 09:47:54  service.py 833: INFO  找到支付记录 - 订单: thesis_download_a63ba6624d57, 状态: 0 [waitress-1]
2025-07-29 09:48:54  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-3]
2025-07-29 09:48:54  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-3]
2025-07-29 09:48:54  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:48:54  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:48:54  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:48:54  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:48:54  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:48:54  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:48:54  EarlyBird.api.thesis.service 791: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_a63ba6624d57 [waitress-3]
2025-07-29 09:48:54  service.py 791: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_a63ba6624d57 [waitress-3]
2025-07-29 09:48:54  EarlyBird.api.thesis.service 833: INFO  找到支付记录 - 订单: thesis_download_a63ba6624d57, 状态: 0 [waitress-3]
2025-07-29 09:48:54  service.py 833: INFO  找到支付记录 - 订单: thesis_download_a63ba6624d57, 状态: 0 [waitress-3]
2025-07-29 09:49:54  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-2]
2025-07-29 09:49:54  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-2]
2025-07-29 09:49:54  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:49:54  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:49:54  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:49:54  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:49:54  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:49:54  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:49:54  EarlyBird.api.thesis.service 791: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_a63ba6624d57 [waitress-2]
2025-07-29 09:49:54  service.py 791: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_a63ba6624d57 [waitress-2]
2025-07-29 09:49:54  EarlyBird.api.thesis.service 833: INFO  找到支付记录 - 订单: thesis_download_a63ba6624d57, 状态: 0 [waitress-2]
2025-07-29 09:49:54  service.py 833: INFO  找到支付记录 - 订单: thesis_download_a63ba6624d57, 状态: 0 [waitress-2]
2025-07-29 09:50:54  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-0]
2025-07-29 09:50:54  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-0]
2025-07-29 09:50:54  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:50:54  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:50:54  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:50:54  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:50:54  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:50:54  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:50:54  EarlyBird.api.thesis.service 791: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_a63ba6624d57 [waitress-0]
2025-07-29 09:50:54  service.py 791: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_a63ba6624d57 [waitress-0]
2025-07-29 09:50:54  EarlyBird.api.thesis.service 833: INFO  找到支付记录 - 订单: thesis_download_a63ba6624d57, 状态: 0 [waitress-0]
2025-07-29 09:50:54  service.py 833: INFO  找到支付记录 - 订单: thesis_download_a63ba6624d57, 状态: 0 [waitress-0]
2025-07-29 09:51:56  init_flask.py 27: INFO  {'CpuCount': 6, 'Mem': '32 GB'} [MainThread]
2025-07-29 09:51:56  web_server.py 32: INFO  Static directory: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\resources\ui [MainThread]
2025-07-29 09:51:56  web_server.py 33: INFO  Static directory exists: True [MainThread]
2025-07-29 09:51:56  web_server.py 37: INFO  设置工作目录: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird [MainThread]
2025-07-29 09:51:56  EarlyBird.config.config 21: INFO  load configFile at d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\config/production.ini [MainThread]
2025-07-29 09:51:56  config.py 21: INFO  load configFile at d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\config/production.ini [MainThread]
2025-07-29 09:51:56  config.py 94: INFO  Database URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-29 09:51:58  EarlyBird.api.admin.app 39: INFO  ✅ 后台管理系统API已注册 [MainThread]
2025-07-29 09:51:58  app.py 39: INFO  ✅ 后台管理系统API已注册 [MainThread]
2025-07-29 09:51:58  EarlyBird 40: INFO  确保数据目录存在: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\resources\data [MainThread]
2025-07-29 09:51:58  server.py 40: INFO  确保数据目录存在: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\resources\data [MainThread]
2025-07-29 09:51:58  EarlyBird 44: INFO  开始执行数据库迁移... [MainThread]
2025-07-29 09:51:58  server.py 44: INFO  开始执行数据库迁移... [MainThread]
2025-07-29 09:51:58  config.py 21: INFO  load configFile at d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\config/production.ini [MainThread]
2025-07-29 09:51:58  config.py 94: INFO  Database URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-29 09:51:58  EarlyBird 53: INFO  数据库连接URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-29 09:51:58  server.py 53: INFO  数据库连接URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-29 09:51:58  EarlyBird 97: WARNING  ChatLog表不存在，将在应用启动时自动创建 [MainThread]
2025-07-29 09:51:58  server.py 97: WARNING  ChatLog表不存在，将在应用启动时自动创建 [MainThread]
2025-07-29 09:51:58  add_uid_to_chatlog.py 20: INFO  开始执行ChatLog表迁移... [MainThread]
2025-07-29 09:51:58  add_uid_to_chatlog.py 24: INFO  数据库连接URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-29 09:51:58  add_uid_to_chatlog.py 38: WARNING  ChatLog表不存在，可能需要先创建表 [MainThread]
2025-07-29 09:51:58  task_api_server.py 31: INFO  TaskApiServer start [MainThread]
2025-07-29 09:51:58  task_paper_generator.py 38: INFO  论文生成任务处理线程初始化 [MainThread]
2025-07-29 09:51:58  task_paper_generator.py 72: INFO  论文生成任务处理线程启动 [Thread-2]
2025-07-29 09:51:58  task_api_server.py 47: INFO  TaskApiServer listen on 127.0.0.1:3302 [Thread-1]
2025-07-29 09:51:58  task_api_server.py 17: INFO  _ApiServerThread start [Thread-3]
2025-07-29 09:52:30  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-0]
2025-07-29 09:52:30  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-0]
2025-07-29 09:52:30  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:52:30  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:52:30  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:52:30  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:52:30  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:52:30  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:52:30  EarlyBird.api.thesis.service 810: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_a63ba6624d57 [waitress-0]
2025-07-29 09:52:30  service.py 810: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_a63ba6624d57 [waitress-0]
2025-07-29 09:52:30  EarlyBird.api.thesis.service 852: INFO  找到支付记录 - 订单: thesis_download_a63ba6624d57, 状态: 0 [waitress-0]
2025-07-29 09:52:30  service.py 852: INFO  找到支付记录 - 订单: thesis_download_a63ba6624d57, 状态: 0 [waitress-0]
2025-07-29 09:52:34  web_server.py 73: INFO  Serving request for path: paper/content [waitress-1]
2025-07-29 09:52:34  web_server.py 85: INFO  Serving index.html for path: paper/content [waitress-1]
2025-07-29 09:52:34  web_server.py 73: INFO  Serving request for path: js/chunk-vendors.04aff60f.js [waitress-2]
2025-07-29 09:52:34  web_server.py 73: INFO  Serving request for path: js/app.4ad3be7b.js [waitress-3]
2025-07-29 09:52:34  web_server.py 82: INFO  Serving static file: js/chunk-vendors.04aff60f.js [waitress-2]
2025-07-29 09:52:34  web_server.py 73: INFO  Serving request for path: css/chunk-vendors.d5efd04f.css [waitress-0]
2025-07-29 09:52:34  web_server.py 73: INFO  Serving request for path: css/app.1e68ddae.css [waitress-1]
2025-07-29 09:52:34  web_server.py 82: INFO  Serving static file: js/app.4ad3be7b.js [waitress-3]
2025-07-29 09:52:34  web_server.py 82: INFO  Serving static file: css/chunk-vendors.d5efd04f.css [waitress-0]
2025-07-29 09:52:34  web_server.py 82: INFO  Serving static file: css/app.1e68ddae.css [waitress-1]
2025-07-29 09:52:34  web_server.py 73: INFO  Serving request for path: .well-known/appspecific/com.chrome.devtools.json [waitress-2]
2025-07-29 09:52:34  web_server.py 85: INFO  Serving index.html for path: .well-known/appspecific/com.chrome.devtools.json [waitress-2]
2025-07-29 09:52:34  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/user/info [waitress-1]
2025-07-29 09:52:34  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/user/info [waitress-1]
2025-07-29 09:52:34  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:52:34  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:52:34  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:52:34  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:52:34  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:52:34  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:52:34  web_server.py 73: INFO  Serving request for path: logo.png [waitress-3]
2025-07-29 09:52:34  web_server.py 82: INFO  Serving static file: logo.png [waitress-3]
2025-07-29 09:52:34  web_server.py 73: INFO  Serving request for path: js/22.683bc166.js [waitress-0]
2025-07-29 09:52:34  web_server.py 82: INFO  Serving static file: js/22.683bc166.js [waitress-0]
2025-07-29 09:52:34  web_server.py 73: INFO  Serving request for path: js/897.25207915.js [waitress-2]
2025-07-29 09:52:34  web_server.py 82: INFO  Serving static file: js/897.25207915.js [waitress-2]
2025-07-29 09:52:34  web_server.py 73: INFO  Serving request for path: css/389.ae47f7d6.css [waitress-1]
2025-07-29 09:52:34  web_server.py 73: INFO  Serving request for path: js/389.9dbcae13.js [waitress-3]
2025-07-29 09:52:34  web_server.py 82: INFO  Serving static file: css/389.ae47f7d6.css [waitress-1]
2025-07-29 09:52:34  web_server.py 82: INFO  Serving static file: js/389.9dbcae13.js [waitress-3]
2025-07-29 09:52:34  web_server.py 73: INFO  Serving request for path: img/logo.ce3a5bfd.png [waitress-0]
2025-07-29 09:52:34  web_server.py 82: INFO  Serving static file: img/logo.ce3a5bfd.png [waitress-0]
2025-07-29 09:52:34  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getList [waitress-1]
2025-07-29 09:52:34  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getList [waitress-1]
2025-07-29 09:52:34  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:52:34  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:52:34  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:52:34  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:52:34  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:52:34  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:52:34  web_server.py 73: INFO  Serving request for path: fonts/element-icons.ff18efd1.woff [waitress-3]
2025-07-29 09:52:34  web_server.py 82: INFO  Serving static file: fonts/element-icons.ff18efd1.woff [waitress-3]
2025-07-29 09:52:34  service.py 70: INFO  获取设置成功，包含 8 个配置项 [waitress-2]
2025-07-29 09:52:34  EarlyBird.api.thesis.apis 92: INFO  用户 14 请求获取论文列表 [waitress-1]
2025-07-29 09:52:34  apis.py 92: INFO  用户 14 请求获取论文列表 [waitress-1]
2025-07-29 09:52:34  EarlyBird.api.thesis.service 659: INFO  查询论文列表，用户ID: 14 [waitress-1]
2025-07-29 09:52:34  service.py 659: INFO  查询论文列表，用户ID: 14 [waitress-1]
2025-07-29 09:52:34  EarlyBird.api.thesis.service 665: INFO  查询到 1 篇论文 [waitress-1]
2025-07-29 09:52:34  service.py 665: INFO  查询到 1 篇论文 [waitress-1]
2025-07-29 09:52:34  EarlyBird.api.thesis.service 675: INFO  论文ID: 33, 标题: CES技术在提高电源效率中的应用研究, 用户ID: 14 [waitress-1]
2025-07-29 09:52:34  service.py 675: INFO  论文ID: 33, 标题: CES技术在提高电源效率中的应用研究, 用户ID: 14 [waitress-1]
2025-07-29 09:52:34  EarlyBird.api.thesis.service 677: INFO  返回论文列表，共 1 篇 [waitress-1]
2025-07-29 09:52:34  service.py 677: INFO  返回论文列表，共 1 篇 [waitress-1]
2025-07-29 09:52:34  EarlyBird.api.thesis.apis 95: INFO  用户 14 获取论文列表成功，共 1 篇 [waitress-1]
2025-07-29 09:52:34  apis.py 95: INFO  用户 14 获取论文列表成功，共 1 篇 [waitress-1]
2025-07-29 09:52:34  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getDetail [waitress-0]
2025-07-29 09:52:34  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getDetail [waitress-0]
2025-07-29 09:52:34  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:52:34  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:52:34  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:52:34  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:52:34  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:52:34  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:52:34  EarlyBird.api.talk2ai.utils 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-0]
2025-07-29 09:52:34  utils.py 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-0]
2025-07-29 09:52:34  EarlyBird.api.talk2ai.utils 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-0]
2025-07-29 09:52:34  utils.py 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-0]
2025-07-29 09:52:34  EarlyBird.api.talk2ai.utils 161: INFO  提纲处理完成，根节点ID: N2GpbeovHh2h35yBnccyB3 [waitress-0]
2025-07-29 09:52:34  utils.py 161: INFO  提纲处理完成，根节点ID: N2GpbeovHh2h35yBnccyB3 [waitress-0]
2025-07-29 09:52:34  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getOutline [waitress-3]
2025-07-29 09:52:34  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getOutline [waitress-3]
2025-07-29 09:52:34  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:52:34  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:52:34  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:52:34  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:52:34  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:52:34  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:52:34  EarlyBird.api.talk2ai.utils 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-3]
2025-07-29 09:52:34  utils.py 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-3]
2025-07-29 09:52:34  EarlyBird.api.talk2ai.utils 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-3]
2025-07-29 09:52:34  utils.py 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-3]
2025-07-29 09:52:34  EarlyBird.api.talk2ai.utils 161: INFO  提纲处理完成，根节点ID: N2GpbeovHh2h35yBnccyB3 [waitress-3]
2025-07-29 09:52:34  utils.py 161: INFO  提纲处理完成，根节点ID: N2GpbeovHh2h35yBnccyB3 [waitress-3]
2025-07-29 09:52:43  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/downloadThesis [waitress-2]
2025-07-29 09:52:43  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/downloadThesis [waitress-2]
2025-07-29 09:52:43  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:52:43  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:52:43  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:52:43  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:52:43  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:52:43  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:52:43  EarlyBird.api.thesis.apis 145: INFO  用户 14 请求下载论文 33 [waitress-2]
2025-07-29 09:52:43  apis.py 145: INFO  用户 14 请求下载论文 33 [waitress-2]
2025-07-29 09:52:43  EarlyBird.api.thesis.service 296: INFO  用户 14 请求导出论文 33 [waitress-2]
2025-07-29 09:52:43  service.py 296: INFO  用户 14 请求导出论文 33 [waitress-2]
2025-07-29 09:52:43  EarlyBird.api.thesis.service 306: INFO  用户 14 VIP状态: <bound method User.isVip of {"accessToken": null, "phone": null, "vip_expire_at": null, "refreshToken": null, "gender": 3, "balance": null, "update_time": "2025-07-29 09:28:33", "nickname": "\u7528\u6237_test1011", "email": null, "id": 14, "headimg": null, "is_lock": false, "invite_code": null, "is_deleted": 0, "activation_key": null, "last_login_time": "2025-07-29 09:28:32", "from_user_id": null, "status": 1, "username": "test1011", "last_login_ip": "127.0.0.1", "openPlatform": "wx", "password_hash": "pbkdf2:sha256:260000$3VYSebusaQuZ1fdD$9217323331b6562a816f82255c4826fd0d815e00bee146926900d409eee250fd", "vip_level": 1, "create_time": "2025-07-28 16:58:53", "openId": null, "realname": null, "vip_start_at": null}> [waitress-2]
2025-07-29 09:52:43  service.py 306: INFO  用户 14 VIP状态: <bound method User.isVip of {"accessToken": null, "phone": null, "vip_expire_at": null, "refreshToken": null, "gender": 3, "balance": null, "update_time": "2025-07-29 09:28:33", "nickname": "\u7528\u6237_test1011", "email": null, "id": 14, "headimg": null, "is_lock": false, "invite_code": null, "is_deleted": 0, "activation_key": null, "last_login_time": "2025-07-29 09:28:32", "from_user_id": null, "status": 1, "username": "test1011", "last_login_ip": "127.0.0.1", "openPlatform": "wx", "password_hash": "pbkdf2:sha256:260000$3VYSebusaQuZ1fdD$9217323331b6562a816f82255c4826fd0d815e00bee146926900d409eee250fd", "vip_level": 1, "create_time": "2025-07-28 16:58:53", "openId": null, "realname": null, "vip_start_at": null}> [waitress-2]
2025-07-29 09:52:43  EarlyBird.api.thesis.service 317: INFO  论文下载收费功能是否启用: True [waitress-2]
2025-07-29 09:52:43  service.py 317: INFO  论文下载收费功能是否启用: True [waitress-2]
2025-07-29 09:52:43  EarlyBird.api.thesis.service 339: INFO  VIP用户是否免费下载: False [waitress-2]
2025-07-29 09:52:43  service.py 339: INFO  VIP用户是否免费下载: False [waitress-2]
2025-07-29 09:52:43  EarlyBird.api.thesis.service 357: INFO  首次下载免费配置查询结果: {"id": 9, "name": "\u9996\u6b21\u4e0b\u8f7d\u662f\u5426\u514d\u8d39", "config_key": "thesis.download.first_free", "config_value": "false", "description": "\u7528\u6237\u9996\u6b21\u4e0b\u8f7d\u8bba\u6587\u662f\u5426\u514d\u8d39", "create_time": "2025-06-28 15:32:25"} [waitress-2]
2025-07-29 09:52:43  service.py 357: INFO  首次下载免费配置查询结果: {"id": 9, "name": "\u9996\u6b21\u4e0b\u8f7d\u662f\u5426\u514d\u8d39", "config_key": "thesis.download.first_free", "config_value": "false", "description": "\u7528\u6237\u9996\u6b21\u4e0b\u8f7d\u8bba\u6587\u662f\u5426\u514d\u8d39", "create_time": "2025-06-28 15:32:25"} [waitress-2]
2025-07-29 09:52:43  EarlyBird.api.thesis.service 359: INFO  配置值: 'false' (类型: <class 'str'>) [waitress-2]
2025-07-29 09:52:43  service.py 359: INFO  配置值: 'false' (类型: <class 'str'>) [waitress-2]
2025-07-29 09:52:43  EarlyBird.api.thesis.service 360: INFO  is_deleted: 0 [waitress-2]
2025-07-29 09:52:43  service.py 360: INFO  is_deleted: 0 [waitress-2]
2025-07-29 09:52:43  EarlyBird.api.thesis.service 367: INFO  配置值处理: 'false' -> 'false' -> False [waitress-2]
2025-07-29 09:52:43  service.py 367: INFO  配置值处理: 'false' -> 'false' -> False [waitress-2]
2025-07-29 09:52:43  EarlyBird.api.thesis.service 371: INFO  首次下载是否免费: False [waitress-2]
2025-07-29 09:52:43  service.py 371: INFO  首次下载是否免费: False [waitress-2]
2025-07-29 09:52:43  EarlyBird.api.thesis.service 376: INFO  用户 14 是否有任何下载记录: True [waitress-2]
2025-07-29 09:52:43  service.py 376: INFO  用户 14 是否有任何下载记录: True [waitress-2]
2025-07-29 09:52:43  EarlyBird.api.thesis.service 381: INFO  用户 14 共有 2 条下载记录 [waitress-2]
2025-07-29 09:52:43  service.py 381: INFO  用户 14 共有 2 条下载记录 [waitress-2]
2025-07-29 09:52:43  EarlyBird.api.thesis.service 393: INFO  用户 14 的下载记录都是今天的测试记录，视为首次下载 [waitress-2]
2025-07-29 09:52:43  service.py 393: INFO  用户 14 的下载记录都是今天的测试记录，视为首次下载 [waitress-2]
2025-07-29 09:52:43  EarlyBird.api.thesis.service 504: INFO  从配置中读取到论文下载价格: 10.0 [waitress-2]
2025-07-29 09:52:43  service.py 504: INFO  从配置中读取到论文下载价格: 10.0 [waitress-2]
2025-07-29 09:52:43  EarlyBird.api.thesis.service 512: INFO  用户 14 需要支付才能下载论文 33，价格: 10.0 [waitress-2]
2025-07-29 09:52:43  service.py 512: INFO  用户 14 需要支付才能下载论文 33，价格: 10.0 [waitress-2]
2025-07-29 09:52:43  EarlyBird.api.thesis.apis 151: INFO  用户 14 需要支付才能下载论文 33，价格: 10.0 [waitress-2]
2025-07-29 09:52:43  apis.py 151: INFO  用户 14 需要支付才能下载论文 33，价格: 10.0 [waitress-2]
2025-07-29 09:52:43  app.py 1454: ERROR  Exception on /api/thesis/downloadThesis [POST] [waitress-2]
Traceback (most recent call last):
  File "C:\Python311\Lib\site-packages\flask\app.py", line 2070, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\flask\app.py", line 1516, in full_dispatch_request
    return self.finalize_request(rv)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\flask\app.py", line 1535, in finalize_request
    response = self.make_response(rv)
               ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\flask\app.py", line 1713, in make_response
    rv = jsonify(rv)
         ^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\flask\json\__init__.py", line 348, in jsonify
    f"{dumps(data, indent=indent, separators=separators)}\n",
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\flask\json\__init__.py", line 129, in dumps
    rv = _json.dumps(obj, **kwargs)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\json\__init__.py", line 238, in dumps
    **kw).encode(obj)
          ^^^^^^^^^^^
  File "C:\Python311\Lib\json\encoder.py", line 200, in encode
    chunks = self.iterencode(o, _one_shot=True)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\json\encoder.py", line 258, in iterencode
    return _iterencode(o, 0)
           ^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\flask\json\__init__.py", line 56, in default
    return super().default(o)
           ^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\json\encoder.py", line 180, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type method is not JSON serializable
2025-07-29 09:56:24  init_flask.py 27: INFO  {'CpuCount': 6, 'Mem': '32 GB'} [MainThread]
2025-07-29 09:56:24  web_server.py 32: INFO  Static directory: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\resources\ui [MainThread]
2025-07-29 09:56:24  web_server.py 33: INFO  Static directory exists: True [MainThread]
2025-07-29 09:56:24  web_server.py 37: INFO  设置工作目录: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird [MainThread]
2025-07-29 09:56:24  EarlyBird.config.config 21: INFO  load configFile at d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\config/production.ini [MainThread]
2025-07-29 09:56:24  config.py 21: INFO  load configFile at d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\config/production.ini [MainThread]
2025-07-29 09:56:24  config.py 94: INFO  Database URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-29 09:56:27  EarlyBird.api.admin.app 39: INFO  ✅ 后台管理系统API已注册 [MainThread]
2025-07-29 09:56:27  app.py 39: INFO  ✅ 后台管理系统API已注册 [MainThread]
2025-07-29 09:56:27  EarlyBird 40: INFO  确保数据目录存在: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\resources\data [MainThread]
2025-07-29 09:56:27  server.py 40: INFO  确保数据目录存在: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\resources\data [MainThread]
2025-07-29 09:56:27  EarlyBird 44: INFO  开始执行数据库迁移... [MainThread]
2025-07-29 09:56:27  server.py 44: INFO  开始执行数据库迁移... [MainThread]
2025-07-29 09:56:27  config.py 21: INFO  load configFile at d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\config/production.ini [MainThread]
2025-07-29 09:56:27  config.py 94: INFO  Database URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-29 09:56:27  EarlyBird 53: INFO  数据库连接URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-29 09:56:27  server.py 53: INFO  数据库连接URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-29 09:56:27  EarlyBird 97: WARNING  ChatLog表不存在，将在应用启动时自动创建 [MainThread]
2025-07-29 09:56:27  server.py 97: WARNING  ChatLog表不存在，将在应用启动时自动创建 [MainThread]
2025-07-29 09:56:27  add_uid_to_chatlog.py 20: INFO  开始执行ChatLog表迁移... [MainThread]
2025-07-29 09:56:27  add_uid_to_chatlog.py 24: INFO  数据库连接URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-29 09:56:27  add_uid_to_chatlog.py 38: WARNING  ChatLog表不存在，可能需要先创建表 [MainThread]
2025-07-29 09:56:27  task_api_server.py 31: INFO  TaskApiServer start [MainThread]
2025-07-29 09:56:27  task_paper_generator.py 38: INFO  论文生成任务处理线程初始化 [MainThread]
2025-07-29 09:56:27  task_paper_generator.py 72: INFO  论文生成任务处理线程启动 [Thread-2]
2025-07-29 09:56:27  task_api_server.py 47: INFO  TaskApiServer listen on 127.0.0.1:3302 [Thread-1]
2025-07-29 09:56:27  task_api_server.py 17: INFO  _ApiServerThread start [Thread-3]
2025-07-29 09:56:36  web_server.py 73: INFO  Serving request for path: paper/content [waitress-0]
2025-07-29 09:56:36  web_server.py 85: INFO  Serving index.html for path: paper/content [waitress-0]
2025-07-29 09:56:36  web_server.py 73: INFO  Serving request for path: js/chunk-vendors.04aff60f.js [waitress-1]
2025-07-29 09:56:36  web_server.py 73: INFO  Serving request for path: .well-known/appspecific/com.chrome.devtools.json [waitress-2]
2025-07-29 09:56:36  web_server.py 82: INFO  Serving static file: js/chunk-vendors.04aff60f.js [waitress-1]
2025-07-29 09:56:36  web_server.py 85: INFO  Serving index.html for path: .well-known/appspecific/com.chrome.devtools.json [waitress-2]
2025-07-29 09:56:36  web_server.py 73: INFO  Serving request for path: css/chunk-vendors.d5efd04f.css [waitress-3]
2025-07-29 09:56:36  web_server.py 82: INFO  Serving static file: css/chunk-vendors.d5efd04f.css [waitress-3]
2025-07-29 09:56:36  web_server.py 73: INFO  Serving request for path: css/app.1e68ddae.css [waitress-0]
2025-07-29 09:56:36  web_server.py 73: INFO  Serving request for path: js/app.4ad3be7b.js [waitress-1]
2025-07-29 09:56:36  web_server.py 82: INFO  Serving static file: css/app.1e68ddae.css [waitress-0]
2025-07-29 09:56:36  web_server.py 82: INFO  Serving static file: js/app.4ad3be7b.js [waitress-1]
2025-07-29 09:56:36  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/user/info [waitress-2]
2025-07-29 09:56:36  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/user/info [waitress-2]
2025-07-29 09:56:36  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:56:36  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:56:36  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:56:36  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:56:36  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:56:36  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:56:36  web_server.py 73: INFO  Serving request for path: logo.png [waitress-3]
2025-07-29 09:56:36  web_server.py 82: INFO  Serving static file: logo.png [waitress-3]
2025-07-29 09:56:36  web_server.py 73: INFO  Serving request for path: js/22.683bc166.js [waitress-0]
2025-07-29 09:56:36  web_server.py 82: INFO  Serving static file: js/22.683bc166.js [waitress-0]
2025-07-29 09:56:36  web_server.py 73: INFO  Serving request for path: js/897.25207915.js [waitress-1]
2025-07-29 09:56:36  web_server.py 82: INFO  Serving static file: js/897.25207915.js [waitress-1]
2025-07-29 09:56:36  web_server.py 73: INFO  Serving request for path: css/389.ae47f7d6.css [waitress-3]
2025-07-29 09:56:36  web_server.py 73: INFO  Serving request for path: js/389.9dbcae13.js [waitress-2]
2025-07-29 09:56:36  web_server.py 82: INFO  Serving static file: js/389.9dbcae13.js [waitress-2]
2025-07-29 09:56:36  web_server.py 82: INFO  Serving static file: css/389.ae47f7d6.css [waitress-3]
2025-07-29 09:56:36  web_server.py 73: INFO  Serving request for path: img/logo.ce3a5bfd.png [waitress-0]
2025-07-29 09:56:36  web_server.py 82: INFO  Serving static file: img/logo.ce3a5bfd.png [waitress-0]
2025-07-29 09:56:36  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getList [waitress-2]
2025-07-29 09:56:36  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getList [waitress-2]
2025-07-29 09:56:36  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:56:36  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:56:36  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:56:36  web_server.py 73: INFO  Serving request for path: fonts/element-icons.ff18efd1.woff [waitress-3]
2025-07-29 09:56:36  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:56:36  service.py 70: INFO  获取设置成功，包含 8 个配置项 [waitress-1]
2025-07-29 09:56:36  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:56:36  web_server.py 82: INFO  Serving static file: fonts/element-icons.ff18efd1.woff [waitress-3]
2025-07-29 09:56:36  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:56:36  EarlyBird.api.thesis.apis 92: INFO  用户 14 请求获取论文列表 [waitress-2]
2025-07-29 09:56:36  apis.py 92: INFO  用户 14 请求获取论文列表 [waitress-2]
2025-07-29 09:56:36  EarlyBird.api.thesis.service 661: INFO  查询论文列表，用户ID: 14 [waitress-2]
2025-07-29 09:56:36  service.py 661: INFO  查询论文列表，用户ID: 14 [waitress-2]
2025-07-29 09:56:36  EarlyBird.api.thesis.service 667: INFO  查询到 1 篇论文 [waitress-2]
2025-07-29 09:56:36  service.py 667: INFO  查询到 1 篇论文 [waitress-2]
2025-07-29 09:56:36  EarlyBird.api.thesis.service 677: INFO  论文ID: 33, 标题: CES技术在提高电源效率中的应用研究, 用户ID: 14 [waitress-2]
2025-07-29 09:56:36  service.py 677: INFO  论文ID: 33, 标题: CES技术在提高电源效率中的应用研究, 用户ID: 14 [waitress-2]
2025-07-29 09:56:36  EarlyBird.api.thesis.service 679: INFO  返回论文列表，共 1 篇 [waitress-2]
2025-07-29 09:56:36  service.py 679: INFO  返回论文列表，共 1 篇 [waitress-2]
2025-07-29 09:56:36  EarlyBird.api.thesis.apis 95: INFO  用户 14 获取论文列表成功，共 1 篇 [waitress-2]
2025-07-29 09:56:36  apis.py 95: INFO  用户 14 获取论文列表成功，共 1 篇 [waitress-2]
2025-07-29 09:56:36  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getDetail [waitress-0]
2025-07-29 09:56:36  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getDetail [waitress-0]
2025-07-29 09:56:36  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:56:36  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:56:36  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:56:36  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:56:36  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:56:36  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:56:36  EarlyBird.api.talk2ai.utils 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-0]
2025-07-29 09:56:36  utils.py 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-0]
2025-07-29 09:56:36  EarlyBird.api.talk2ai.utils 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-0]
2025-07-29 09:56:36  utils.py 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-0]
2025-07-29 09:56:36  EarlyBird.api.talk2ai.utils 161: INFO  提纲处理完成，根节点ID: N2GpbeovHh2h35yBnccyB3 [waitress-0]
2025-07-29 09:56:36  utils.py 161: INFO  提纲处理完成，根节点ID: N2GpbeovHh2h35yBnccyB3 [waitress-0]
2025-07-29 09:56:36  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getOutline [waitress-1]
2025-07-29 09:56:36  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getOutline [waitress-1]
2025-07-29 09:56:36  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:56:36  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:56:36  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:56:36  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:56:36  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:56:36  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:56:36  EarlyBird.api.talk2ai.utils 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-1]
2025-07-29 09:56:36  utils.py 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-1]
2025-07-29 09:56:36  EarlyBird.api.talk2ai.utils 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-1]
2025-07-29 09:56:36  utils.py 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-1]
2025-07-29 09:56:36  EarlyBird.api.talk2ai.utils 161: INFO  提纲处理完成，根节点ID: N2GpbeovHh2h35yBnccyB3 [waitress-1]
2025-07-29 09:56:36  utils.py 161: INFO  提纲处理完成，根节点ID: N2GpbeovHh2h35yBnccyB3 [waitress-1]
2025-07-29 09:56:39  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/downloadThesis [waitress-3]
2025-07-29 09:56:39  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/downloadThesis [waitress-3]
2025-07-29 09:56:39  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:56:39  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:56:39  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:56:39  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:56:39  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:56:39  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:56:39  EarlyBird.api.thesis.apis 145: INFO  用户 14 请求下载论文 33 [waitress-3]
2025-07-29 09:56:39  apis.py 145: INFO  用户 14 请求下载论文 33 [waitress-3]
2025-07-29 09:56:39  EarlyBird.api.thesis.service 296: INFO  用户 14 请求导出论文 33 [waitress-3]
2025-07-29 09:56:39  service.py 296: INFO  用户 14 请求导出论文 33 [waitress-3]
2025-07-29 09:56:39  EarlyBird.api.thesis.service 306: INFO  用户 14 VIP状态: False [waitress-3]
2025-07-29 09:56:39  service.py 306: INFO  用户 14 VIP状态: False [waitress-3]
2025-07-29 09:56:39  EarlyBird.api.thesis.service 317: INFO  论文下载收费功能是否启用: True [waitress-3]
2025-07-29 09:56:39  service.py 317: INFO  论文下载收费功能是否启用: True [waitress-3]
2025-07-29 09:56:39  EarlyBird.api.thesis.service 339: INFO  VIP用户是否免费下载: False [waitress-3]
2025-07-29 09:56:39  service.py 339: INFO  VIP用户是否免费下载: False [waitress-3]
2025-07-29 09:56:39  EarlyBird.api.thesis.service 357: INFO  首次下载免费配置查询结果: {"id": 9, "name": "\u9996\u6b21\u4e0b\u8f7d\u662f\u5426\u514d\u8d39", "config_key": "thesis.download.first_free", "config_value": "false", "description": "\u7528\u6237\u9996\u6b21\u4e0b\u8f7d\u8bba\u6587\u662f\u5426\u514d\u8d39", "create_time": "2025-06-28 15:32:25"} [waitress-3]
2025-07-29 09:56:39  service.py 357: INFO  首次下载免费配置查询结果: {"id": 9, "name": "\u9996\u6b21\u4e0b\u8f7d\u662f\u5426\u514d\u8d39", "config_key": "thesis.download.first_free", "config_value": "false", "description": "\u7528\u6237\u9996\u6b21\u4e0b\u8f7d\u8bba\u6587\u662f\u5426\u514d\u8d39", "create_time": "2025-06-28 15:32:25"} [waitress-3]
2025-07-29 09:56:39  EarlyBird.api.thesis.service 359: INFO  配置值: 'false' (类型: <class 'str'>) [waitress-3]
2025-07-29 09:56:39  service.py 359: INFO  配置值: 'false' (类型: <class 'str'>) [waitress-3]
2025-07-29 09:56:39  EarlyBird.api.thesis.service 360: INFO  is_deleted: 0 [waitress-3]
2025-07-29 09:56:39  service.py 360: INFO  is_deleted: 0 [waitress-3]
2025-07-29 09:56:39  EarlyBird.api.thesis.service 367: INFO  配置值处理: 'false' -> 'false' -> False [waitress-3]
2025-07-29 09:56:39  service.py 367: INFO  配置值处理: 'false' -> 'false' -> False [waitress-3]
2025-07-29 09:56:39  EarlyBird.api.thesis.service 371: INFO  首次下载是否免费: False [waitress-3]
2025-07-29 09:56:39  service.py 371: INFO  首次下载是否免费: False [waitress-3]
2025-07-29 09:56:39  EarlyBird.api.thesis.service 376: INFO  用户 14 是否有任何下载记录: True [waitress-3]
2025-07-29 09:56:39  service.py 376: INFO  用户 14 是否有任何下载记录: True [waitress-3]
2025-07-29 09:56:39  EarlyBird.api.thesis.service 381: INFO  用户 14 共有 2 条下载记录 [waitress-3]
2025-07-29 09:56:39  service.py 381: INFO  用户 14 共有 2 条下载记录 [waitress-3]
2025-07-29 09:56:39  EarlyBird.api.thesis.service 393: INFO  用户 14 的下载记录都是今天的测试记录，视为首次下载 [waitress-3]
2025-07-29 09:56:39  service.py 393: INFO  用户 14 的下载记录都是今天的测试记录，视为首次下载 [waitress-3]
2025-07-29 09:56:39  EarlyBird.api.thesis.service 504: INFO  从配置中读取到论文下载价格: 10.0 [waitress-3]
2025-07-29 09:56:39  service.py 504: INFO  从配置中读取到论文下载价格: 10.0 [waitress-3]
2025-07-29 09:56:39  EarlyBird.api.thesis.service 512: INFO  用户 14 需要支付才能下载论文 33，价格: 10.0 [waitress-3]
2025-07-29 09:56:39  service.py 512: INFO  用户 14 需要支付才能下载论文 33，价格: 10.0 [waitress-3]
2025-07-29 09:56:39  EarlyBird.api.thesis.apis 151: INFO  用户 14 需要支付才能下载论文 33，价格: 10.0 [waitress-3]
2025-07-29 09:56:39  apis.py 151: INFO  用户 14 需要支付才能下载论文 33，价格: 10.0 [waitress-3]
2025-07-29 09:56:39  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/payDownload [waitress-2]
2025-07-29 09:56:39  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/payDownload [waitress-2]
2025-07-29 09:56:39  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:56:39  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:56:39  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:56:39  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:56:39  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:56:39  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:56:39  EarlyBird.api.thesis.service 790: INFO  创建支付记录成功 - 订单: thesis_download_8a50f1c7c2e8, 金额: 10.0 [waitress-2]
2025-07-29 09:56:39  service.py 790: INFO  创建支付记录成功 - 订单: thesis_download_8a50f1c7c2e8, 金额: 10.0 [waitress-2]
2025-07-29 09:56:42  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-0]
2025-07-29 09:56:42  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-0]
2025-07-29 09:56:42  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:56:42  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:56:42  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:56:42  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:56:42  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:56:42  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:56:42  EarlyBird.api.thesis.service 812: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_8a50f1c7c2e8 [waitress-0]
2025-07-29 09:56:42  service.py 812: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_8a50f1c7c2e8 [waitress-0]
2025-07-29 09:56:42  EarlyBird.api.thesis.service 854: INFO  找到支付记录 - 订单: thesis_download_8a50f1c7c2e8, 状态: 0 [waitress-0]
2025-07-29 09:56:42  service.py 854: INFO  找到支付记录 - 订单: thesis_download_8a50f1c7c2e8, 状态: 0 [waitress-0]
2025-07-29 09:56:42  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/downloadThesis [waitress-1]
2025-07-29 09:56:42  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/downloadThesis [waitress-1]
2025-07-29 09:56:42  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:56:42  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:56:42  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:56:42  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:56:42  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:56:42  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:56:42  EarlyBird.api.thesis.apis 145: INFO  用户 14 请求下载论文 33 [waitress-1]
2025-07-29 09:56:42  apis.py 145: INFO  用户 14 请求下载论文 33 [waitress-1]
2025-07-29 09:56:42  EarlyBird.api.thesis.service 296: INFO  用户 14 请求导出论文 33 [waitress-1]
2025-07-29 09:56:42  service.py 296: INFO  用户 14 请求导出论文 33 [waitress-1]
2025-07-29 09:56:42  EarlyBird.api.thesis.service 306: INFO  用户 14 VIP状态: False [waitress-1]
2025-07-29 09:56:42  service.py 306: INFO  用户 14 VIP状态: False [waitress-1]
2025-07-29 09:56:42  EarlyBird.api.thesis.service 317: INFO  论文下载收费功能是否启用: True [waitress-1]
2025-07-29 09:56:42  service.py 317: INFO  论文下载收费功能是否启用: True [waitress-1]
2025-07-29 09:56:42  EarlyBird.api.thesis.service 339: INFO  VIP用户是否免费下载: False [waitress-1]
2025-07-29 09:56:42  service.py 339: INFO  VIP用户是否免费下载: False [waitress-1]
2025-07-29 09:56:42  EarlyBird.api.thesis.service 357: INFO  首次下载免费配置查询结果: {"id": 9, "name": "\u9996\u6b21\u4e0b\u8f7d\u662f\u5426\u514d\u8d39", "config_key": "thesis.download.first_free", "config_value": "false", "description": "\u7528\u6237\u9996\u6b21\u4e0b\u8f7d\u8bba\u6587\u662f\u5426\u514d\u8d39", "create_time": "2025-06-28 15:32:25"} [waitress-1]
2025-07-29 09:56:42  service.py 357: INFO  首次下载免费配置查询结果: {"id": 9, "name": "\u9996\u6b21\u4e0b\u8f7d\u662f\u5426\u514d\u8d39", "config_key": "thesis.download.first_free", "config_value": "false", "description": "\u7528\u6237\u9996\u6b21\u4e0b\u8f7d\u8bba\u6587\u662f\u5426\u514d\u8d39", "create_time": "2025-06-28 15:32:25"} [waitress-1]
2025-07-29 09:56:42  EarlyBird.api.thesis.service 359: INFO  配置值: 'false' (类型: <class 'str'>) [waitress-1]
2025-07-29 09:56:42  service.py 359: INFO  配置值: 'false' (类型: <class 'str'>) [waitress-1]
2025-07-29 09:56:42  EarlyBird.api.thesis.service 360: INFO  is_deleted: 0 [waitress-1]
2025-07-29 09:56:42  service.py 360: INFO  is_deleted: 0 [waitress-1]
2025-07-29 09:56:42  EarlyBird.api.thesis.service 367: INFO  配置值处理: 'false' -> 'false' -> False [waitress-1]
2025-07-29 09:56:42  service.py 367: INFO  配置值处理: 'false' -> 'false' -> False [waitress-1]
2025-07-29 09:56:42  EarlyBird.api.thesis.service 371: INFO  首次下载是否免费: False [waitress-1]
2025-07-29 09:56:42  service.py 371: INFO  首次下载是否免费: False [waitress-1]
2025-07-29 09:56:42  EarlyBird.api.thesis.service 376: INFO  用户 14 是否有任何下载记录: True [waitress-1]
2025-07-29 09:56:42  service.py 376: INFO  用户 14 是否有任何下载记录: True [waitress-1]
2025-07-29 09:56:42  EarlyBird.api.thesis.service 381: INFO  用户 14 共有 2 条下载记录 [waitress-1]
2025-07-29 09:56:42  service.py 381: INFO  用户 14 共有 2 条下载记录 [waitress-1]
2025-07-29 09:56:42  EarlyBird.api.thesis.service 393: INFO  用户 14 的下载记录都是今天的测试记录，视为首次下载 [waitress-1]
2025-07-29 09:56:42  service.py 393: INFO  用户 14 的下载记录都是今天的测试记录，视为首次下载 [waitress-1]
2025-07-29 09:56:42  EarlyBird.api.thesis.service 504: INFO  从配置中读取到论文下载价格: 10.0 [waitress-1]
2025-07-29 09:56:42  service.py 504: INFO  从配置中读取到论文下载价格: 10.0 [waitress-1]
2025-07-29 09:56:42  EarlyBird.api.thesis.service 512: INFO  用户 14 需要支付才能下载论文 33，价格: 10.0 [waitress-1]
2025-07-29 09:56:42  service.py 512: INFO  用户 14 需要支付才能下载论文 33，价格: 10.0 [waitress-1]
2025-07-29 09:56:42  EarlyBird.api.thesis.apis 151: INFO  用户 14 需要支付才能下载论文 33，价格: 10.0 [waitress-1]
2025-07-29 09:56:42  apis.py 151: INFO  用户 14 需要支付才能下载论文 33，价格: 10.0 [waitress-1]
2025-07-29 09:56:42  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/payDownload [waitress-3]
2025-07-29 09:56:42  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/payDownload [waitress-3]
2025-07-29 09:56:42  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:56:42  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:56:42  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:56:42  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:56:42  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:56:42  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:56:43  EarlyBird.api.thesis.service 790: INFO  创建支付记录成功 - 订单: thesis_download_95e3eff217d1, 金额: 10.0 [waitress-3]
2025-07-29 09:56:43  service.py 790: INFO  创建支付记录成功 - 订单: thesis_download_95e3eff217d1, 金额: 10.0 [waitress-3]
2025-07-29 09:56:46  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-2]
2025-07-29 09:56:46  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-2]
2025-07-29 09:56:46  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:56:46  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:56:46  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:56:46  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:56:46  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:56:46  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:56:46  EarlyBird.api.thesis.service 812: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_95e3eff217d1 [waitress-2]
2025-07-29 09:56:46  service.py 812: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_95e3eff217d1 [waitress-2]
2025-07-29 09:56:46  EarlyBird.api.thesis.service 854: INFO  找到支付记录 - 订单: thesis_download_95e3eff217d1, 状态: 0 [waitress-2]
2025-07-29 09:56:46  service.py 854: INFO  找到支付记录 - 订单: thesis_download_95e3eff217d1, 状态: 0 [waitress-2]
2025-07-29 09:56:49  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-0]
2025-07-29 09:56:49  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-0]
2025-07-29 09:56:49  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:56:49  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:56:49  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:56:49  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:56:49  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:56:49  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:56:49  EarlyBird.api.thesis.service 812: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_95e3eff217d1 [waitress-0]
2025-07-29 09:56:49  service.py 812: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_95e3eff217d1 [waitress-0]
2025-07-29 09:56:49  EarlyBird.api.thesis.service 854: INFO  找到支付记录 - 订单: thesis_download_95e3eff217d1, 状态: 0 [waitress-0]
2025-07-29 09:56:49  service.py 854: INFO  找到支付记录 - 订单: thesis_download_95e3eff217d1, 状态: 0 [waitress-0]
2025-07-29 09:56:52  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-1]
2025-07-29 09:56:52  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-1]
2025-07-29 09:56:52  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:56:52  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:56:52  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:56:52  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:56:52  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:56:52  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:56:52  EarlyBird.api.thesis.service 812: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_95e3eff217d1 [waitress-1]
2025-07-29 09:56:52  service.py 812: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_95e3eff217d1 [waitress-1]
2025-07-29 09:56:52  EarlyBird.api.thesis.service 854: INFO  找到支付记录 - 订单: thesis_download_95e3eff217d1, 状态: 0 [waitress-1]
2025-07-29 09:56:52  service.py 854: INFO  找到支付记录 - 订单: thesis_download_95e3eff217d1, 状态: 0 [waitress-1]
2025-07-29 09:56:55  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-3]
2025-07-29 09:56:55  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-3]
2025-07-29 09:56:55  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:56:55  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:56:55  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:56:55  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:56:55  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:56:55  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:56:55  EarlyBird.api.thesis.service 812: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_95e3eff217d1 [waitress-3]
2025-07-29 09:56:55  service.py 812: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_95e3eff217d1 [waitress-3]
2025-07-29 09:56:55  EarlyBird.api.thesis.service 854: INFO  找到支付记录 - 订单: thesis_download_95e3eff217d1, 状态: 0 [waitress-3]
2025-07-29 09:56:55  service.py 854: INFO  找到支付记录 - 订单: thesis_download_95e3eff217d1, 状态: 0 [waitress-3]
2025-07-29 09:56:58  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-2]
2025-07-29 09:56:58  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-2]
2025-07-29 09:56:58  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:56:58  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:56:58  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:56:58  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:56:58  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:56:58  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:56:58  EarlyBird.api.thesis.service 812: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_95e3eff217d1 [waitress-2]
2025-07-29 09:56:58  service.py 812: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_95e3eff217d1 [waitress-2]
2025-07-29 09:56:58  EarlyBird.api.thesis.service 854: INFO  找到支付记录 - 订单: thesis_download_95e3eff217d1, 状态: 0 [waitress-2]
2025-07-29 09:56:58  service.py 854: INFO  找到支付记录 - 订单: thesis_download_95e3eff217d1, 状态: 0 [waitress-2]
2025-07-29 09:57:01  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-0]
2025-07-29 09:57:01  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-0]
2025-07-29 09:57:01  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:57:01  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:57:01  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:57:01  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:57:01  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:57:01  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:57:01  EarlyBird.api.thesis.service 812: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_95e3eff217d1 [waitress-0]
2025-07-29 09:57:01  service.py 812: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_95e3eff217d1 [waitress-0]
2025-07-29 09:57:01  EarlyBird.api.thesis.service 854: INFO  找到支付记录 - 订单: thesis_download_95e3eff217d1, 状态: 0 [waitress-0]
2025-07-29 09:57:01  service.py 854: INFO  找到支付记录 - 订单: thesis_download_95e3eff217d1, 状态: 0 [waitress-0]
2025-07-29 09:57:04  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-1]
2025-07-29 09:57:04  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-1]
2025-07-29 09:57:04  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:57:04  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:57:04  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:57:04  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:57:04  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:57:04  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:57:04  EarlyBird.api.thesis.service 812: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_95e3eff217d1 [waitress-1]
2025-07-29 09:57:04  service.py 812: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_95e3eff217d1 [waitress-1]
2025-07-29 09:57:04  EarlyBird.api.thesis.service 854: INFO  找到支付记录 - 订单: thesis_download_95e3eff217d1, 状态: 0 [waitress-1]
2025-07-29 09:57:04  service.py 854: INFO  找到支付记录 - 订单: thesis_download_95e3eff217d1, 状态: 0 [waitress-1]
2025-07-29 09:57:07  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-3]
2025-07-29 09:57:07  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-3]
2025-07-29 09:57:07  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:57:07  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:57:07  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:57:07  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:57:07  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:57:07  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:57:07  EarlyBird.api.thesis.service 812: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_95e3eff217d1 [waitress-3]
2025-07-29 09:57:07  service.py 812: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_95e3eff217d1 [waitress-3]
2025-07-29 09:57:07  EarlyBird.api.thesis.service 854: INFO  找到支付记录 - 订单: thesis_download_95e3eff217d1, 状态: 0 [waitress-3]
2025-07-29 09:57:07  service.py 854: INFO  找到支付记录 - 订单: thesis_download_95e3eff217d1, 状态: 0 [waitress-3]
2025-07-29 09:57:10  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-2]
2025-07-29 09:57:10  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-2]
2025-07-29 09:57:10  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:57:10  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:57:10  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:57:10  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:57:10  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:57:10  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:57:10  EarlyBird.api.thesis.service 812: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_95e3eff217d1 [waitress-2]
2025-07-29 09:57:10  service.py 812: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_95e3eff217d1 [waitress-2]
2025-07-29 09:57:10  EarlyBird.api.thesis.service 854: INFO  找到支付记录 - 订单: thesis_download_95e3eff217d1, 状态: 0 [waitress-2]
2025-07-29 09:57:10  service.py 854: INFO  找到支付记录 - 订单: thesis_download_95e3eff217d1, 状态: 0 [waitress-2]
2025-07-29 09:57:13  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-0]
2025-07-29 09:57:13  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-0]
2025-07-29 09:57:13  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:57:13  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:57:13  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:57:13  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:57:13  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:57:13  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:57:13  EarlyBird.api.thesis.service 812: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_95e3eff217d1 [waitress-0]
2025-07-29 09:57:13  service.py 812: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_95e3eff217d1 [waitress-0]
2025-07-29 09:57:13  EarlyBird.api.thesis.service 854: INFO  找到支付记录 - 订单: thesis_download_95e3eff217d1, 状态: 0 [waitress-0]
2025-07-29 09:57:13  service.py 854: INFO  找到支付记录 - 订单: thesis_download_95e3eff217d1, 状态: 0 [waitress-0]
2025-07-29 09:57:16  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-1]
2025-07-29 09:57:16  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-1]
2025-07-29 09:57:16  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:57:16  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:57:16  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:57:16  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:57:16  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:57:16  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:57:16  EarlyBird.api.thesis.service 812: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_95e3eff217d1 [waitress-1]
2025-07-29 09:57:16  service.py 812: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_95e3eff217d1 [waitress-1]
2025-07-29 09:57:16  EarlyBird.api.thesis.service 854: INFO  找到支付记录 - 订单: thesis_download_95e3eff217d1, 状态: 0 [waitress-1]
2025-07-29 09:57:16  service.py 854: INFO  找到支付记录 - 订单: thesis_download_95e3eff217d1, 状态: 0 [waitress-1]
2025-07-29 09:57:19  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-3]
2025-07-29 09:57:19  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-3]
2025-07-29 09:57:19  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:57:19  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:57:19  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:57:19  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:57:19  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:57:19  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:57:19  EarlyBird.api.thesis.service 812: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_95e3eff217d1 [waitress-3]
2025-07-29 09:57:19  service.py 812: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_95e3eff217d1 [waitress-3]
2025-07-29 09:57:19  EarlyBird.api.thesis.service 854: INFO  找到支付记录 - 订单: thesis_download_95e3eff217d1, 状态: 0 [waitress-3]
2025-07-29 09:57:19  service.py 854: INFO  找到支付记录 - 订单: thesis_download_95e3eff217d1, 状态: 0 [waitress-3]
2025-07-29 09:57:22  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-2]
2025-07-29 09:57:22  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-2]
2025-07-29 09:57:22  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:57:22  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:57:22  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:57:22  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:57:22  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:57:22  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:57:22  EarlyBird.api.thesis.service 812: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_95e3eff217d1 [waitress-2]
2025-07-29 09:57:22  service.py 812: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_95e3eff217d1 [waitress-2]
2025-07-29 09:57:22  EarlyBird.api.thesis.service 854: INFO  找到支付记录 - 订单: thesis_download_95e3eff217d1, 状态: 0 [waitress-2]
2025-07-29 09:57:22  service.py 854: INFO  找到支付记录 - 订单: thesis_download_95e3eff217d1, 状态: 0 [waitress-2]
2025-07-29 09:57:25  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-0]
2025-07-29 09:57:25  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-0]
2025-07-29 09:57:25  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:57:25  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:57:25  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:57:25  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:57:25  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:57:25  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:57:25  EarlyBird.api.thesis.service 812: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_95e3eff217d1 [waitress-0]
2025-07-29 09:57:25  service.py 812: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_95e3eff217d1 [waitress-0]
2025-07-29 09:57:25  EarlyBird.api.thesis.service 854: INFO  找到支付记录 - 订单: thesis_download_95e3eff217d1, 状态: 0 [waitress-0]
2025-07-29 09:57:25  service.py 854: INFO  找到支付记录 - 订单: thesis_download_95e3eff217d1, 状态: 0 [waitress-0]
2025-07-29 09:57:28  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-1]
2025-07-29 09:57:28  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-1]
2025-07-29 09:57:28  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:57:28  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:57:28  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:57:28  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:57:28  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:57:28  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:57:28  EarlyBird.api.thesis.service 812: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_95e3eff217d1 [waitress-1]
2025-07-29 09:57:28  service.py 812: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_95e3eff217d1 [waitress-1]
2025-07-29 09:57:28  EarlyBird.api.thesis.service 854: INFO  找到支付记录 - 订单: thesis_download_95e3eff217d1, 状态: 0 [waitress-1]
2025-07-29 09:57:28  service.py 854: INFO  找到支付记录 - 订单: thesis_download_95e3eff217d1, 状态: 0 [waitress-1]
2025-07-29 09:57:31  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-3]
2025-07-29 09:57:31  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-3]
2025-07-29 09:57:31  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:57:31  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:57:31  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:57:31  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:57:31  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:57:31  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:57:31  EarlyBird.api.thesis.service 812: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_95e3eff217d1 [waitress-3]
2025-07-29 09:57:31  service.py 812: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_95e3eff217d1 [waitress-3]
2025-07-29 09:57:31  EarlyBird.api.thesis.service 854: INFO  找到支付记录 - 订单: thesis_download_95e3eff217d1, 状态: 0 [waitress-3]
2025-07-29 09:57:31  service.py 854: INFO  找到支付记录 - 订单: thesis_download_95e3eff217d1, 状态: 0 [waitress-3]
2025-07-29 09:57:34  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-2]
2025-07-29 09:57:34  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-2]
2025-07-29 09:57:34  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:57:34  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:57:34  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:57:34  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:57:34  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:57:34  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:57:34  EarlyBird.api.thesis.service 812: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_95e3eff217d1 [waitress-2]
2025-07-29 09:57:34  service.py 812: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_95e3eff217d1 [waitress-2]
2025-07-29 09:57:34  EarlyBird.api.thesis.service 854: INFO  找到支付记录 - 订单: thesis_download_95e3eff217d1, 状态: 0 [waitress-2]
2025-07-29 09:57:34  service.py 854: INFO  找到支付记录 - 订单: thesis_download_95e3eff217d1, 状态: 0 [waitress-2]
2025-07-29 09:57:37  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-0]
2025-07-29 09:57:37  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-0]
2025-07-29 09:57:37  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:57:37  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:57:37  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:57:37  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:57:37  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:57:37  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:57:37  EarlyBird.api.thesis.service 812: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_95e3eff217d1 [waitress-0]
2025-07-29 09:57:37  service.py 812: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_95e3eff217d1 [waitress-0]
2025-07-29 09:57:37  EarlyBird.api.thesis.service 854: INFO  找到支付记录 - 订单: thesis_download_95e3eff217d1, 状态: 0 [waitress-0]
2025-07-29 09:57:37  service.py 854: INFO  找到支付记录 - 订单: thesis_download_95e3eff217d1, 状态: 0 [waitress-0]
2025-07-29 09:57:40  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-1]
2025-07-29 09:57:40  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-1]
2025-07-29 09:57:40  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:57:40  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:57:40  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:57:40  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:57:40  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:57:40  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:57:40  EarlyBird.api.thesis.service 812: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_95e3eff217d1 [waitress-1]
2025-07-29 09:57:40  service.py 812: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_95e3eff217d1 [waitress-1]
2025-07-29 09:57:40  EarlyBird.api.thesis.service 854: INFO  找到支付记录 - 订单: thesis_download_95e3eff217d1, 状态: 0 [waitress-1]
2025-07-29 09:57:40  service.py 854: INFO  找到支付记录 - 订单: thesis_download_95e3eff217d1, 状态: 0 [waitress-1]
2025-07-29 09:57:43  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-3]
2025-07-29 09:57:43  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-3]
2025-07-29 09:57:43  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:57:43  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:57:43  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:57:43  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:57:43  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:57:43  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:57:43  EarlyBird.api.thesis.service 812: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_95e3eff217d1 [waitress-3]
2025-07-29 09:57:43  service.py 812: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_95e3eff217d1 [waitress-3]
2025-07-29 09:57:43  EarlyBird.api.thesis.service 854: INFO  找到支付记录 - 订单: thesis_download_95e3eff217d1, 状态: 0 [waitress-3]
2025-07-29 09:57:43  service.py 854: INFO  找到支付记录 - 订单: thesis_download_95e3eff217d1, 状态: 0 [waitress-3]
2025-07-29 09:57:46  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-2]
2025-07-29 09:57:46  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-2]
2025-07-29 09:57:46  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:57:46  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:57:46  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:57:46  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:57:46  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:57:46  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:57:46  EarlyBird.api.thesis.service 812: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_95e3eff217d1 [waitress-2]
2025-07-29 09:57:46  service.py 812: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_95e3eff217d1 [waitress-2]
2025-07-29 09:57:46  EarlyBird.api.thesis.service 854: INFO  找到支付记录 - 订单: thesis_download_95e3eff217d1, 状态: 0 [waitress-2]
2025-07-29 09:57:46  service.py 854: INFO  找到支付记录 - 订单: thesis_download_95e3eff217d1, 状态: 0 [waitress-2]
2025-07-29 09:57:49  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-0]
2025-07-29 09:57:49  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-0]
2025-07-29 09:57:49  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:57:49  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:57:49  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:57:49  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:57:49  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:57:49  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:57:49  EarlyBird.api.thesis.service 812: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_95e3eff217d1 [waitress-0]
2025-07-29 09:57:49  service.py 812: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_95e3eff217d1 [waitress-0]
2025-07-29 09:57:49  EarlyBird.api.thesis.service 854: INFO  找到支付记录 - 订单: thesis_download_95e3eff217d1, 状态: 0 [waitress-0]
2025-07-29 09:57:49  service.py 854: INFO  找到支付记录 - 订单: thesis_download_95e3eff217d1, 状态: 0 [waitress-0]
2025-07-29 09:57:52  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-1]
2025-07-29 09:57:52  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-1]
2025-07-29 09:57:52  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:57:52  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-1]
2025-07-29 09:57:52  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:57:52  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-1]
2025-07-29 09:57:52  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:57:52  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-29 09:57:52  EarlyBird.api.thesis.service 812: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_95e3eff217d1 [waitress-1]
2025-07-29 09:57:52  service.py 812: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_95e3eff217d1 [waitress-1]
2025-07-29 09:57:52  EarlyBird.api.thesis.service 854: INFO  找到支付记录 - 订单: thesis_download_95e3eff217d1, 状态: 0 [waitress-1]
2025-07-29 09:57:52  service.py 854: INFO  找到支付记录 - 订单: thesis_download_95e3eff217d1, 状态: 0 [waitress-1]
2025-07-29 09:57:55  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-3]
2025-07-29 09:57:55  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-3]
2025-07-29 09:57:55  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:57:55  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-3]
2025-07-29 09:57:55  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:57:55  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-3]
2025-07-29 09:57:55  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:57:55  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-29 09:57:55  EarlyBird.api.thesis.service 812: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_95e3eff217d1 [waitress-3]
2025-07-29 09:57:55  service.py 812: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_95e3eff217d1 [waitress-3]
2025-07-29 09:57:55  EarlyBird.api.thesis.service 854: INFO  找到支付记录 - 订单: thesis_download_95e3eff217d1, 状态: 0 [waitress-3]
2025-07-29 09:57:55  service.py 854: INFO  找到支付记录 - 订单: thesis_download_95e3eff217d1, 状态: 0 [waitress-3]
2025-07-29 09:57:58  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-2]
2025-07-29 09:57:58  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-2]
2025-07-29 09:57:58  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:57:58  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-2]
2025-07-29 09:57:58  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:57:58  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-2]
2025-07-29 09:57:58  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:57:58  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-29 09:57:58  EarlyBird.api.thesis.service 812: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_95e3eff217d1 [waitress-2]
2025-07-29 09:57:58  service.py 812: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_95e3eff217d1 [waitress-2]
2025-07-29 09:57:58  EarlyBird.api.thesis.service 854: INFO  找到支付记录 - 订单: thesis_download_95e3eff217d1, 状态: 0 [waitress-2]
2025-07-29 09:57:58  service.py 854: INFO  找到支付记录 - 订单: thesis_download_95e3eff217d1, 状态: 0 [waitress-2]
2025-07-29 09:58:54  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-0]
2025-07-29 09:58:54  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-0]
2025-07-29 09:58:54  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:58:54  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-29T09:28:33.015415', 'user_id': 14, 'username': 'test1011'} [waitress-0]
2025-07-29 09:58:54  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:58:54  hook_register.py 131: INFO  🔍 提取的user_id: 14 [waitress-0]
2025-07-29 09:58:54  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:58:54  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-29 09:58:54  EarlyBird.api.thesis.service 812: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_95e3eff217d1 [waitress-0]
2025-07-29 09:58:54  service.py 812: INFO  查询支付状态 - 用户: 14, 论文: 33, 订单: thesis_download_95e3eff217d1 [waitress-0]
2025-07-29 09:58:54  EarlyBird.api.thesis.service 854: INFO  找到支付记录 - 订单: thesis_download_95e3eff217d1, 状态: 0 [waitress-0]
2025-07-29 09:58:54  service.py 854: INFO  找到支付记录 - 订单: thesis_download_95e3eff217d1, 状态: 0 [waitress-0]
2025-07-29 09:59:31  init_flask.py 27: INFO  {'CpuCount': 6, 'Mem': '32 GB'} [MainThread]
2025-07-29 09:59:31  web_server.py 32: INFO  Static directory: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\resources\ui [MainThread]
2025-07-29 09:59:31  web_server.py 33: INFO  Static directory exists: True [MainThread]
2025-07-29 09:59:31  web_server.py 37: INFO  设置工作目录: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird [MainThread]
2025-07-29 09:59:31  EarlyBird.config.config 21: INFO  load configFile at d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\config/production.ini [MainThread]
2025-07-29 09:59:31  config.py 21: INFO  load configFile at d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\config/production.ini [MainThread]
2025-07-29 09:59:31  config.py 94: INFO  Database URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
