#!/usr/bin/env python3
"""
初始化默认模型配置脚本
用于设置系统的默认AI模型配置
"""

import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def init_default_model():
    """初始化默认模型配置"""
    try:
        # 导入必要的模块
        from EarlyBird.web_server import create_custom_app
        from EarlyBird.model.setting import Setting
        from EarlyBird.ExtendRegister.db_register import db
        
        # 创建应用上下文
        app = create_custom_app()
        
        with app.app_context():
            print("🔧 开始初始化默认模型配置...")
            
            # 检查是否已存在模型配置
            existing_setting = Setting.query.filter_by(settingKey="modelName").first()
            
            if existing_setting:
                print(f"📋 当前模型配置: {existing_setting.settingValue}")
                
                # 如果是无效的配置，更新为有效配置
                if existing_setting.settingValue == "默认模型":
                    existing_setting.settingValue = "qianwen"
                    existing_setting.save()
                    print("✅ 已将无效的'默认模型'更新为'qianwen'")
                else:
                    print("✅ 模型配置已存在且有效")
            else:
                # 创建新的模型配置
                new_setting = Setting(
                    settingKey="modelName",
                    settingValue="qianwen",
                    description="系统默认AI模型"
                )
                new_setting.save()
                print("✅ 已创建默认模型配置: qianwen")
            
            # 验证配置
            current_setting = Setting.query.filter_by(settingKey="modelName").first()
            if current_setting:
                print(f"🎯 最终模型配置: {current_setting.settingValue}")
            
            print("🎉 默认模型配置初始化完成！")
            
    except Exception as e:
        print(f"❌ 初始化失败: {str(e)}")
        import traceback
        traceback.print_exc()

def check_model_config():
    """检查当前模型配置"""
    try:
        from EarlyBird.web_server import create_custom_app
        from EarlyBird.model.setting import Setting
        from EarlyBird.api.home.service import HomeService
        
        app = create_custom_app()
        
        with app.app_context():
            print("🔍 检查当前模型配置...")
            
            # 检查数据库中的配置
            setting = Setting.query.filter_by(settingKey="modelName").first()
            if setting:
                print(f"📋 数据库配置: {setting.settingValue}")
            else:
                print("❌ 数据库中无模型配置")
            
            # 检查HomeService返回的配置
            home_service = HomeService()
            model_name = home_service.getModelName()
            print(f"🏠 HomeService返回: {model_name}")
            
            # 检查AI适配器是否能正常获取
            try:
                from EarlyBird.common import ai
                adapter = ai.getAdapter(model_name)
                print(f"✅ AI适配器获取成功: {type(adapter).__name__}")
            except Exception as e:
                print(f"❌ AI适配器获取失败: {str(e)}")
                
    except Exception as e:
        print(f"❌ 检查失败: {str(e)}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("🔧 AI模型配置工具")
    print("=" * 50)
    
    # 检查当前配置
    check_model_config()
    
    print("\n" + "=" * 50)
    
    # 初始化默认配置
    init_default_model()
    
    print("\n" + "=" * 50)
    
    # 再次检查配置
    print("🔍 验证修复结果...")
    check_model_config()

if __name__ == "__main__":
    main()
