#!/usr/bin/env python3
"""
重置论文支付状态
用于测试支付功能
"""

import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置环境变量
os.environ["FLASK_ENV"] = "production"

def reset_thesis_payment(user_id, thesis_id):
    """重置指定论文的支付状态"""
    try:
        import pymysql
        from EarlyBird.config.config import AppConfig
        
        # 解析数据库连接信息
        db_uri = AppConfig.SQLALCHEMY_DATABASE_URI
        
        # 从URI中提取连接信息
        import re
        match = re.match(r'mysql\+pymysql://([^:]+):([^@]+)@([^:]+):(\d+)/([^?]+)', db_uri)
        if not match:
            print("❌ 无法解析数据库连接URI")
            return False
        
        username, password, host, port, database = match.groups()
        port = int(port)
        
        # 连接数据库
        connection = pymysql.connect(
            host=host,
            port=port,
            user=username,
            password=password,
            database=database,
            charset='utf8mb4'
        )
        
        cursor = connection.cursor()
        
        print(f"🔧 重置用户{user_id}论文{thesis_id}的支付状态...")
        
        # 1. 删除下载记录
        cursor.execute("""
            DELETE FROM earlybird_paper_thesis_download_record 
            WHERE uid = %s AND thesis_id = %s
        """, (user_id, thesis_id))
        deleted_download_count = cursor.rowcount
        
        # 2. 删除支付记录
        cursor.execute("""
            DELETE FROM earlybird_paper_payment 
            WHERE user_id = %s AND product_id = %s
        """, (user_id, f"thesis_download_{thesis_id}"))
        deleted_payment_count = cursor.rowcount
        
        # 提交更改
        connection.commit()
        
        print(f"✅ 已删除 {deleted_download_count} 条下载记录")
        print(f"✅ 已删除 {deleted_payment_count} 条支付记录")
        
        cursor.close()
        connection.close()
        
        print(f"\n🎉 论文{thesis_id}的支付状态已重置！")
        print("📋 现在用户再次下载时会要求支付")
        
        return True
        
    except Exception as e:
        print(f"❌ 重置失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 论文支付状态重置工具")
    print("=" * 50)
    
    # 重置用户14论文33的支付状态
    user_id = 14
    thesis_id = 33
    
    print(f"准备重置用户{user_id}论文{thesis_id}的支付状态")
    response = input("确认要重置吗？这将删除所有相关的支付和下载记录 (y/N): ")
    
    if response.lower() == 'y':
        if reset_thesis_payment(user_id, thesis_id):
            print("\n✅ 重置完成！现在可以测试支付功能了")
        else:
            print("\n❌ 重置失败")
    else:
        print("操作已取消")

if __name__ == "__main__":
    main()
