#!/usr/bin/env python3
"""
重新启用支付功能
修复支付流程，让用户能够正常支付和下载
"""

import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置环境变量
os.environ["FLASK_ENV"] = "production"

def enable_payment_function():
    """重新启用支付功能"""
    try:
        import pymysql
        from EarlyBird.config.config import AppConfig
        
        # 解析数据库连接信息
        db_uri = AppConfig.SQLALCHEMY_DATABASE_URI
        
        # 从URI中提取连接信息
        import re
        match = re.match(r'mysql\+pymysql://([^:]+):([^@]+)@([^:]+):(\d+)/([^?]+)', db_uri)
        if not match:
            print("❌ 无法解析数据库连接URI")
            return False
        
        username, password, host, port, database = match.groups()
        port = int(port)
        
        # 连接数据库
        connection = pymysql.connect(
            host=host,
            port=port,
            user=username,
            password=password,
            database=database,
            charset='utf8mb4'
        )
        
        cursor = connection.cursor()
        
        print("🔧 重新启用支付功能...")
        
        # 启用论文下载收费功能
        cursor.execute("""
            UPDATE earlybird_payment_config 
            SET config_value = 'true', update_time = NOW()
            WHERE config_key = 'thesis.download.is_active' AND is_deleted = 0
        """)
        
        if cursor.rowcount > 0:
            print("✅ 已启用论文下载收费功能")
        else:
            # 如果没有找到，创建一个
            cursor.execute("""
                INSERT INTO earlybird_payment_config 
                (config_key, config_value, name, description, create_time, update_time, is_deleted, status) 
                VALUES ('thesis.download.is_active', 'true', '论文下载收费开关', '是否启用论文下载收费功能', NOW(), NOW(), 0, 1)
                ON DUPLICATE KEY UPDATE 
                config_value = 'true', update_time = NOW()
            """)
            print("✅ 已创建并启用论文下载收费功能")
        
        # 设置合理的价格
        cursor.execute("""
            UPDATE earlybird_payment_config 
            SET config_value = '5.00', update_time = NOW()
            WHERE config_key = 'thesis.download.price' AND is_deleted = 0
        """)
        
        if cursor.rowcount > 0:
            print("✅ 已设置论文下载价格为 5.00 元")
        else:
            cursor.execute("""
                INSERT INTO earlybird_payment_config 
                (config_key, config_value, name, description, create_time, update_time, is_deleted, status) 
                VALUES ('thesis.download.price', '5.00', '论文下载收费金额', '论文下载的收费金额(元)', NOW(), NOW(), 0, 1)
                ON DUPLICATE KEY UPDATE 
                config_value = '5.00', update_time = NOW()
            """)
            print("✅ 已创建并设置论文下载价格为 5.00 元")
        
        # 启用首次下载免费
        cursor.execute("""
            UPDATE earlybird_payment_config 
            SET config_value = 'true', update_time = NOW()
            WHERE config_key = 'thesis.download.first_free' AND is_deleted = 0
        """)
        
        if cursor.rowcount > 0:
            print("✅ 已启用首次下载免费")
        else:
            cursor.execute("""
                INSERT INTO earlybird_payment_config 
                (config_key, config_value, name, description, create_time, update_time, is_deleted, status) 
                VALUES ('thesis.download.first_free', 'true', '首次下载是否免费', '用户首次下载论文是否免费', NOW(), NOW(), 0, 1)
                ON DUPLICATE KEY UPDATE 
                config_value = 'true', update_time = NOW()
            """)
            print("✅ 已创建并启用首次下载免费")
        
        # 提交更改
        connection.commit()
        
        # 验证配置
        print("\n🔍 验证配置结果:")
        cursor.execute("SELECT config_key, config_value, name FROM earlybird_payment_config WHERE config_key LIKE 'thesis.download.%' AND is_deleted = 0")
        configs = cursor.fetchall()
        
        for key, value, name in configs:
            status = "✅" if key == 'thesis.download.is_active' and value == 'true' else "📋"
            print(f"  {status} {name}: {key} = {value}")
        
        cursor.close()
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ 启用失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 重新启用支付功能")
    print("=" * 50)
    
    if enable_payment_function():
        print("\n🎉 支付功能已重新启用！")
        print("\n📋 当前配置:")
        print("  ✅ 论文下载收费: 启用")
        print("  💰 下载价格: 5.00 元")
        print("  🆓 首次下载: 免费")
        print("  🎖️ VIP用户: 免费")
        print("\n📝 下一步:")
        print("  1. 重启服务器")
        print("  2. 修复前端支付请求问题")
        print("  3. 测试完整的支付流程")
    else:
        print("\n❌ 支付功能启用失败")

if __name__ == "__main__":
    main()
