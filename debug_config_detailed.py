#!/usr/bin/env python3
"""
详细的配置调试脚本
检查为什么 PaymentConfig 查询返回错误的结果
"""

import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置环境变量
os.environ["FLASK_ENV"] = "production"

def debug_payment_config():
    """详细调试 PaymentConfig 查询"""
    try:
        from EarlyBird.web_server import create_custom_app
        from EarlyBird.model.payment import PaymentConfig
        from EarlyBird.model import TABLE_PREFIX
        
        app = create_custom_app()
        if not app:
            print("❌ 无法创建Flask应用")
            return False
        
        with app.app_context():
            print("🔍 详细调试 PaymentConfig 查询")
            print("=" * 60)
            
            # 1. 检查表前缀
            print(f"📋 TABLE_PREFIX: '{TABLE_PREFIX}'")
            print(f"📋 PaymentConfig.__tablename__: '{PaymentConfig.__tablename__}'")
            
            # 2. 检查数据库连接
            from EarlyBird.ExtendRegister.db_register import db
            print(f"📋 数据库引擎: {db.engine}")
            
            # 3. 执行原始SQL查询
            print(f"\n🔍 执行原始SQL查询:")
            from sqlalchemy import text
            sql = f"SELECT config_key, config_value, name, is_deleted FROM {PaymentConfig.__tablename__} WHERE config_key = 'thesis.download.first_free'"
            print(f"SQL: {sql}")

            result = db.session.execute(text(sql))
            rows = result.fetchall()

            print(f"原始SQL结果 ({len(rows)} 条):")
            for row in rows:
                print(f"  config_key: {row[0]}")
                print(f"  config_value: {row[1]}")
                print(f"  name: {row[2]}")
                print(f"  is_deleted: {row[3]}")
                print()
            
            # 4. 执行ORM查询（不带 is_deleted 条件）
            print(f"🔍 执行ORM查询（不带 is_deleted 条件）:")
            configs = PaymentConfig.query.filter_by(config_key='thesis.download.first_free').all()
            print(f"ORM查询结果 ({len(configs)} 条):")
            for config in configs:
                print(f"  id: {config.id}")
                print(f"  config_key: {config.config_key}")
                print(f"  config_value: {config.config_value}")
                print(f"  name: {config.name}")
                print(f"  is_deleted: {config.is_deleted}")
                print(f"  create_time: {config.create_time}")
                print(f"  update_time: {config.update_time}")
                print()
            
            # 5. 执行ORM查询（带 is_deleted=False 条件）
            print(f"🔍 执行ORM查询（带 is_deleted=False 条件）:")
            configs_filtered = PaymentConfig.query.filter_by(
                config_key='thesis.download.first_free', 
                is_deleted=False
            ).all()
            print(f"ORM过滤查询结果 ({len(configs_filtered)} 条):")
            for config in configs_filtered:
                print(f"  id: {config.id}")
                print(f"  config_key: {config.config_key}")
                print(f"  config_value: {config.config_value}")
                print(f"  name: {config.name}")
                print(f"  is_deleted: {config.is_deleted}")
                print()
            
            # 6. 模拟实际的查询逻辑
            print(f"🧪 模拟实际的查询逻辑:")
            first_free_config = PaymentConfig.query.filter_by(
                config_key='thesis.download.first_free', 
                is_deleted=False
            ).first()
            
            print(f"first_free_config: {first_free_config}")
            if first_free_config:
                print(f"  config_value: '{first_free_config.config_value}'")
                print(f"  config_value == 'true': {first_free_config.config_value == 'true'}")
                print(f"  config_value type: {type(first_free_config.config_value)}")
                print(f"  config_value repr: {repr(first_free_config.config_value)}")
                
                # 检查是否有隐藏字符
                if first_free_config.config_value:
                    value_bytes = first_free_config.config_value.encode('utf-8')
                    print(f"  config_value bytes: {value_bytes}")
                    print(f"  config_value stripped: '{first_free_config.config_value.strip()}'")
                    print(f"  stripped == 'true': {first_free_config.config_value.strip() == 'true'}")
            
            first_free = first_free_config and first_free_config.config_value == 'true'
            print(f"  first_free 最终结果: {first_free}")
            
            # 7. 检查所有论文下载相关配置
            print(f"\n📋 所有论文下载相关配置:")
            all_thesis_configs = PaymentConfig.query.filter(
                PaymentConfig.config_key.like('thesis.download.%')
            ).all()
            
            for config in all_thesis_configs:
                print(f"  {config.name}: {config.config_key} = '{config.config_value}' (is_deleted: {config.is_deleted})")
            
            return True
            
    except Exception as e:
        print(f"❌ 调试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def fix_config_value():
    """修复配置值"""
    try:
        from EarlyBird.web_server import create_custom_app
        from EarlyBird.model.payment import PaymentConfig
        
        app = create_custom_app()
        if not app:
            print("❌ 无法创建Flask应用")
            return False
        
        with app.app_context():
            from EarlyBird.ExtendRegister.db_register import db
            
            print("🔧 修复配置值...")
            
            # 查找配置
            config = PaymentConfig.query.filter_by(
                config_key='thesis.download.first_free'
            ).first()
            
            if config:
                print(f"找到配置: {config.config_key} = '{config.config_value}'")
                
                # 强制更新为 'true'
                config.config_value = 'true'
                config.is_deleted = False
                
                db.session.commit()
                print("✅ 配置已更新")
                
                # 验证更新
                updated_config = PaymentConfig.query.filter_by(
                    config_key='thesis.download.first_free',
                    is_deleted=False
                ).first()
                
                if updated_config:
                    print(f"验证结果: {updated_config.config_key} = '{updated_config.config_value}'")
                    print(f"验证判断: config_value == 'true' -> {updated_config.config_value == 'true'}")
                else:
                    print("❌ 验证失败：无法找到更新后的配置")
            else:
                print("❌ 未找到配置")
            
            return True
            
    except Exception as e:
        print(f"❌ 修复失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 PaymentConfig 详细调试工具")
    print("=" * 50)
    
    # 详细调试
    if debug_payment_config():
        print("\n" + "=" * 50)
        
        # 询问是否修复
        response = input("是否尝试修复配置值？(y/N): ")
        if response.lower() == 'y':
            if fix_config_value():
                print("\n🎉 配置修复完成！")
                print("📋 请重启服务器以确保修改生效")
            else:
                print("\n❌ 配置修复失败")

if __name__ == "__main__":
    main()
