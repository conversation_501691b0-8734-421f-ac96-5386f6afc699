#!/usr/bin/env python3
"""
测试文件下载修复
验证论文文档下载功能
"""

import os
import requests
import time

def test_download_url_generation():
    """测试下载URL生成"""
    print("🔍 测试下载URL生成...")
    
    # 模拟后端逻辑
    file_name = "[早鸟论文]_测试论文_V20250729_140248.docx"
    download_url = f"/resources/download/{file_name}"
    
    print(f"  📄 文件名: {file_name}")
    print(f"  🔗 下载URL: {download_url}")
    print(f"  ✅ URL格式正确")
    
    return download_url

def test_download_route():
    """测试下载路由"""
    print("\n🔍 测试下载路由...")
    
    # 创建测试文件
    test_dir = "resources/download"
    os.makedirs(test_dir, exist_ok=True)
    
    test_file = "test_thesis.docx"
    test_content = b"This is a test thesis document content"
    
    test_file_path = os.path.join(test_dir, test_file)
    with open(test_file_path, 'wb') as f:
        f.write(test_content)
    
    print(f"  📄 创建测试文件: {test_file_path}")
    
    # 测试下载URL
    download_url = f"http://127.0.0.1:3301/resources/download/{test_file}"
    
    try:
        response = requests.get(download_url, timeout=10)
        
        print(f"  🌐 请求URL: {download_url}")
        print(f"  📊 响应状态: {response.status_code}")
        print(f"  📋 响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            print(f"  ✅ 下载成功")
            print(f"  📦 内容长度: {len(response.content)} 字节")
            print(f"  📄 内容类型: {response.headers.get('Content-Type', 'N/A')}")
            
            # 验证内容
            if response.content == test_content:
                print(f"  ✅ 文件内容正确")
            else:
                print(f"  ❌ 文件内容不匹配")
                print(f"    期望: {test_content}")
                print(f"    实际: {response.content}")
        else:
            print(f"  ❌ 下载失败")
            print(f"  📄 响应内容: {response.text[:200]}...")
            
    except requests.exceptions.RequestException as e:
        print(f"  ❌ 请求失败: {str(e)}")
    
    # 清理测试文件
    try:
        os.remove(test_file_path)
        print(f"  🗑️ 清理测试文件: {test_file_path}")
    except:
        pass

def test_frontend_download_logic():
    """测试前端下载逻辑"""
    print("\n🔍 测试前端下载逻辑...")
    
    # 模拟前端接收到的API响应
    mock_api_response = {
        "is_success": True,
        "data": {
            "is_paid": True,
            "payment_status": "paid",
            "download_file": "/resources/download/[早鸟论文]_CES技术在提高电源效率中的应用研究_V20250729_140248.docx",
            "auto_download": True
        }
    }
    
    print(f"  📊 模拟API响应: {mock_api_response}")
    
    # 提取下载文件URL
    download_file = mock_api_response["data"]["download_file"]
    print(f"  🔗 下载文件URL: {download_file}")
    
    # 模拟前端triggerDownload逻辑
    if download_file:
        # 构建完整URL
        full_url = f"http://127.0.0.1:3301{download_file}"
        print(f"  🌐 完整下载URL: {full_url}")
        
        # 提取文件名
        filename = download_file.split('/')[-1]
        print(f"  📄 提取的文件名: {filename}")
        
        print(f"  ✅ 前端下载逻辑正确")
    else:
        print(f"  ❌ 没有下载文件URL")

def test_complete_flow():
    """测试完整流程"""
    print("\n🔍 测试完整下载流程...")
    
    # 1. 模拟支付状态查询API
    print("  1️⃣ 模拟支付状态查询...")
    
    try:
        # 这里应该是真实的API调用，但由于需要登录，我们模拟响应
        mock_response = {
            "code": 0,
            "is_success": True,
            "data": {
                "is_paid": True,
                "payment_status": "paid",
                "download_file": "/resources/download/test_thesis.docx",
                "auto_download": True
            }
        }
        
        print(f"    📊 API响应: {mock_response}")
        
        # 2. 提取下载URL
        if mock_response["is_success"] and mock_response["data"].get("download_file"):
            download_url = mock_response["data"]["download_file"]
            print(f"    🔗 下载URL: {download_url}")
            
            # 3. 构建完整URL并测试
            full_url = f"http://127.0.0.1:3301{download_url}"
            print(f"    🌐 完整URL: {full_url}")
            
            # 4. 测试下载（如果服务器运行中）
            try:
                response = requests.head(full_url, timeout=5)
                if response.status_code == 200:
                    print(f"    ✅ 下载URL可访问")
                elif response.status_code == 404:
                    print(f"    ⚠️ 文件不存在（正常，因为是测试文件）")
                else:
                    print(f"    ❌ 下载URL返回状态: {response.status_code}")
            except requests.exceptions.RequestException:
                print(f"    ⚠️ 服务器未运行或无法连接")
        else:
            print(f"    ❌ API响应中没有下载文件")
            
    except Exception as e:
        print(f"    ❌ 测试失败: {str(e)}")

def main():
    """主函数"""
    print("🔧 文件下载修复测试工具")
    print("=" * 50)
    
    # 1. 测试URL生成
    test_download_url_generation()
    
    # 2. 测试下载路由
    test_download_route()
    
    # 3. 测试前端逻辑
    test_frontend_download_logic()
    
    # 4. 测试完整流程
    test_complete_flow()
    
    print("\n" + "=" * 50)
    print("📋 修复总结:")
    print("  1. ✅ 后端返回完整下载URL路径")
    print("  2. ✅ 添加专门的下载文件路由")
    print("  3. ✅ 设置正确的Content-Type")
    print("  4. ✅ 前端接收正确的下载URL")
    
    print("\n🎯 预期效果:")
    print("  - 支付完成后，用户应该下载到实际的论文文档")
    print("  - 不再下载index.html文件")
    print("  - 文件名格式：[早鸟论文]_标题_V时间戳.docx")
    
    print("\n🔄 请重启服务器并测试真实的支付下载流程！")

if __name__ == "__main__":
    main()
