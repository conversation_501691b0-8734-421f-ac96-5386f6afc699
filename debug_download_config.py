#!/usr/bin/env python3
"""
论文下载配置调试工具
用于检查当前的配置状态和用户下载记录
"""

import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入Flask应用
from EarlyBird.app import app

from EarlyBird.ExtendRegister.db_register import db
from EarlyBird.model.payment import PaymentConfig, ThesisDownloadRecord
from EarlyBird.model.user import User
from EarlyBird.ExtendRegister.model_register import Thesis

def check_payment_config():
    """检查支付配置"""
    print("=== 论文下载支付配置检查 ===")
    
    configs = PaymentConfig.query.filter(
        PaymentConfig.config_key.like('thesis.download.%'),
        PaymentConfig.is_deleted == False
    ).all()
    
    if not configs:
        print("❌ 未找到任何论文下载配置")
        return
    
    for config in configs:
        print(f"📋 {config.name}: {config.config_key} = {config.config_value}")
    
    print()

def check_user_status(user_id):
    """检查用户状态"""
    print(f"=== 用户 {user_id} 状态检查 ===")
    
    user = User.query.get(user_id)
    if not user:
        print(f"❌ 用户 {user_id} 不存在")
        return
    
    print(f"👤 用户名: {user.username}")
    print(f"🎖️ VIP状态: {user.isVip()}")
    if user.vip_start_at and user.vip_expire_at:
        print(f"📅 VIP期限: {user.vip_start_at} ~ {user.vip_expire_at}")
    else:
        print("📅 VIP期限: 未设置")
    
    print()

def check_download_records(user_id):
    """检查用户下载记录"""
    print(f"=== 用户 {user_id} 下载记录检查 ===")
    
    records = ThesisDownloadRecord.query.filter_by(uid=user_id).all()
    
    if not records:
        print("📝 无下载记录")
        return
    
    print(f"📝 共有 {len(records)} 条下载记录:")
    for record in records:
        print(f"  - 论文ID: {record.thesis_id}")
        print(f"    已支付: {record.is_paid}")
        print(f"    价格: {record.price}")
        print(f"    支付方式: {record.payment_method}")
        print(f"    支付时间: {record.payment_time}")
        print(f"    创建时间: {record.create_time}")
        print()

def check_thesis_info(thesis_id):
    """检查论文信息"""
    print(f"=== 论文 {thesis_id} 信息检查 ===")
    
    thesis = Thesis.query.get(thesis_id)
    if not thesis:
        print(f"❌ 论文 {thesis_id} 不存在")
        return
    
    print(f"📄 论文标题: {thesis.title}")
    print(f"👤 作者ID: {thesis.uid}")
    print(f"📅 创建时间: {thesis.create_time}")
    print()

def simulate_download_logic(user_id, thesis_id):
    """模拟下载逻辑"""
    print(f"=== 模拟用户 {user_id} 下载论文 {thesis_id} 的逻辑 ===")
    
    # 1. 检查用户
    user = User.query.get(user_id)
    if not user:
        print("❌ 用户不存在")
        return
    
    # 2. 检查论文
    thesis = Thesis.query.get(thesis_id)
    if not thesis:
        print("❌ 论文不存在")
        return
    
    if thesis.uid != user_id:
        print("❌ 用户没有权限下载此论文")
        return
    
    # 3. 检查是否启用收费
    payment_enabled_config = PaymentConfig.query.filter_by(
        config_key='thesis.download.is_active', 
        is_deleted=False
    ).first()
    payment_enabled = payment_enabled_config and payment_enabled_config.config_value == 'true'
    print(f"💰 收费功能启用: {payment_enabled}")
    
    if not payment_enabled:
        print("✅ 收费功能未启用，可以免费下载")
        return
    
    # 4. 检查VIP状态
    is_vip = user.isVip()
    vip_free_config = PaymentConfig.query.filter_by(
        config_key='thesis.download.vip_free', 
        is_deleted=False
    ).first()
    vip_free = vip_free_config and vip_free_config.config_value == 'true'
    print(f"🎖️ 用户VIP状态: {is_vip}")
    print(f"🎖️ VIP免费下载: {vip_free}")
    
    if is_vip and vip_free:
        print("✅ VIP用户可以免费下载")
        return
    
    # 5. 检查首次下载
    first_free_config = PaymentConfig.query.filter_by(
        config_key='thesis.download.first_free', 
        is_deleted=False
    ).first()
    first_free = first_free_config and first_free_config.config_value == 'true'
    any_download_record = ThesisDownloadRecord.query.filter_by(uid=user_id).first()
    print(f"🆓 首次下载免费: {first_free}")
    print(f"📝 有下载记录: {any_download_record is not None}")
    
    if first_free and not any_download_record:
        print("✅ 首次下载免费")
        return
    
    # 6. 检查当前论文的下载记录
    download_record = ThesisDownloadRecord.query.filter_by(
        uid=user_id, 
        thesis_id=thesis_id
    ).first()
    
    if download_record and download_record.is_paid:
        if download_record.payment_method not in ['free_first_time', 'vip_free', 'free_disabled']:
            print("✅ 已支付过此论文，可以下载")
            return
        else:
            print("✅ 有免费下载记录，可以下载")
            return
    
    # 7. 需要支付
    price_config = PaymentConfig.query.filter_by(
        config_key='thesis.download.price', 
        is_deleted=False
    ).first()
    price = float(price_config.config_value) if price_config else 10.0
    print(f"💳 需要支付，价格: {price} 元")

def main():
    """主函数"""
    with app.app_context():
        print("🔧 论文下载配置调试工具")
        print("=" * 50)

        # 检查配置
        check_payment_config()

        # 检查用户12的状态
        user_id = 12
        check_user_status(user_id)
        check_download_records(user_id)

        # 检查论文28的信息
        thesis_id = 28
        check_thesis_info(thesis_id)

        # 模拟下载逻辑
        simulate_download_logic(user_id, thesis_id)

if __name__ == "__main__":
    main()
