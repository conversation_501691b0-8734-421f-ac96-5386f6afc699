<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>支付流程测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .section h3 {
            color: #666;
            margin-top: 0;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-weight: bold;
        }
        .status.success {
            background-color: #f0f9ff;
            border: 1px solid #67c23a;
            color: #67c23a;
        }
        .status.error {
            background-color: #fef0f0;
            border: 1px solid #f56c6c;
            color: #f56c6c;
        }
        .status.warning {
            background-color: #fdf6ec;
            border: 1px solid #e6a23c;
            color: #e6a23c;
        }
        .code-block {
            background-color: #f8f8f8;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            overflow-x: auto;
            max-height: 300px;
            overflow-y: auto;
        }
        button {
            background-color: #409EFF;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background-color: #66b1ff;
        }
        button.success {
            background-color: #67c23a;
        }
        button.success:hover {
            background-color: #85ce61;
        }
        button.danger {
            background-color: #F56C6C;
        }
        button.danger:hover {
            background-color: #f78989;
        }
        .input-group {
            margin: 10px 0;
        }
        .input-group label {
            display: inline-block;
            width: 100px;
            font-weight: bold;
        }
        .input-group input {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 300px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 支付流程测试工具</h1>
        
        <div class="section">
            <h3>✅ 修复完成状态</h3>
            <div class="status success">
                ✅ 支付功能已重新启用
            </div>
            <div class="status success">
                ✅ payDownload 方法已修复，现在会创建完整的支付记录
            </div>
            <div class="status success">
                ✅ 添加了模拟支付成功功能用于测试
            </div>
            <div class="status warning">
                ⚠️ 需要重启服务器以应用修改
            </div>
        </div>

        <div class="section">
            <h3>🧪 测试工具</h3>
            
            <div class="input-group">
                <label>订单号:</label>
                <input type="text" id="orderId" placeholder="例如: thesis_download_abc123" />
            </div>
            
            <button onclick="simulatePayment()">模拟支付成功</button>
            <button onclick="checkPaymentStatus()">查询支付状态</button>
            <button onclick="clearLog()">清空日志</button>
            
            <div id="test-result" class="status warning" style="margin-top: 15px;">
                等待测试操作...
            </div>
        </div>

        <div class="section">
            <h3>📝 测试步骤</h3>
            <ol>
                <li><strong>重启服务器</strong>：确保修改生效</li>
                <li><strong>测试下载流程</strong>：
                    <ul>
                        <li>登录用户账号</li>
                        <li>尝试下载论文</li>
                        <li>观察是否弹出支付窗口</li>
                        <li>记录生成的订单号</li>
                    </ul>
                </li>
                <li><strong>模拟支付成功</strong>：
                    <ul>
                        <li>将订单号输入上面的输入框</li>
                        <li>点击"模拟支付成功"按钮</li>
                        <li>再次尝试下载论文</li>
                    </ul>
                </li>
            </ol>
        </div>

        <div class="section">
            <h3>🎯 预期结果</h3>
            
            <h4>✅ 修复后应该看到：</h4>
            <div class="code-block">1. 首次下载（非首次用户）：
   - 弹出支付窗口
   - 显示二维码（模拟）
   - 生成订单号

2. 模拟支付成功后：
   - 支付状态更新为已支付
   - 再次下载时直接成功
   - 显示"已支付过此论文"</div>

            <h4>❌ 不应该再看到：</h4>
            <div class="code-block">- 请求被取消错误
- 支付订单不存在错误
- 未找到对应的支付记录</div>
        </div>

        <div class="section">
            <h3>📊 测试日志</h3>
            <div id="test-log" class="code-block">等待测试操作...\n</div>
        </div>

        <div class="section">
            <h3>🔧 关键修复点</h3>
            
            <h4>1. payDownload 方法修复：</h4>
            <div class="code-block"># 修复前：只创建下载记录
ThesisDownloadRecord(...)

# 修复后：同时创建支付记录
Payment(
    user_id=body.userId,
    product_id=f"thesis_download_{body.thesisId}",
    amount=price,
    out_trade_no=order_id,
    status=0  # 未支付状态
)</div>

            <h4>2. 支付状态同步：</h4>
            <div class="code-block"># 新增模拟支付成功功能
def simulatePaymentSuccess(order_id):
    payment.status = 1  # 已支付
    download_record.is_paid = True</div>
        </div>
    </div>

    <script>
        function log(message, type = 'info') {
            const logElement = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warning' ? '⚠️' : 'ℹ️';
            logElement.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function showResult(message, type) {
            const resultElement = document.getElementById('test-result');
            resultElement.textContent = message;
            resultElement.className = `status ${type}`;
        }

        function clearLog() {
            document.getElementById('test-log').textContent = '日志已清空...\n';
        }

        async function simulatePayment() {
            const orderId = document.getElementById('orderId').value.trim();
            
            if (!orderId) {
                log('请输入订单号', 'error');
                showResult('请输入订单号', 'error');
                return;
            }
            
            log(`开始模拟支付成功 - 订单: ${orderId}`, 'info');
            
            try {
                const response = await fetch('/api/thesis/simulatePayment', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        order_id: orderId
                    })
                });
                
                const result = await response.json();
                
                if (result.is_success) {
                    log(`✅ 模拟支付成功: ${JSON.stringify(result.data)}`, 'success');
                    showResult('模拟支付成功', 'success');
                } else {
                    log(`❌ 模拟支付失败: ${result.message}`, 'error');
                    showResult(`模拟支付失败: ${result.message}`, 'error');
                }
            } catch (error) {
                log(`❌ 请求失败: ${error.message}`, 'error');
                showResult(`请求失败: ${error.message}`, 'error');
            }
        }

        async function checkPaymentStatus() {
            const orderId = document.getElementById('orderId').value.trim();
            
            if (!orderId) {
                log('请输入订单号', 'error');
                showResult('请输入订单号', 'error');
                return;
            }
            
            log(`查询支付状态 - 订单: ${orderId}`, 'info');
            
            try {
                const response = await fetch('/api/thesis/paymentStatus', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        thesisId: 32, // 假设论文ID为32
                        orderId: orderId
                    })
                });
                
                const result = await response.json();
                
                if (result.is_success) {
                    log(`✅ 支付状态查询成功: ${JSON.stringify(result.data)}`, 'success');
                    showResult(`支付状态: ${result.data.payment_status}`, 'success');
                } else {
                    log(`❌ 支付状态查询失败: ${result.message}`, 'error');
                    showResult(`查询失败: ${result.message}`, 'error');
                }
            } catch (error) {
                log(`❌ 请求失败: ${error.message}`, 'error');
                showResult(`请求失败: ${error.message}`, 'error');
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('🔧 支付流程测试工具已加载', 'success');
            log('📋 请先重启服务器，然后进行测试', 'info');
        });
    </script>
</body>
</html>
