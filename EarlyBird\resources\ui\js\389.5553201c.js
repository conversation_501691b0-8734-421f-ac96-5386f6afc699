(self["webpackChunkopen_essay_gui"]=self["webpackChunkopen_essay_gui"]||[]).push([[4,8,12,318,344,345,389,503,586,632,739,773,795,917],{12:(t,e,s)=>{"use strict";s.r(e),s.d(e,{default:()=>h});var a=function(){var t=this,e=t._self._c;return e("div",{staticClass:"payment-config"},[e("div",{staticClass:"page-header"},[e("h2",[t._v("支付配置管理")]),e("div",{staticClass:"header-actions"},[e("el-button",{attrs:{type:"primary",icon:"el-icon-refresh"},on:{click:t.loadConfig}},[t._v("刷新配置")]),e("el-button",{attrs:{type:"success",loading:t.testing,icon:"el-icon-connection"},on:{click:t.testPayment}},[t._v(" 测试连接 ")])],1)]),e("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"config-container"},[e("el-card",{staticClass:"config-card"},[e("div",{attrs:{slot:"header"},slot:"header"},[e("span",[t._v("微信支付配置")])]),e("el-form",{ref:"configForm",attrs:{model:t.configForm,"label-width":"140px"}},[e("el-row",{attrs:{gutter:20}},[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"应用ID",prop:"app_id"}},[e("el-input",{attrs:{placeholder:"请输入微信AppID"},model:{value:t.configForm.app_id,callback:function(e){t.$set(t.configForm,"app_id",e)},expression:"configForm.app_id"}})],1)],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"商户号",prop:"mch_id"}},[e("el-input",{attrs:{placeholder:"请输入微信商户号"},model:{value:t.configForm.mch_id,callback:function(e){t.$set(t.configForm,"mch_id",e)},expression:"configForm.mch_id"}})],1)],1)],1),e("el-row",{attrs:{gutter:20}},[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"API V3密钥",prop:"api_v3_key"}},[e("el-input",{attrs:{type:"password",placeholder:"请输入API V3密钥","show-password":""},model:{value:t.configForm.api_v3_key,callback:function(e){t.$set(t.configForm,"api_v3_key",e)},expression:"configForm.api_v3_key"}})],1)],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"证书序列号",prop:"serial_no"}},[e("el-input",{attrs:{placeholder:"请输入证书序列号"},model:{value:t.configForm.serial_no,callback:function(e){t.$set(t.configForm,"serial_no",e)},expression:"configForm.serial_no"}})],1)],1)],1),e("el-row",{attrs:{gutter:20}},[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"私钥文件路径",prop:"private_key_path"}},[e("el-input",{attrs:{placeholder:"请输入私钥文件路径"},model:{value:t.configForm.private_key_path,callback:function(e){t.$set(t.configForm,"private_key_path",e)},expression:"configForm.private_key_path"}})],1)],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"回调通知地址",prop:"notify_url"}},[e("el-input",{attrs:{placeholder:"请输入回调通知地址"},model:{value:t.configForm.notify_url,callback:function(e){t.$set(t.configForm,"notify_url",e)},expression:"configForm.notify_url"}})],1)],1)],1),e("el-row",{attrs:{gutter:20}},[e("el-col",{attrs:{span:24}},[e("el-form-item",{attrs:{label:"备注"}},[e("el-input",{attrs:{type:"textarea",placeholder:"请输入配置备注",rows:3},model:{value:t.configForm.remark,callback:function(e){t.$set(t.configForm,"remark",e)},expression:"configForm.remark"}})],1)],1)],1),e("el-form-item",[e("el-button",{attrs:{type:"primary",loading:t.saving},on:{click:t.saveConfig}},[t._v("保存配置")]),e("el-button",{on:{click:t.resetForm}},[t._v("重置")])],1)],1)],1)],1),e("el-dialog",{attrs:{title:"支付测试结果",visible:t.testDialogVisible,width:"600px","close-on-click-modal":!1},on:{"update:visible":function(e){t.testDialogVisible=e}}},[t.testResult.success?e("div",[e("el-alert",{attrs:{type:"success",title:t.testResult.message,closable:!1,"show-icon":""}}),t.testResult.data&&t.testResult.data.qr_code_url?e("div",{staticClass:"qr-code-container"},[e("h4",[t._v("测试支付二维码 (0.01元)")]),e("div",{staticClass:"qr-code-wrapper"},[e("img",{staticClass:"qr-code",attrs:{src:t.testResult.data.qr_code_url,alt:"支付二维码"}})]),e("p",{staticClass:"order-info"},[t._v("订单号: "+t._s(t.testResult.data.out_trade_no))]),t.testResult.data.is_mock?e("div",{staticClass:"mock-warning"},[e("el-alert",{attrs:{type:"warning",title:"模拟测试模式",description:"当前为模拟测试，无法进行真实支付。请配置正确的证书文件后重试。",closable:!1,"show-icon":""}}),t.testResult.data.message?e("div",{staticStyle:{"margin-top":"10px"}},[e("p",[e("strong",[t._v("错误信息:")]),t._v(" "+t._s(t.testResult.data.message))])]):t._e(),t.testResult.data.error_details?e("div",{staticStyle:{"margin-top":"5px"}},[e("p",[e("strong",[t._v("详细错误:")]),t._v(" "+t._s(t.testResult.data.error_details))])]):t._e()],1):e("div",{staticClass:"real-payment-info"},[e("el-alert",{attrs:{type:"success",title:"真实支付模式",description:"这是真实的微信支付二维码，扫码后会产生0.01元的实际支付。",closable:!1,"show-icon":""}})],1)]):t._e()],1):e("div",[e("el-alert",{attrs:{type:"error",title:t.testResult.message,closable:!1,"show-icon":""}}),t.testResult.details?e("p",{staticClass:"error-details"},[t._v(t._s(t.testResult.details))]):t._e()],1),e("div",{attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.testDialogVisible=!1}}},[t._v("关闭")]),t.testResult.success&&t.testResult.data?e("el-button",{attrs:{type:"primary"},on:{click:t.checkPaymentStatus}},[t._v(" 检查支付状态 ")]):t._e()],1)])],1)},i=[],r=s(5597),l=s(5545);const n={name:"PaymentConfigSimple",data(){return{loading:!1,saving:!1,testing:!1,testDialogVisible:!1,configForm:{app_id:"",mch_id:"",api_v3_key:"",serial_no:"",private_key_path:"",notify_url:"",is_enabled:!0,is_test_mode:!1,remark:""},testResult:{success:!1,message:"",data:null,details:""}}},created(){this.loadConfig()},methods:{async loadConfig(){try{this.loading=!0;const t=await(0,r.jp)();if(t.success&&t.data){const e=t.data;this.configForm={...this.configForm,app_id:e.appid||e.app_id||"",mch_id:e.mchid||e.mch_id||"",api_v3_key:e.api_v3_key||"",serial_no:e.serial_no||"",private_key_path:e.private_key_path||"",notify_url:e.notify_url||"",is_enabled:void 0===e.is_enabled||e.is_enabled,is_test_mode:void 0!==e.is_test_mode&&e.is_test_mode,remark:e.remark||""},this.$message.success("配置加载成功")}else this.$message.error(t.message||"加载配置失败")}catch(t){console.error("加载配置失败:",t),this.$message.error("加载配置失败: "+(t.message||"未知错误"))}finally{this.loading=!1}},async saveConfig(){try{this.saving=!0;const t=await(0,r.YN)(this.configForm);t.success?(this.$message.success("配置保存成功"),this.loadConfig()):this.$message.error(t.message||"保存配置失败")}catch(t){console.error("保存配置失败:",t),this.$message.error("保存配置失败: "+(t.message||"未知错误"))}finally{this.saving=!1}},resetForm(){this.$refs.configForm.resetFields()},async testPayment(){try{this.testing=!0;const t=await(0,l["default"])({url:"/api/pay/test",method:"post",data:{pay_type:"wxpay",amount:.01}});this.testResult={success:t.success,message:t.message||(t.success?"支付测试成功":"支付测试失败"),data:t.data,details:t.details||""},this.testDialogVisible=!0}catch(t){console.error("测试支付失败:",t),this.testResult={success:!1,message:"测试支付失败",data:null,details:t.message||"网络错误"},this.testDialogVisible=!0}finally{this.testing=!1}},async checkPaymentStatus(){if(this.testResult.data&&this.testResult.data.out_trade_no)try{const t=await(0,l["default"])({url:"/api/pay/query",method:"post",data:{out_trade_no:this.testResult.data.out_trade_no}});t.success?this.$message.success("订单状态: "+(t.data.status||"未知")):this.$message.error("查询失败: "+t.message)}catch(t){console.error("查询支付状态失败:",t),this.$message.error("查询失败: "+t.message)}else this.$message.warning("没有可查询的订单")}}},o=n;var c=s(1656),d=(0,c.A)(o,a,i,!1,null,"e927ecaa",null);const h=d.exports},23:(t,e,s)=>{"use strict";function a(){return{async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null}}s.d(e,{xI:()=>It});var i=a();function r(t){i=t}var l={exec:()=>null};function n(t,e=""){let s="string"===typeof t?t:t.source;const a={replace:(t,e)=>{let i="string"===typeof e?e:e.source;return i=i.replace(o.caret,"$1"),s=s.replace(t,i),a},getRegex:()=>new RegExp(s,e)};return a}var o={codeRemoveIndent:/^(?: {1,4}| {0,3}\t)/gm,outputLinkReplace:/\\([\[\]])/g,indentCodeCompensation:/^(\s+)(?:```)/,beginningSpace:/^\s+/,endingHash:/#$/,startingSpaceChar:/^ /,endingSpaceChar:/ $/,nonSpaceChar:/[^ ]/,newLineCharGlobal:/\n/g,tabCharGlobal:/\t/g,multipleSpaceGlobal:/\s+/g,blankLine:/^[ \t]*$/,doubleBlankLine:/\n[ \t]*\n[ \t]*$/,blockquoteStart:/^ {0,3}>/,blockquoteSetextReplace:/\n {0,3}((?:=+|-+) *)(?=\n|$)/g,blockquoteSetextReplace2:/^ {0,3}>[ \t]?/gm,listReplaceTabs:/^\t+/,listReplaceNesting:/^ {1,4}(?=( {4})*[^ ])/g,listIsTask:/^\[[ xX]\] /,listReplaceTask:/^\[[ xX]\] +/,anyLine:/\n.*\n/,hrefBrackets:/^<(.*)>$/,tableDelimiter:/[:|]/,tableAlignChars:/^\||\| *$/g,tableRowBlankLine:/\n[ \t]*$/,tableAlignRight:/^ *-+: *$/,tableAlignCenter:/^ *:-+: *$/,tableAlignLeft:/^ *:-+ *$/,startATag:/^<a /i,endATag:/^<\/a>/i,startPreScriptTag:/^<(pre|code|kbd|script)(\s|>)/i,endPreScriptTag:/^<\/(pre|code|kbd|script)(\s|>)/i,startAngleBracket:/^</,endAngleBracket:/>$/,pedanticHrefTitle:/^([^'"]*[^\s])\s+(['"])(.*)\2/,unicodeAlphaNumeric:/[\p{L}\p{N}]/u,escapeTest:/[&<>"']/,escapeReplace:/[&<>"']/g,escapeTestNoEncode:/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/,escapeReplaceNoEncode:/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/g,unescapeTest:/&(#(?:\d+)|(?:#x[0-9A-Fa-f]+)|(?:\w+));?/gi,caret:/(^|[^\[])\^/g,percentDecode:/%25/g,findPipe:/\|/g,splitPipe:/ \|/,slashPipe:/\\\|/g,carriageReturn:/\r\n|\r/g,spaceLine:/^ +$/gm,notSpaceStart:/^\S*/,endingNewline:/\n$/,listItemRegex:t=>new RegExp(`^( {0,3}${t})((?:[\t ][^\\n]*)?(?:\\n|$))`),nextBulletRegex:t=>new RegExp(`^ {0,${Math.min(3,t-1)}}(?:[*+-]|\\d{1,9}[.)])((?:[ \t][^\\n]*)?(?:\\n|$))`),hrRegex:t=>new RegExp(`^ {0,${Math.min(3,t-1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$)`),fencesBeginRegex:t=>new RegExp(`^ {0,${Math.min(3,t-1)}}(?:\`\`\`|~~~)`),headingBeginRegex:t=>new RegExp(`^ {0,${Math.min(3,t-1)}}#`),htmlBeginRegex:t=>new RegExp(`^ {0,${Math.min(3,t-1)}}<(?:[a-z].*>|!--)`,"i")},c=/^(?:[ \t]*(?:\n|$))+/,d=/^((?: {4}| {0,3}\t)[^\n]+(?:\n(?:[ \t]*(?:\n|$))*)?)+/,h=/^ {0,3}(`{3,}(?=[^`\n]*(?:\n|$))|~{3,})([^\n]*)(?:\n|$)(?:|([\s\S]*?)(?:\n|$))(?: {0,3}\1[~`]* *(?=\n|$)|$)/,p=/^ {0,3}((?:-[\t ]*){3,}|(?:_[ \t]*){3,}|(?:\*[ \t]*){3,})(?:\n+|$)/,u=/^ {0,3}(#{1,6})(?=\s|$)(.*)(?:\n+|$)/,m=/(?:[*+-]|\d{1,9}[.)])/,g=/^(?!bull |blockCode|fences|blockquote|heading|html|table)((?:.|\n(?!\s*?\n|bull |blockCode|fences|blockquote|heading|html|table))+?)\n {0,3}(=+|-+) *(?:\n+|$)/,f=n(g).replace(/bull/g,m).replace(/blockCode/g,/(?: {4}| {0,3}\t)/).replace(/fences/g,/ {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g,/ {0,3}>/).replace(/heading/g,/ {0,3}#{1,6}/).replace(/html/g,/ {0,3}<[^\n>]+>\n/).replace(/\|table/g,"").getRegex(),v=n(g).replace(/bull/g,m).replace(/blockCode/g,/(?: {4}| {0,3}\t)/).replace(/fences/g,/ {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g,/ {0,3}>/).replace(/heading/g,/ {0,3}#{1,6}/).replace(/html/g,/ {0,3}<[^\n>]+>\n/).replace(/table/g,/ {0,3}\|?(?:[:\- ]*\|)+[\:\- ]*\n/).getRegex(),b=/^([^\n]+(?:\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\n)[^\n]+)*)/,y=/^[^\n]+/,_=/(?!\s*\])(?:\\.|[^\[\]\\])+/,C=n(/^ {0,3}\[(label)\]: *(?:\n[ \t]*)?([^<\s][^\s]*|<.*?>)(?:(?: +(?:\n[ \t]*)?| *\n[ \t]*)(title))? *(?:\n+|$)/).replace("label",_).replace("title",/(?:"(?:\\"?|[^"\\])*"|'[^'\n]*(?:\n[^'\n]+)*\n?'|\([^()]*\))/).getRegex(),w=n(/^( {0,3}bull)([ \t][^\n]+?)?(?:\n|$)/).replace(/bull/g,m).getRegex(),k="address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|search|section|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul",x=/<!--(?:-?>|[\s\S]*?(?:-->|$))/,$=n("^ {0,3}(?:<(script|pre|style|textarea)[\\s>][\\s\\S]*?(?:</\\1>[^\\n]*\\n+|$)|comment[^\\n]*(\\n+|$)|<\\?[\\s\\S]*?(?:\\?>\\n*|$)|<![A-Z][\\s\\S]*?(?:>\\n*|$)|<!\\[CDATA\\[[\\s\\S]*?(?:\\]\\]>\\n*|$)|</?(tag)(?: +|\\n|/?>)[\\s\\S]*?(?:(?:\\n[ \t]*)+\\n|$)|<(?!script|pre|style|textarea)([a-z][\\w-]*)(?:attribute)*? */?>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n[ \t]*)+\\n|$)|</(?!script|pre|style|textarea)[a-z][\\w-]*\\s*>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n[ \t]*)+\\n|$))","i").replace("comment",x).replace("tag",k).replace("attribute",/ +[a-zA-Z:_][\w.:-]*(?: *= *"[^"\n]*"| *= *'[^'\n]*'| *= *[^\s"'=<>`]+)?/).getRegex(),S=n(b).replace("hr",p).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("|table","").replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",k).getRegex(),T=n(/^( {0,3}> ?(paragraph|[^\n]*)(?:\n|$))+/).replace("paragraph",S).getRegex(),P={blockquote:T,code:d,def:C,fences:h,heading:u,hr:p,html:$,lheading:f,list:w,newline:c,paragraph:S,table:l,text:y},I=n("^ *([^\\n ].*)\\n {0,3}((?:\\| *)?:?-+:? *(?:\\| *:?-+:? *)*(?:\\| *)?)(?:\\n((?:(?! *\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\n|$))*)\\n*|$)").replace("hr",p).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("blockquote"," {0,3}>").replace("code","(?: {4}| {0,3}\t)[^\\n]").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",k).getRegex(),D={...P,lheading:v,table:I,paragraph:n(b).replace("hr",p).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("table",I).replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",k).getRegex()},R={...P,html:n("^ *(?:comment *(?:\\n|\\s*$)|<(tag)[\\s\\S]+?</\\1> *(?:\\n{2,}|\\s*$)|<tag(?:\"[^\"]*\"|'[^']*'|\\s[^'\"/>\\s]*)*?/?> *(?:\\n{2,}|\\s*$))").replace("comment",x).replace(/tag/g,"(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:|[^\\w\\s@]*@)\\b").getRegex(),def:/^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +(["(][^\n]+[")]))? *(?:\n+|$)/,heading:/^(#{1,6})(.*)(?:\n+|$)/,fences:l,lheading:/^(.+?)\n {0,3}(=+|-+) *(?:\n+|$)/,paragraph:n(b).replace("hr",p).replace("heading"," *#{1,6} *[^\n]").replace("lheading",f).replace("|table","").replace("blockquote"," {0,3}>").replace("|fences","").replace("|list","").replace("|html","").replace("|tag","").getRegex()},L=/^\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/,A=/^(`+)([^`]|[^`][\s\S]*?[^`])\1(?!`)/,E=/^( {2,}|\\)\n(?!\s*$)/,F=/^(`+|[^`])(?:(?= {2,}\n)|[\s\S]*?(?:(?=[\\<!\[`*_]|\b_|$)|[^ ](?= {2,}\n)))/,z=/[\p{P}\p{S}]/u,O=/[\s\p{P}\p{S}]/u,M=/[^\s\p{P}\p{S}]/u,V=n(/^((?![*_])punctSpace)/,"u").replace(/punctSpace/g,O).getRegex(),B=/(?!~)[\p{P}\p{S}]/u,N=/(?!~)[\s\p{P}\p{S}]/u,H=/(?:[^\s\p{P}\p{S}]|~)/u,q=/\[[^[\]]*?\]\((?:\\.|[^\\\(\)]|\((?:\\.|[^\\\(\)])*\))*\)|`[^`]*?`|<[^<>]*?>/g,j=/^(?:\*+(?:((?!\*)punct)|[^\s*]))|^_+(?:((?!_)punct)|([^\s_]))/,U=n(j,"u").replace(/punct/g,z).getRegex(),G=n(j,"u").replace(/punct/g,B).getRegex(),J="^[^_*]*?__[^_*]*?\\*[^_*]*?(?=__)|[^*]+(?=[^*])|(?!\\*)punct(\\*+)(?=[\\s]|$)|notPunctSpace(\\*+)(?!\\*)(?=punctSpace|$)|(?!\\*)punctSpace(\\*+)(?=notPunctSpace)|[\\s](\\*+)(?!\\*)(?=punct)|(?!\\*)punct(\\*+)(?!\\*)(?=punct)|notPunctSpace(\\*+)(?=notPunctSpace)",K=n(J,"gu").replace(/notPunctSpace/g,M).replace(/punctSpace/g,O).replace(/punct/g,z).getRegex(),Q=n(J,"gu").replace(/notPunctSpace/g,H).replace(/punctSpace/g,N).replace(/punct/g,B).getRegex(),W=n("^[^_*]*?\\*\\*[^_*]*?_[^_*]*?(?=\\*\\*)|[^_]+(?=[^_])|(?!_)punct(_+)(?=[\\s]|$)|notPunctSpace(_+)(?!_)(?=punctSpace|$)|(?!_)punctSpace(_+)(?=notPunctSpace)|[\\s](_+)(?!_)(?=punct)|(?!_)punct(_+)(?!_)(?=punct)","gu").replace(/notPunctSpace/g,M).replace(/punctSpace/g,O).replace(/punct/g,z).getRegex(),Z=n(/\\(punct)/,"gu").replace(/punct/g,z).getRegex(),Y=n(/^<(scheme:[^\s\x00-\x1f<>]*|email)>/).replace("scheme",/[a-zA-Z][a-zA-Z0-9+.-]{1,31}/).replace("email",/[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/).getRegex(),X=n(x).replace("(?:--\x3e|$)","--\x3e").getRegex(),tt=n("^comment|^</[a-zA-Z][\\w:-]*\\s*>|^<[a-zA-Z][\\w-]*(?:attribute)*?\\s*/?>|^<\\?[\\s\\S]*?\\?>|^<![a-zA-Z]+\\s[\\s\\S]*?>|^<!\\[CDATA\\[[\\s\\S]*?\\]\\]>").replace("comment",X).replace("attribute",/\s+[a-zA-Z:_][\w.:-]*(?:\s*=\s*"[^"]*"|\s*=\s*'[^']*'|\s*=\s*[^\s"'=<>`]+)?/).getRegex(),et=/(?:\[(?:\\.|[^\[\]\\])*\]|\\.|`[^`]*`|[^\[\]\\`])*?/,st=n(/^!?\[(label)\]\(\s*(href)(?:(?:[ \t]*(?:\n[ \t]*)?)(title))?\s*\)/).replace("label",et).replace("href",/<(?:\\.|[^\n<>\\])+>|[^ \t\n\x00-\x1f]*/).replace("title",/"(?:\\"?|[^"\\])*"|'(?:\\'?|[^'\\])*'|\((?:\\\)?|[^)\\])*\)/).getRegex(),at=n(/^!?\[(label)\]\[(ref)\]/).replace("label",et).replace("ref",_).getRegex(),it=n(/^!?\[(ref)\](?:\[\])?/).replace("ref",_).getRegex(),rt=n("reflink|nolink(?!\\()","g").replace("reflink",at).replace("nolink",it).getRegex(),lt={_backpedal:l,anyPunctuation:Z,autolink:Y,blockSkip:q,br:E,code:A,del:l,emStrongLDelim:U,emStrongRDelimAst:K,emStrongRDelimUnd:W,escape:L,link:st,nolink:it,punctuation:V,reflink:at,reflinkSearch:rt,tag:tt,text:F,url:l},nt={...lt,link:n(/^!?\[(label)\]\((.*?)\)/).replace("label",et).getRegex(),reflink:n(/^!?\[(label)\]\s*\[([^\]]*)\]/).replace("label",et).getRegex()},ot={...lt,emStrongRDelimAst:Q,emStrongLDelim:G,url:n(/^((?:ftp|https?):\/\/|www\.)(?:[a-zA-Z0-9\-]+\.?)+[^\s<]*|^email/,"i").replace("email",/[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/).getRegex(),_backpedal:/(?:[^?!.,:;*_'"~()&]+|\([^)]*\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'"~)]+(?!$))+/,del:/^(~~?)(?=[^\s~])((?:\\.|[^\\])*?(?:\\.|[^\s~\\]))\1(?=[^~]|$)/,text:/^([`~]+|[^`~])(?:(?= {2,}\n)|(?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)|[\s\S]*?(?:(?=[\\<!\[`*~_]|\b_|https?:\/\/|ftp:\/\/|www\.|$)|[^ ](?= {2,}\n)|[^a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-](?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)))/},ct={...ot,br:n(E).replace("{2,}","*").getRegex(),text:n(ot.text).replace("\\b_","\\b_| {2,}\\n").replace(/\{2,\}/g,"*").getRegex()},dt={normal:P,gfm:D,pedantic:R},ht={normal:lt,gfm:ot,breaks:ct,pedantic:nt},pt={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},ut=t=>pt[t];function mt(t,e){if(e){if(o.escapeTest.test(t))return t.replace(o.escapeReplace,ut)}else if(o.escapeTestNoEncode.test(t))return t.replace(o.escapeReplaceNoEncode,ut);return t}function gt(t){try{t=encodeURI(t).replace(o.percentDecode,"%")}catch{return null}return t}function ft(t,e){const s=t.replace(o.findPipe,((t,e,s)=>{let a=!1,i=e;while(--i>=0&&"\\"===s[i])a=!a;return a?"|":" |"})),a=s.split(o.splitPipe);let i=0;if(a[0].trim()||a.shift(),a.length>0&&!a.at(-1)?.trim()&&a.pop(),e)if(a.length>e)a.splice(e);else while(a.length<e)a.push("");for(;i<a.length;i++)a[i]=a[i].trim().replace(o.slashPipe,"|");return a}function vt(t,e,s){const a=t.length;if(0===a)return"";let i=0;while(i<a){const r=t.charAt(a-i-1);if(r!==e||s){if(r===e||!s)break;i++}else i++}return t.slice(0,a-i)}function bt(t,e){if(-1===t.indexOf(e[1]))return-1;let s=0;for(let a=0;a<t.length;a++)if("\\"===t[a])a++;else if(t[a]===e[0])s++;else if(t[a]===e[1]&&(s--,s<0))return a;return s>0?-2:-1}function yt(t,e,s,a,i){const r=e.href,l=e.title||null,n=t[1].replace(i.other.outputLinkReplace,"$1");a.state.inLink=!0;const o={type:"!"===t[0].charAt(0)?"image":"link",raw:s,href:r,title:l,text:n,tokens:a.inlineTokens(n)};return a.state.inLink=!1,o}function _t(t,e,s){const a=t.match(s.other.indentCodeCompensation);if(null===a)return e;const i=a[1];return e.split("\n").map((t=>{const e=t.match(s.other.beginningSpace);if(null===e)return t;const[a]=e;return a.length>=i.length?t.slice(i.length):t})).join("\n")}var Ct=class{options;rules;lexer;constructor(t){this.options=t||i}space(t){const e=this.rules.block.newline.exec(t);if(e&&e[0].length>0)return{type:"space",raw:e[0]}}code(t){const e=this.rules.block.code.exec(t);if(e){const t=e[0].replace(this.rules.other.codeRemoveIndent,"");return{type:"code",raw:e[0],codeBlockStyle:"indented",text:this.options.pedantic?t:vt(t,"\n")}}}fences(t){const e=this.rules.block.fences.exec(t);if(e){const t=e[0],s=_t(t,e[3]||"",this.rules);return{type:"code",raw:t,lang:e[2]?e[2].trim().replace(this.rules.inline.anyPunctuation,"$1"):e[2],text:s}}}heading(t){const e=this.rules.block.heading.exec(t);if(e){let t=e[2].trim();if(this.rules.other.endingHash.test(t)){const e=vt(t,"#");this.options.pedantic?t=e.trim():e&&!this.rules.other.endingSpaceChar.test(e)||(t=e.trim())}return{type:"heading",raw:e[0],depth:e[1].length,text:t,tokens:this.lexer.inline(t)}}}hr(t){const e=this.rules.block.hr.exec(t);if(e)return{type:"hr",raw:vt(e[0],"\n")}}blockquote(t){const e=this.rules.block.blockquote.exec(t);if(e){let t=vt(e[0],"\n").split("\n"),s="",a="";const i=[];while(t.length>0){let e=!1;const r=[];let l;for(l=0;l<t.length;l++)if(this.rules.other.blockquoteStart.test(t[l]))r.push(t[l]),e=!0;else{if(e)break;r.push(t[l])}t=t.slice(l);const n=r.join("\n"),o=n.replace(this.rules.other.blockquoteSetextReplace,"\n    $1").replace(this.rules.other.blockquoteSetextReplace2,"");s=s?`${s}\n${n}`:n,a=a?`${a}\n${o}`:o;const c=this.lexer.state.top;if(this.lexer.state.top=!0,this.lexer.blockTokens(o,i,!0),this.lexer.state.top=c,0===t.length)break;const d=i.at(-1);if("code"===d?.type)break;if("blockquote"===d?.type){const e=d,r=e.raw+"\n"+t.join("\n"),l=this.blockquote(r);i[i.length-1]=l,s=s.substring(0,s.length-e.raw.length)+l.raw,a=a.substring(0,a.length-e.text.length)+l.text;break}if("list"!==d?.type);else{const e=d,r=e.raw+"\n"+t.join("\n"),l=this.list(r);i[i.length-1]=l,s=s.substring(0,s.length-d.raw.length)+l.raw,a=a.substring(0,a.length-e.raw.length)+l.raw,t=r.substring(i.at(-1).raw.length).split("\n")}}return{type:"blockquote",raw:s,tokens:i,text:a}}}list(t){let e=this.rules.block.list.exec(t);if(e){let s=e[1].trim();const a=s.length>1,i={type:"list",raw:"",ordered:a,start:a?+s.slice(0,-1):"",loose:!1,items:[]};s=a?`\\d{1,9}\\${s.slice(-1)}`:`\\${s}`,this.options.pedantic&&(s=a?s:"[*+-]");const r=this.rules.other.listItemRegex(s);let l=!1;while(t){let s=!1,a="",n="";if(!(e=r.exec(t)))break;if(this.rules.block.hr.test(t))break;a=e[0],t=t.substring(a.length);let o=e[2].split("\n",1)[0].replace(this.rules.other.listReplaceTabs,(t=>" ".repeat(3*t.length))),c=t.split("\n",1)[0],d=!o.trim(),h=0;if(this.options.pedantic?(h=2,n=o.trimStart()):d?h=e[1].length+1:(h=e[2].search(this.rules.other.nonSpaceChar),h=h>4?1:h,n=o.slice(h),h+=e[1].length),d&&this.rules.other.blankLine.test(c)&&(a+=c+"\n",t=t.substring(c.length+1),s=!0),!s){const e=this.rules.other.nextBulletRegex(h),s=this.rules.other.hrRegex(h),i=this.rules.other.fencesBeginRegex(h),r=this.rules.other.headingBeginRegex(h),l=this.rules.other.htmlBeginRegex(h);while(t){const p=t.split("\n",1)[0];let u;if(c=p,this.options.pedantic?(c=c.replace(this.rules.other.listReplaceNesting,"  "),u=c):u=c.replace(this.rules.other.tabCharGlobal,"    "),i.test(c))break;if(r.test(c))break;if(l.test(c))break;if(e.test(c))break;if(s.test(c))break;if(u.search(this.rules.other.nonSpaceChar)>=h||!c.trim())n+="\n"+u.slice(h);else{if(d)break;if(o.replace(this.rules.other.tabCharGlobal,"    ").search(this.rules.other.nonSpaceChar)>=4)break;if(i.test(o))break;if(r.test(o))break;if(s.test(o))break;n+="\n"+c}d||c.trim()||(d=!0),a+=p+"\n",t=t.substring(p.length+1),o=u.slice(h)}}i.loose||(l?i.loose=!0:this.rules.other.doubleBlankLine.test(a)&&(l=!0));let p,u=null;this.options.gfm&&(u=this.rules.other.listIsTask.exec(n),u&&(p="[ ] "!==u[0],n=n.replace(this.rules.other.listReplaceTask,""))),i.items.push({type:"list_item",raw:a,task:!!u,checked:p,loose:!1,text:n,tokens:[]}),i.raw+=a}const n=i.items.at(-1);if(!n)return;n.raw=n.raw.trimEnd(),n.text=n.text.trimEnd(),i.raw=i.raw.trimEnd();for(let t=0;t<i.items.length;t++)if(this.lexer.state.top=!1,i.items[t].tokens=this.lexer.blockTokens(i.items[t].text,[]),!i.loose){const e=i.items[t].tokens.filter((t=>"space"===t.type)),s=e.length>0&&e.some((t=>this.rules.other.anyLine.test(t.raw)));i.loose=s}if(i.loose)for(let t=0;t<i.items.length;t++)i.items[t].loose=!0;return i}}html(t){const e=this.rules.block.html.exec(t);if(e){const t={type:"html",block:!0,raw:e[0],pre:"pre"===e[1]||"script"===e[1]||"style"===e[1],text:e[0]};return t}}def(t){const e=this.rules.block.def.exec(t);if(e){const t=e[1].toLowerCase().replace(this.rules.other.multipleSpaceGlobal," "),s=e[2]?e[2].replace(this.rules.other.hrefBrackets,"$1").replace(this.rules.inline.anyPunctuation,"$1"):"",a=e[3]?e[3].substring(1,e[3].length-1).replace(this.rules.inline.anyPunctuation,"$1"):e[3];return{type:"def",tag:t,raw:e[0],href:s,title:a}}}table(t){const e=this.rules.block.table.exec(t);if(!e)return;if(!this.rules.other.tableDelimiter.test(e[2]))return;const s=ft(e[1]),a=e[2].replace(this.rules.other.tableAlignChars,"").split("|"),i=e[3]?.trim()?e[3].replace(this.rules.other.tableRowBlankLine,"").split("\n"):[],r={type:"table",raw:e[0],header:[],align:[],rows:[]};if(s.length===a.length){for(const t of a)this.rules.other.tableAlignRight.test(t)?r.align.push("right"):this.rules.other.tableAlignCenter.test(t)?r.align.push("center"):this.rules.other.tableAlignLeft.test(t)?r.align.push("left"):r.align.push(null);for(let t=0;t<s.length;t++)r.header.push({text:s[t],tokens:this.lexer.inline(s[t]),header:!0,align:r.align[t]});for(const t of i)r.rows.push(ft(t,r.header.length).map(((t,e)=>({text:t,tokens:this.lexer.inline(t),header:!1,align:r.align[e]}))));return r}}lheading(t){const e=this.rules.block.lheading.exec(t);if(e)return{type:"heading",raw:e[0],depth:"="===e[2].charAt(0)?1:2,text:e[1],tokens:this.lexer.inline(e[1])}}paragraph(t){const e=this.rules.block.paragraph.exec(t);if(e){const t="\n"===e[1].charAt(e[1].length-1)?e[1].slice(0,-1):e[1];return{type:"paragraph",raw:e[0],text:t,tokens:this.lexer.inline(t)}}}text(t){const e=this.rules.block.text.exec(t);if(e)return{type:"text",raw:e[0],text:e[0],tokens:this.lexer.inline(e[0])}}escape(t){const e=this.rules.inline.escape.exec(t);if(e)return{type:"escape",raw:e[0],text:e[1]}}tag(t){const e=this.rules.inline.tag.exec(t);if(e)return!this.lexer.state.inLink&&this.rules.other.startATag.test(e[0])?this.lexer.state.inLink=!0:this.lexer.state.inLink&&this.rules.other.endATag.test(e[0])&&(this.lexer.state.inLink=!1),!this.lexer.state.inRawBlock&&this.rules.other.startPreScriptTag.test(e[0])?this.lexer.state.inRawBlock=!0:this.lexer.state.inRawBlock&&this.rules.other.endPreScriptTag.test(e[0])&&(this.lexer.state.inRawBlock=!1),{type:"html",raw:e[0],inLink:this.lexer.state.inLink,inRawBlock:this.lexer.state.inRawBlock,block:!1,text:e[0]}}link(t){const e=this.rules.inline.link.exec(t);if(e){const t=e[2].trim();if(!this.options.pedantic&&this.rules.other.startAngleBracket.test(t)){if(!this.rules.other.endAngleBracket.test(t))return;const e=vt(t.slice(0,-1),"\\");if((t.length-e.length)%2===0)return}else{const t=bt(e[2],"()");if(-2===t)return;if(t>-1){const s=0===e[0].indexOf("!")?5:4,a=s+e[1].length+t;e[2]=e[2].substring(0,t),e[0]=e[0].substring(0,a).trim(),e[3]=""}}let s=e[2],a="";if(this.options.pedantic){const t=this.rules.other.pedanticHrefTitle.exec(s);t&&(s=t[1],a=t[3])}else a=e[3]?e[3].slice(1,-1):"";return s=s.trim(),this.rules.other.startAngleBracket.test(s)&&(s=this.options.pedantic&&!this.rules.other.endAngleBracket.test(t)?s.slice(1):s.slice(1,-1)),yt(e,{href:s?s.replace(this.rules.inline.anyPunctuation,"$1"):s,title:a?a.replace(this.rules.inline.anyPunctuation,"$1"):a},e[0],this.lexer,this.rules)}}reflink(t,e){let s;if((s=this.rules.inline.reflink.exec(t))||(s=this.rules.inline.nolink.exec(t))){const t=(s[2]||s[1]).replace(this.rules.other.multipleSpaceGlobal," "),a=e[t.toLowerCase()];if(!a){const t=s[0].charAt(0);return{type:"text",raw:t,text:t}}return yt(s,a,s[0],this.lexer,this.rules)}}emStrong(t,e,s=""){let a=this.rules.inline.emStrongLDelim.exec(t);if(!a)return;if(a[3]&&s.match(this.rules.other.unicodeAlphaNumeric))return;const i=a[1]||a[2]||"";if(!i||!s||this.rules.inline.punctuation.exec(s)){const s=[...a[0]].length-1;let i,r,l=s,n=0;const o="*"===a[0][0]?this.rules.inline.emStrongRDelimAst:this.rules.inline.emStrongRDelimUnd;o.lastIndex=0,e=e.slice(-1*t.length+s);while(null!=(a=o.exec(e))){if(i=a[1]||a[2]||a[3]||a[4]||a[5]||a[6],!i)continue;if(r=[...i].length,a[3]||a[4]){l+=r;continue}if((a[5]||a[6])&&s%3&&!((s+r)%3)){n+=r;continue}if(l-=r,l>0)continue;r=Math.min(r,r+l+n);const e=[...a[0]][0].length,o=t.slice(0,s+a.index+e+r);if(Math.min(s,r)%2){const t=o.slice(1,-1);return{type:"em",raw:o,text:t,tokens:this.lexer.inlineTokens(t)}}const c=o.slice(2,-2);return{type:"strong",raw:o,text:c,tokens:this.lexer.inlineTokens(c)}}}}codespan(t){const e=this.rules.inline.code.exec(t);if(e){let t=e[2].replace(this.rules.other.newLineCharGlobal," ");const s=this.rules.other.nonSpaceChar.test(t),a=this.rules.other.startingSpaceChar.test(t)&&this.rules.other.endingSpaceChar.test(t);return s&&a&&(t=t.substring(1,t.length-1)),{type:"codespan",raw:e[0],text:t}}}br(t){const e=this.rules.inline.br.exec(t);if(e)return{type:"br",raw:e[0]}}del(t){const e=this.rules.inline.del.exec(t);if(e)return{type:"del",raw:e[0],text:e[2],tokens:this.lexer.inlineTokens(e[2])}}autolink(t){const e=this.rules.inline.autolink.exec(t);if(e){let t,s;return"@"===e[2]?(t=e[1],s="mailto:"+t):(t=e[1],s=t),{type:"link",raw:e[0],text:t,href:s,tokens:[{type:"text",raw:t,text:t}]}}}url(t){let e;if(e=this.rules.inline.url.exec(t)){let t,s;if("@"===e[2])t=e[0],s="mailto:"+t;else{let a;do{a=e[0],e[0]=this.rules.inline._backpedal.exec(e[0])?.[0]??""}while(a!==e[0]);t=e[0],s="www."===e[1]?"http://"+e[0]:e[0]}return{type:"link",raw:e[0],text:t,href:s,tokens:[{type:"text",raw:t,text:t}]}}}inlineText(t){const e=this.rules.inline.text.exec(t);if(e){const t=this.lexer.state.inRawBlock;return{type:"text",raw:e[0],text:e[0],escaped:t}}}},wt=class t{tokens;options;state;tokenizer;inlineQueue;constructor(t){this.tokens=[],this.tokens.links=Object.create(null),this.options=t||i,this.options.tokenizer=this.options.tokenizer||new Ct,this.tokenizer=this.options.tokenizer,this.tokenizer.options=this.options,this.tokenizer.lexer=this,this.inlineQueue=[],this.state={inLink:!1,inRawBlock:!1,top:!0};const e={other:o,block:dt.normal,inline:ht.normal};this.options.pedantic?(e.block=dt.pedantic,e.inline=ht.pedantic):this.options.gfm&&(e.block=dt.gfm,this.options.breaks?e.inline=ht.breaks:e.inline=ht.gfm),this.tokenizer.rules=e}static get rules(){return{block:dt,inline:ht}}static lex(e,s){const a=new t(s);return a.lex(e)}static lexInline(e,s){const a=new t(s);return a.inlineTokens(e)}lex(t){t=t.replace(o.carriageReturn,"\n"),this.blockTokens(t,this.tokens);for(let e=0;e<this.inlineQueue.length;e++){const t=this.inlineQueue[e];this.inlineTokens(t.src,t.tokens)}return this.inlineQueue=[],this.tokens}blockTokens(t,e=[],s=!1){this.options.pedantic&&(t=t.replace(o.tabCharGlobal,"    ").replace(o.spaceLine,""));while(t){let a;if(this.options.extensions?.block?.some((s=>!!(a=s.call({lexer:this},t,e))&&(t=t.substring(a.raw.length),e.push(a),!0))))continue;if(a=this.tokenizer.space(t)){t=t.substring(a.raw.length);const s=e.at(-1);1===a.raw.length&&void 0!==s?s.raw+="\n":e.push(a);continue}if(a=this.tokenizer.code(t)){t=t.substring(a.raw.length);const s=e.at(-1);"paragraph"===s?.type||"text"===s?.type?(s.raw+="\n"+a.raw,s.text+="\n"+a.text,this.inlineQueue.at(-1).src=s.text):e.push(a);continue}if(a=this.tokenizer.fences(t)){t=t.substring(a.raw.length),e.push(a);continue}if(a=this.tokenizer.heading(t)){t=t.substring(a.raw.length),e.push(a);continue}if(a=this.tokenizer.hr(t)){t=t.substring(a.raw.length),e.push(a);continue}if(a=this.tokenizer.blockquote(t)){t=t.substring(a.raw.length),e.push(a);continue}if(a=this.tokenizer.list(t)){t=t.substring(a.raw.length),e.push(a);continue}if(a=this.tokenizer.html(t)){t=t.substring(a.raw.length),e.push(a);continue}if(a=this.tokenizer.def(t)){t=t.substring(a.raw.length);const s=e.at(-1);"paragraph"===s?.type||"text"===s?.type?(s.raw+="\n"+a.raw,s.text+="\n"+a.raw,this.inlineQueue.at(-1).src=s.text):this.tokens.links[a.tag]||(this.tokens.links[a.tag]={href:a.href,title:a.title});continue}if(a=this.tokenizer.table(t)){t=t.substring(a.raw.length),e.push(a);continue}if(a=this.tokenizer.lheading(t)){t=t.substring(a.raw.length),e.push(a);continue}let i=t;if(this.options.extensions?.startBlock){let e=1/0;const s=t.slice(1);let a;this.options.extensions.startBlock.forEach((t=>{a=t.call({lexer:this},s),"number"===typeof a&&a>=0&&(e=Math.min(e,a))})),e<1/0&&e>=0&&(i=t.substring(0,e+1))}if(this.state.top&&(a=this.tokenizer.paragraph(i))){const r=e.at(-1);s&&"paragraph"===r?.type?(r.raw+="\n"+a.raw,r.text+="\n"+a.text,this.inlineQueue.pop(),this.inlineQueue.at(-1).src=r.text):e.push(a),s=i.length!==t.length,t=t.substring(a.raw.length)}else if(a=this.tokenizer.text(t)){t=t.substring(a.raw.length);const s=e.at(-1);"text"===s?.type?(s.raw+="\n"+a.raw,s.text+="\n"+a.text,this.inlineQueue.pop(),this.inlineQueue.at(-1).src=s.text):e.push(a)}else if(t){const e="Infinite loop on byte: "+t.charCodeAt(0);if(this.options.silent){console.error(e);break}throw new Error(e)}}return this.state.top=!0,e}inline(t,e=[]){return this.inlineQueue.push({src:t,tokens:e}),e}inlineTokens(t,e=[]){let s=t,a=null;if(this.tokens.links){const t=Object.keys(this.tokens.links);if(t.length>0)while(null!=(a=this.tokenizer.rules.inline.reflinkSearch.exec(s)))t.includes(a[0].slice(a[0].lastIndexOf("[")+1,-1))&&(s=s.slice(0,a.index)+"["+"a".repeat(a[0].length-2)+"]"+s.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex))}while(null!=(a=this.tokenizer.rules.inline.anyPunctuation.exec(s)))s=s.slice(0,a.index)+"++"+s.slice(this.tokenizer.rules.inline.anyPunctuation.lastIndex);while(null!=(a=this.tokenizer.rules.inline.blockSkip.exec(s)))s=s.slice(0,a.index)+"["+"a".repeat(a[0].length-2)+"]"+s.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);let i=!1,r="";while(t){let a;if(i||(r=""),i=!1,this.options.extensions?.inline?.some((s=>!!(a=s.call({lexer:this},t,e))&&(t=t.substring(a.raw.length),e.push(a),!0))))continue;if(a=this.tokenizer.escape(t)){t=t.substring(a.raw.length),e.push(a);continue}if(a=this.tokenizer.tag(t)){t=t.substring(a.raw.length),e.push(a);continue}if(a=this.tokenizer.link(t)){t=t.substring(a.raw.length),e.push(a);continue}if(a=this.tokenizer.reflink(t,this.tokens.links)){t=t.substring(a.raw.length);const s=e.at(-1);"text"===a.type&&"text"===s?.type?(s.raw+=a.raw,s.text+=a.text):e.push(a);continue}if(a=this.tokenizer.emStrong(t,s,r)){t=t.substring(a.raw.length),e.push(a);continue}if(a=this.tokenizer.codespan(t)){t=t.substring(a.raw.length),e.push(a);continue}if(a=this.tokenizer.br(t)){t=t.substring(a.raw.length),e.push(a);continue}if(a=this.tokenizer.del(t)){t=t.substring(a.raw.length),e.push(a);continue}if(a=this.tokenizer.autolink(t)){t=t.substring(a.raw.length),e.push(a);continue}if(!this.state.inLink&&(a=this.tokenizer.url(t))){t=t.substring(a.raw.length),e.push(a);continue}let l=t;if(this.options.extensions?.startInline){let e=1/0;const s=t.slice(1);let a;this.options.extensions.startInline.forEach((t=>{a=t.call({lexer:this},s),"number"===typeof a&&a>=0&&(e=Math.min(e,a))})),e<1/0&&e>=0&&(l=t.substring(0,e+1))}if(a=this.tokenizer.inlineText(l)){t=t.substring(a.raw.length),"_"!==a.raw.slice(-1)&&(r=a.raw.slice(-1)),i=!0;const s=e.at(-1);"text"===s?.type?(s.raw+=a.raw,s.text+=a.text):e.push(a)}else if(t){const e="Infinite loop on byte: "+t.charCodeAt(0);if(this.options.silent){console.error(e);break}throw new Error(e)}}return e}},kt=class{options;parser;constructor(t){this.options=t||i}space(t){return""}code({text:t,lang:e,escaped:s}){const a=(e||"").match(o.notSpaceStart)?.[0],i=t.replace(o.endingNewline,"")+"\n";return a?'<pre><code class="language-'+mt(a)+'">'+(s?i:mt(i,!0))+"</code></pre>\n":"<pre><code>"+(s?i:mt(i,!0))+"</code></pre>\n"}blockquote({tokens:t}){const e=this.parser.parse(t);return`<blockquote>\n${e}</blockquote>\n`}html({text:t}){return t}heading({tokens:t,depth:e}){return`<h${e}>${this.parser.parseInline(t)}</h${e}>\n`}hr(t){return"<hr>\n"}list(t){const e=t.ordered,s=t.start;let a="";for(let l=0;l<t.items.length;l++){const e=t.items[l];a+=this.listitem(e)}const i=e?"ol":"ul",r=e&&1!==s?' start="'+s+'"':"";return"<"+i+r+">\n"+a+"</"+i+">\n"}listitem(t){let e="";if(t.task){const s=this.checkbox({checked:!!t.checked});t.loose?"paragraph"===t.tokens[0]?.type?(t.tokens[0].text=s+" "+t.tokens[0].text,t.tokens[0].tokens&&t.tokens[0].tokens.length>0&&"text"===t.tokens[0].tokens[0].type&&(t.tokens[0].tokens[0].text=s+" "+mt(t.tokens[0].tokens[0].text),t.tokens[0].tokens[0].escaped=!0)):t.tokens.unshift({type:"text",raw:s+" ",text:s+" ",escaped:!0}):e+=s+" "}return e+=this.parser.parse(t.tokens,!!t.loose),`<li>${e}</li>\n`}checkbox({checked:t}){return"<input "+(t?'checked="" ':"")+'disabled="" type="checkbox">'}paragraph({tokens:t}){return`<p>${this.parser.parseInline(t)}</p>\n`}table(t){let e="",s="";for(let i=0;i<t.header.length;i++)s+=this.tablecell(t.header[i]);e+=this.tablerow({text:s});let a="";for(let i=0;i<t.rows.length;i++){const e=t.rows[i];s="";for(let t=0;t<e.length;t++)s+=this.tablecell(e[t]);a+=this.tablerow({text:s})}return a&&(a=`<tbody>${a}</tbody>`),"<table>\n<thead>\n"+e+"</thead>\n"+a+"</table>\n"}tablerow({text:t}){return`<tr>\n${t}</tr>\n`}tablecell(t){const e=this.parser.parseInline(t.tokens),s=t.header?"th":"td",a=t.align?`<${s} align="${t.align}">`:`<${s}>`;return a+e+`</${s}>\n`}strong({tokens:t}){return`<strong>${this.parser.parseInline(t)}</strong>`}em({tokens:t}){return`<em>${this.parser.parseInline(t)}</em>`}codespan({text:t}){return`<code>${mt(t,!0)}</code>`}br(t){return"<br>"}del({tokens:t}){return`<del>${this.parser.parseInline(t)}</del>`}link({href:t,title:e,tokens:s}){const a=this.parser.parseInline(s),i=gt(t);if(null===i)return a;t=i;let r='<a href="'+t+'"';return e&&(r+=' title="'+mt(e)+'"'),r+=">"+a+"</a>",r}image({href:t,title:e,text:s,tokens:a}){a&&(s=this.parser.parseInline(a,this.parser.textRenderer));const i=gt(t);if(null===i)return mt(s);t=i;let r=`<img src="${t}" alt="${s}"`;return e&&(r+=` title="${mt(e)}"`),r+=">",r}text(t){return"tokens"in t&&t.tokens?this.parser.parseInline(t.tokens):"escaped"in t&&t.escaped?t.text:mt(t.text)}},xt=class{strong({text:t}){return t}em({text:t}){return t}codespan({text:t}){return t}del({text:t}){return t}html({text:t}){return t}text({text:t}){return t}link({text:t}){return""+t}image({text:t}){return""+t}br(){return""}},$t=class t{options;renderer;textRenderer;constructor(t){this.options=t||i,this.options.renderer=this.options.renderer||new kt,this.renderer=this.options.renderer,this.renderer.options=this.options,this.renderer.parser=this,this.textRenderer=new xt}static parse(e,s){const a=new t(s);return a.parse(e)}static parseInline(e,s){const a=new t(s);return a.parseInline(e)}parse(t,e=!0){let s="";for(let a=0;a<t.length;a++){const i=t[a];if(this.options.extensions?.renderers?.[i.type]){const t=i,e=this.options.extensions.renderers[t.type].call({parser:this},t);if(!1!==e||!["space","hr","heading","code","table","blockquote","list","html","paragraph","text"].includes(t.type)){s+=e||"";continue}}const r=i;switch(r.type){case"space":s+=this.renderer.space(r);continue;case"hr":s+=this.renderer.hr(r);continue;case"heading":s+=this.renderer.heading(r);continue;case"code":s+=this.renderer.code(r);continue;case"table":s+=this.renderer.table(r);continue;case"blockquote":s+=this.renderer.blockquote(r);continue;case"list":s+=this.renderer.list(r);continue;case"html":s+=this.renderer.html(r);continue;case"paragraph":s+=this.renderer.paragraph(r);continue;case"text":{let i=r,l=this.renderer.text(i);while(a+1<t.length&&"text"===t[a+1].type)i=t[++a],l+="\n"+this.renderer.text(i);s+=e?this.renderer.paragraph({type:"paragraph",raw:l,text:l,tokens:[{type:"text",raw:l,text:l,escaped:!0}]}):l;continue}default:{const t='Token with "'+r.type+'" type was not found.';if(this.options.silent)return console.error(t),"";throw new Error(t)}}}return s}parseInline(t,e=this.renderer){let s="";for(let a=0;a<t.length;a++){const i=t[a];if(this.options.extensions?.renderers?.[i.type]){const t=this.options.extensions.renderers[i.type].call({parser:this},i);if(!1!==t||!["escape","html","link","image","strong","em","codespan","br","del","text"].includes(i.type)){s+=t||"";continue}}const r=i;switch(r.type){case"escape":s+=e.text(r);break;case"html":s+=e.html(r);break;case"link":s+=e.link(r);break;case"image":s+=e.image(r);break;case"strong":s+=e.strong(r);break;case"em":s+=e.em(r);break;case"codespan":s+=e.codespan(r);break;case"br":s+=e.br(r);break;case"del":s+=e.del(r);break;case"text":s+=e.text(r);break;default:{const t='Token with "'+r.type+'" type was not found.';if(this.options.silent)return console.error(t),"";throw new Error(t)}}}return s}},St=class{options;block;constructor(t){this.options=t||i}static passThroughHooks=new Set(["preprocess","postprocess","processAllTokens"]);preprocess(t){return t}postprocess(t){return t}processAllTokens(t){return t}provideLexer(){return this.block?wt.lex:wt.lexInline}provideParser(){return this.block?$t.parse:$t.parseInline}},Tt=class{defaults=a();options=this.setOptions;parse=this.parseMarkdown(!0);parseInline=this.parseMarkdown(!1);Parser=$t;Renderer=kt;TextRenderer=xt;Lexer=wt;Tokenizer=Ct;Hooks=St;constructor(...t){this.use(...t)}walkTokens(t,e){let s=[];for(const a of t)switch(s=s.concat(e.call(this,a)),a.type){case"table":{const t=a;for(const a of t.header)s=s.concat(this.walkTokens(a.tokens,e));for(const a of t.rows)for(const t of a)s=s.concat(this.walkTokens(t.tokens,e));break}case"list":{const t=a;s=s.concat(this.walkTokens(t.items,e));break}default:{const t=a;this.defaults.extensions?.childTokens?.[t.type]?this.defaults.extensions.childTokens[t.type].forEach((a=>{const i=t[a].flat(1/0);s=s.concat(this.walkTokens(i,e))})):t.tokens&&(s=s.concat(this.walkTokens(t.tokens,e)))}}return s}use(...t){const e=this.defaults.extensions||{renderers:{},childTokens:{}};return t.forEach((t=>{const s={...t};if(s.async=this.defaults.async||s.async||!1,t.extensions&&(t.extensions.forEach((t=>{if(!t.name)throw new Error("extension name required");if("renderer"in t){const s=e.renderers[t.name];e.renderers[t.name]=s?function(...e){let a=t.renderer.apply(this,e);return!1===a&&(a=s.apply(this,e)),a}:t.renderer}if("tokenizer"in t){if(!t.level||"block"!==t.level&&"inline"!==t.level)throw new Error("extension level must be 'block' or 'inline'");const s=e[t.level];s?s.unshift(t.tokenizer):e[t.level]=[t.tokenizer],t.start&&("block"===t.level?e.startBlock?e.startBlock.push(t.start):e.startBlock=[t.start]:"inline"===t.level&&(e.startInline?e.startInline.push(t.start):e.startInline=[t.start]))}"childTokens"in t&&t.childTokens&&(e.childTokens[t.name]=t.childTokens)})),s.extensions=e),t.renderer){const e=this.defaults.renderer||new kt(this.defaults);for(const s in t.renderer){if(!(s in e))throw new Error(`renderer '${s}' does not exist`);if(["options","parser"].includes(s))continue;const a=s,i=t.renderer[a],r=e[a];e[a]=(...t)=>{let s=i.apply(e,t);return!1===s&&(s=r.apply(e,t)),s||""}}s.renderer=e}if(t.tokenizer){const e=this.defaults.tokenizer||new Ct(this.defaults);for(const s in t.tokenizer){if(!(s in e))throw new Error(`tokenizer '${s}' does not exist`);if(["options","rules","lexer"].includes(s))continue;const a=s,i=t.tokenizer[a],r=e[a];e[a]=(...t)=>{let s=i.apply(e,t);return!1===s&&(s=r.apply(e,t)),s}}s.tokenizer=e}if(t.hooks){const e=this.defaults.hooks||new St;for(const s in t.hooks){if(!(s in e))throw new Error(`hook '${s}' does not exist`);if(["options","block"].includes(s))continue;const a=s,i=t.hooks[a],r=e[a];St.passThroughHooks.has(s)?e[a]=t=>{if(this.defaults.async)return Promise.resolve(i.call(e,t)).then((t=>r.call(e,t)));const s=i.call(e,t);return r.call(e,s)}:e[a]=(...t)=>{let s=i.apply(e,t);return!1===s&&(s=r.apply(e,t)),s}}s.hooks=e}if(t.walkTokens){const e=this.defaults.walkTokens,a=t.walkTokens;s.walkTokens=function(t){let s=[];return s.push(a.call(this,t)),e&&(s=s.concat(e.call(this,t))),s}}this.defaults={...this.defaults,...s}})),this}setOptions(t){return this.defaults={...this.defaults,...t},this}lexer(t,e){return wt.lex(t,e??this.defaults)}parser(t,e){return $t.parse(t,e??this.defaults)}parseMarkdown(t){const e=(e,s)=>{const a={...s},i={...this.defaults,...a},r=this.onError(!!i.silent,!!i.async);if(!0===this.defaults.async&&!1===a.async)return r(new Error("marked(): The async option was set to true by an extension. Remove async: false from the parse options object to return a Promise."));if("undefined"===typeof e||null===e)return r(new Error("marked(): input parameter is undefined or null"));if("string"!==typeof e)return r(new Error("marked(): input parameter is of type "+Object.prototype.toString.call(e)+", string expected"));i.hooks&&(i.hooks.options=i,i.hooks.block=t);const l=i.hooks?i.hooks.provideLexer():t?wt.lex:wt.lexInline,n=i.hooks?i.hooks.provideParser():t?$t.parse:$t.parseInline;if(i.async)return Promise.resolve(i.hooks?i.hooks.preprocess(e):e).then((t=>l(t,i))).then((t=>i.hooks?i.hooks.processAllTokens(t):t)).then((t=>i.walkTokens?Promise.all(this.walkTokens(t,i.walkTokens)).then((()=>t)):t)).then((t=>n(t,i))).then((t=>i.hooks?i.hooks.postprocess(t):t)).catch(r);try{i.hooks&&(e=i.hooks.preprocess(e));let t=l(e,i);i.hooks&&(t=i.hooks.processAllTokens(t)),i.walkTokens&&this.walkTokens(t,i.walkTokens);let s=n(t,i);return i.hooks&&(s=i.hooks.postprocess(s)),s}catch(o){return r(o)}};return e}onError(t,e){return s=>{if(s.message+="\nPlease report this to https://github.com/markedjs/marked.",t){const t="<p>An error occurred:</p><pre>"+mt(s.message+"",!0)+"</pre>";return e?Promise.resolve(t):t}if(e)return Promise.reject(s);throw s}}},Pt=new Tt;function It(t,e){return Pt.parse(t,e)}It.options=It.setOptions=function(t){return Pt.setOptions(t),It.defaults=Pt.defaults,r(It.defaults),It},It.getDefaults=a,It.defaults=i,It.use=function(...t){return Pt.use(...t),It.defaults=Pt.defaults,r(It.defaults),It},It.walkTokens=function(t,e){return Pt.walkTokens(t,e)},It.parseInline=Pt.parseInline,It.Parser=$t,It.parser=$t.parse,It.Renderer=kt,It.TextRenderer=xt,It.Lexer=wt,It.lexer=wt.lex,It.Tokenizer=Ct,It.Hooks=St,It.parse=It;It.options,It.setOptions,It.use,It.walkTokens,It.parseInline,$t.parse,wt.lex},222:(t,e,s)=>{"use strict";s.r(e),s.d(e,{default:()=>d});var a=function(){var t=this,e=t._self._c;return e("div",{staticClass:"report-history-list"},[e("div",{staticClass:"history-header"},[e("h3",[t._v("历史报告")]),e("div",{staticClass:"header-actions"},[e("el-button",{attrs:{size:"small",type:"text"},on:{click:t.refreshList}},[e("i",{staticClass:"el-icon-refresh"}),t._v(" 刷新 ")])],1)]),e("div",{staticClass:"history-content"},[t.loading?e("div",{staticClass:"loading-container"},[e("i",{staticClass:"el-icon-loading"}),e("p",[t._v("加载中...")])]):t.reportList&&0!==t.reportList.length?t._l(t.reportList,(function(s,a){return e("div",{key:s.id,class:["report-item",t.selectedReportId===s.id?"active":""],on:{click:function(e){return t.selectReport(s,a)}}},[e("div",{staticClass:"report-item-header"},[e("div",{staticClass:"report-title-container"},[e("span",{staticClass:"report-id"},[t._v("#"+t._s(s.id))]),e("span",{staticClass:"report-title"},[t._v(t._s(s.title||"未命名报告"))])]),e("el-button",{staticClass:"delete-btn",attrs:{type:"danger",size:"mini",icon:"el-icon-delete"},on:{click:function(e){return e.stopPropagation(),t.confirmDelete(s)}}},[t._v(" 删除 ")])],1),e("div",{staticClass:"report-info"},[e("span",{staticClass:"report-type"},[t._v(t._s(t.getReportTypeName(s.report_type)))]),e("span",{staticClass:"report-date"},[t._v(t._s(s.create_time))])])])})):e("div",{staticClass:"empty-container"},[e("i",{staticClass:"el-icon-document"}),e("p",[t._v("暂无历史报告")])]),t.total>t.pageSize?e("div",{staticClass:"pagination-container"},[e("el-pagination",{attrs:{background:"",layout:"prev, pager, next","page-size":t.pageSize,total:t.total,"current-page":t.currentPage},on:{"update:currentPage":function(e){t.currentPage=e},"update:current-page":function(e){t.currentPage=e},"current-change":t.handlePageChange}})],1):t._e()],2)])},i=[],r=s(6960);const l={name:"ReportHistoryList",props:{selectedReportId:{type:[Number,String],default:-1},reportType:{type:String,default:""}},data(){return{loading:!1,reportList:[],currentPage:1,pageSize:20,total:0}},mounted(){this.loadReportList()},watch:{reportType(){this.currentPage=1,this.loadReportList()}},methods:{loadReportList(){this.loading=!0;const t={page:this.currentPage,pageSize:this.pageSize};this.reportType&&(t.reportType=this.reportType),console.log("加载历史报告，参数:",t),(0,r.Vd)(t).then((t=>{console.log("历史报告结果:",t),t.is_success?(this.reportList=t.data.list||[],this.total=t.data.total||0,console.log(`成功加载${this.reportList.length}条历史报告，总计${this.total}条`),this.reportList.length>0?console.log("第一条记录:",JSON.stringify(this.reportList[0])):console.log("没有历史报告记录")):(console.error("加载历史报告失败:",t.message),this.$message.error(t.message||"加载历史报告失败"),this.reportList=[],this.total=0),this.loading=!1})).catch((t=>{console.error("加载历史报告失败:",t),this.$message.error("加载历史报告失败"),this.reportList=[],this.total=0,this.loading=!1}))},refreshList(){this.loadReportList()},selectReport(t,e){this.$emit("select",t,e)},handlePageChange(t){this.currentPage=t,this.loadReportList()},confirmDelete(t){this.$confirm("确定要删除这份报告吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((()=>{this.deleteReport(t.id)})).catch((()=>{}))},deleteReport(t){(0,r.deleteReportHistory)({id:t}).then((e=>{e.is_success?(this.$message.success("删除成功"),t===this.selectedReportId&&this.$emit("select",null,-1),this.loadReportList()):this.$message.error(e.message||"删除失败")})).catch((t=>{console.error("删除报告失败:",t),this.$message.error("删除报告失败")}))},getReportTypeName(t){const e={daily:"日报",weekly:"周报",monthly:"月报",summary:"总结"};return e[t]||"未知类型"}}},n=l;var o=s(1656),c=(0,o.A)(n,a,i,!1,null,"029a64c1",null);const d=c.exports},345:(t,e,s)=>{"use strict";s.r(e),s.d(e,{default:()=>h});var a=function(){var t=this,e=t._self._c;return e("div",{staticClass:"admin-stats-thesis"},[e("el-card",[e("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[e("span",[t._v("论文统计")]),e("div",{staticClass:"header-right"},[e("el-radio-group",{attrs:{size:"small"},on:{change:t.fetchThesisStats},model:{value:t.period,callback:function(e){t.period=e},expression:"period"}},[e("el-radio-button",{attrs:{label:"7d"}},[t._v("最近7天")]),e("el-radio-button",{attrs:{label:"30d"}},[t._v("最近30天")]),e("el-radio-button",{attrs:{label:"90d"}},[t._v("最近90天")])],1),e("el-button",{staticStyle:{"margin-left":"10px"},attrs:{size:"small",type:"primary",icon:"el-icon-refresh"},on:{click:t.fetchThesisStats}},[t._v(" 刷新 ")])],1)]),e("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}]},[e("div",{staticClass:"stat-cards"},[e("el-row",{attrs:{gutter:20}},[e("el-col",{attrs:{span:12}},[e("el-card",{staticClass:"stat-card",attrs:{shadow:"hover"}},[e("div",{staticClass:"stat-card-title"},[t._v("总论文数")]),e("div",{staticClass:"stat-card-value"},[t._v(t._s(t.statsData.total_thesis||0))])])],1),e("el-col",{attrs:{span:12}},[e("el-card",{staticClass:"stat-card",attrs:{shadow:"hover"}},[e("div",{staticClass:"stat-card-title"},[t._v("新增论文数")]),e("div",{staticClass:"stat-card-value"},[t._v(t._s(t.statsData.new_thesis||0))])])],1)],1)],1),e("div",{staticClass:"chart-container"},[e("el-row",{attrs:{gutter:20}},[e("el-col",{attrs:{span:24}},[e("el-card",{staticClass:"chart-card",attrs:{shadow:"hover"}},[e("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[e("span",[t._v("每日新增论文趋势")])]),e("div",{staticClass:"chart",staticStyle:{height:"300px"},attrs:{id:"daily-thesis-chart"}})])],1)],1),e("el-row",{staticStyle:{"margin-top":"20px"},attrs:{gutter:20}},[e("el-col",{attrs:{span:8}},[e("el-card",{staticClass:"chart-card",attrs:{shadow:"hover"}},[e("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[e("span",[t._v("论文语言分布")])]),e("div",{staticClass:"chart",staticStyle:{height:"300px"},attrs:{id:"lang-chart"}})])],1),e("el-col",{attrs:{span:8}},[e("el-card",{staticClass:"chart-card",attrs:{shadow:"hover"}},[e("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[e("span",[t._v("论文级别分布")])]),e("div",{staticClass:"chart",staticStyle:{height:"300px"},attrs:{id:"level-chart"}})])],1),e("el-col",{attrs:{span:8}},[e("el-card",{staticClass:"chart-card",attrs:{shadow:"hover"}},[e("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[e("span",[t._v("论文长度分布")])]),e("div",{staticClass:"chart",staticStyle:{height:"300px"},attrs:{id:"length-chart"}})])],1)],1),e("el-row",{staticStyle:{"margin-top":"20px"},attrs:{gutter:20}},[e("el-col",{attrs:{span:24}},[e("el-card",{attrs:{shadow:"hover"}},[e("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[e("span",[t._v("论文数据表格")])]),e("el-table",{staticStyle:{width:"100%"},attrs:{data:t.tableData}},[e("el-table-column",{attrs:{prop:"date",label:"日期",width:"180"}}),e("el-table-column",{attrs:{prop:"count",label:"新增论文数"}})],1)],1)],1)],1)],1)])])],1)},i=[],r=s(9192),l=s(9393);const n={name:"AdminStatsThesis",data(){return{loading:!1,period:"7d",statsData:{},tableData:[],charts:{dailyThesisChart:null,langChart:null,levelChart:null,lengthChart:null}}},mounted(){this.fetchThesisStats(),window.addEventListener("resize",this.resizeCharts)},beforeDestroy(){window.removeEventListener("resize",this.resizeCharts),Object.keys(this.charts).forEach((t=>{this.charts[t]&&this.charts[t].dispose()}))},methods:{async fetchThesisStats(){this.loading=!0;try{const t=await r.bk.getThesisStats({period:this.period});t.success?(this.statsData=t.data,this.tableData=this.statsData.daily_stats||[],this.$nextTick((()=>{this.initCharts()}))):this.$message.error(t.message||"获取论文统计数据失败")}catch(t){this.$message.error("获取论文统计数据失败："+t.message)}finally{this.loading=!1}},initCharts(){this.initDailyThesisChart(),this.initLangChart(),this.initLevelChart(),this.initLengthChart()},initDailyThesisChart(){const t=document.getElementById("daily-thesis-chart");if(!t)return;this.charts.dailyThesisChart&&this.charts.dailyThesisChart.dispose(),this.charts.dailyThesisChart=l.init(t);const e=this.statsData.daily_stats||[],s=e.map((t=>t.date)),a=e.map((t=>t.count)),i={tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:[{type:"category",data:s,axisLabel:{rotate:45}}],yAxis:[{type:"value"}],series:[{name:"新增论文",data:a,type:"bar",itemStyle:{color:"#67C23A"}}]};this.charts.dailyThesisChart.setOption(i)},initLangChart(){const t=document.getElementById("lang-chart");if(!t)return;this.charts.langChart&&this.charts.langChart.dispose(),this.charts.langChart=l.init(t);const e=this.statsData.lang_stats||[],s=e.map((t=>({name:t.lang||"未知",value:t.count}))),a={tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {c} ({d}%)"},legend:{orient:"vertical",right:10,top:"center",data:s.map((t=>t.name))},series:[{name:"语言分布",type:"pie",radius:["50%","70%"],avoidLabelOverlap:!1,label:{show:!1,position:"center"},emphasis:{label:{show:!0,fontSize:"16",fontWeight:"bold"}},labelLine:{show:!1},data:s}]};this.charts.langChart.setOption(a)},initLevelChart(){const t=document.getElementById("level-chart");if(!t)return;this.charts.levelChart&&this.charts.levelChart.dispose(),this.charts.levelChart=l.init(t);const e=this.statsData.level_stats||[],s=e.map((t=>({name:t.level||"未知",value:t.count}))),a={tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {c} ({d}%)"},legend:{orient:"vertical",right:10,top:"center",data:s.map((t=>t.name))},series:[{name:"级别分布",type:"pie",radius:["50%","70%"],avoidLabelOverlap:!1,label:{show:!1,position:"center"},emphasis:{label:{show:!0,fontSize:"16",fontWeight:"bold"}},labelLine:{show:!1},data:s}]};this.charts.levelChart.setOption(a)},initLengthChart(){const t=document.getElementById("length-chart");if(!t)return;this.charts.lengthChart&&this.charts.lengthChart.dispose(),this.charts.lengthChart=l.init(t);const e=this.statsData.length_stats||[],s=e.map((t=>({name:t.length?`${t.length}字`:"未知",value:t.count}))),a={tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {c} ({d}%)"},legend:{orient:"vertical",right:10,top:"center",data:s.map((t=>t.name))},series:[{name:"长度分布",type:"pie",radius:["50%","70%"],avoidLabelOverlap:!1,label:{show:!1,position:"center"},emphasis:{label:{show:!0,fontSize:"16",fontWeight:"bold"}},labelLine:{show:!1},data:s}]};this.charts.lengthChart.setOption(a)},resizeCharts(){Object.keys(this.charts).forEach((t=>{this.charts[t]&&this.charts[t].resize()}))}}},o=n;var c=s(1656),d=(0,c.A)(o,a,i,!1,null,"a119913e",null);const h=d.exports},387:(t,e,s)=>{"use strict";s.r(e),s.d(e,{default:()=>p});var a=function(){var t=this,e=t._self._c;return e("div",{staticClass:"report-container"},[t._m(0),e("div",{staticClass:"report-content"},[e("div",{staticClass:"report-history-sidebar"},[e("ReportHistoryList",{ref:"historyList",attrs:{selectedReportId:t.selectedHistoryId,reportType:t.getReportTypeKey(t.selectedType)},on:{select:t.selectHistoryReport}})],1),e("div",{staticClass:"report-main"},[!t.reportResult||t.isEditing?e("div",{staticClass:"form-container"},[e("div",{staticClass:"form-header"},[e("h3",[t._v(t._s(t.reportTypes[t.selectedType].name))]),e("p",{staticClass:"description"},[t._v(t._s(t.reportTypes[t.selectedType].description))])]),e("el-form",{staticClass:"report-form",attrs:{model:t.formData,"label-width":"100px"}},[e("el-form-item",{attrs:{label:"报告主题"}},[e("el-input",{attrs:{placeholder:"请输入报告主题，如项目名称、工作内容等"},model:{value:t.formData.title,callback:function(e){t.$set(t.formData,"title",e)},expression:"formData.title"}})],1),e("el-form-item",{attrs:{label:"工作内容"}},[e("el-input",{attrs:{type:"textarea",rows:4,placeholder:"请简要描述您的工作内容，关键词或要点即可，AI将自动扩展"},model:{value:t.formData.content,callback:function(e){t.$set(t.formData,"content",e)},expression:"formData.content"}})],1),e("el-form-item",{attrs:{label:"报告风格"}},[e("el-select",{attrs:{placeholder:"请选择报告风格"},model:{value:t.formData.style,callback:function(e){t.$set(t.formData,"style",e)},expression:"formData.style"}},[e("el-option",{attrs:{label:"简洁明了",value:"concise"}}),e("el-option",{attrs:{label:"详细专业",value:"detailed"}}),e("el-option",{attrs:{label:"成果导向",value:"achievement"}}),e("el-option",{attrs:{label:"问题分析",value:"problem"}})],1)],1),0===t.selectedType?[e("el-form-item",{attrs:{label:"日期"}},[e("el-date-picker",{attrs:{type:"date",placeholder:"选择日期",format:"yyyy-MM-dd","value-format":"yyyy-MM-dd"},model:{value:t.formData.date,callback:function(e){t.$set(t.formData,"date",e)},expression:"formData.date"}})],1)]:t._e(),1===t.selectedType?[e("el-form-item",{attrs:{label:"周期"}},[e("el-date-picker",{attrs:{type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"yyyy-MM-dd","value-format":"yyyy-MM-dd"},model:{value:t.formData.dateRange,callback:function(e){t.$set(t.formData,"dateRange",e)},expression:"formData.dateRange"}})],1),e("el-form-item",{attrs:{label:"重点项目"}},[e("el-input",{attrs:{placeholder:"请输入本周重点项目"},model:{value:t.formData.keyProject,callback:function(e){t.$set(t.formData,"keyProject",e)},expression:"formData.keyProject"}})],1)]:t._e(),2===t.selectedType?[e("el-form-item",{attrs:{label:"月份"}},[e("el-date-picker",{attrs:{type:"month",placeholder:"选择月份",format:"yyyy-MM","value-format":"yyyy-MM"},model:{value:t.formData.month,callback:function(e){t.$set(t.formData,"month",e)},expression:"formData.month"}})],1),e("el-form-item",{attrs:{label:"部门/团队"}},[e("el-input",{attrs:{placeholder:"请输入您的部门或团队名称"},model:{value:t.formData.department,callback:function(e){t.$set(t.formData,"department",e)},expression:"formData.department"}})],1),e("el-form-item",{attrs:{label:"KPI完成度"}},[e("el-slider",{attrs:{step:5,"show-stops":""},model:{value:t.formData.kpiCompletion,callback:function(e){t.$set(t.formData,"kpiCompletion",e)},expression:"formData.kpiCompletion"}})],1)]:t._e(),3===t.selectedType?[e("el-form-item",{attrs:{label:"总结类型"}},[e("el-select",{attrs:{placeholder:"请选择总结类型"},model:{value:t.formData.summaryType,callback:function(e){t.$set(t.formData,"summaryType",e)},expression:"formData.summaryType"}},[e("el-option",{attrs:{label:"项目总结",value:"project"}}),e("el-option",{attrs:{label:"季度总结",value:"quarter"}}),e("el-option",{attrs:{label:"半年总结",value:"halfyear"}}),e("el-option",{attrs:{label:"年度总结",value:"annual"}})],1)],1),e("el-form-item",{attrs:{label:"成果亮点"}},[e("el-input",{attrs:{type:"textarea",rows:3,placeholder:"请列出主要成果和亮点"},model:{value:t.formData.achievements,callback:function(e){t.$set(t.formData,"achievements",e)},expression:"formData.achievements"}})],1),e("el-form-item",{attrs:{label:"问题与改进"}},[e("el-input",{attrs:{type:"textarea",rows:3,placeholder:"请列出存在的问题和改进方向"},model:{value:t.formData.improvements,callback:function(e){t.$set(t.formData,"improvements",e)},expression:"formData.improvements"}})],1)]:t._e(),e("el-form-item",[e("el-button",{attrs:{type:"primary",loading:t.isGenerating},on:{click:t.generateReport}},[t._v("生成报告")]),e("el-button",{on:{click:t.resetForm}},[t._v("重置")])],1)],2)],1):t._e(),t.reportResult?e("div",{staticClass:"result-container"},[e("div",{staticClass:"result-header"},[e("h3",[t._v(t._s(t.currentReportTitle))]),e("div",{staticClass:"result-actions"},[e("el-button",{attrs:{size:"small",type:"primary"},on:{click:t.toggleEditMode}},[t._v(" "+t._s(t.isEditing?"预览报告":"编辑报告")+" ")]),e("el-button",{attrs:{size:"small",type:"success"},on:{click:t.saveReportManually}},[t._v(" 保存报告 ")]),e("el-button",{attrs:{size:"small",type:"primary",icon:"el-icon-document-copy"},on:{click:t.copyReport}},[t._v("复制")]),e("el-dropdown",{attrs:{trigger:"click"},on:{command:t.handleDownload}},[e("el-button",{attrs:{size:"small",type:"success",icon:"el-icon-download"}},[t._v(" 下载 "),e("i",{staticClass:"el-icon-arrow-down el-icon--right"})]),e("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[e("el-dropdown-item",{attrs:{command:"markdown"}},[t._v("Markdown格式")]),e("el-dropdown-item",{attrs:{command:"html"}},[t._v("HTML格式")])],1)],1)],1)]),t._e(),t.isEditing?e("div",{staticClass:"source-edit"},[e("el-input",{attrs:{type:"textarea",rows:20,resize:"none"},model:{value:t.reportResult,callback:function(e){t.reportResult=e},expression:"reportResult"}})],1):e("div",{staticClass:"markdown-view-container"},[e("MarkdownRenderer",{staticClass:"markdown-view",attrs:{content:t.reportResult}})],1)]):t._e()]),e("div",{staticClass:"report-sidebar"},[e("div",{staticClass:"report-types"},t._l(t.reportTypes,(function(s,a){return e("div",{key:a,class:["report-type-item",t.selectedType===a?"active":""],on:{click:function(e){return t.selectReportType(a)}}},[e("i",{class:s.icon}),e("span",[t._v(t._s(s.name))])])})),0)])])])},i=[function(){var t=this,e=t._self._c;return e("div",{staticClass:"report-header"},[e("h2",[t._v("报告生成助手")]),e("p",{staticClass:"subtitle"},[t._v("快速生成各类工作报告，提高工作效率")])])}],r=s(6960),l=s(529),n=s(222);s(23);const o={name:"ReportGenerator",components:{MarkdownRenderer:l["default"],ReportHistoryList:n["default"]},data(){return{selectedType:0,isEditing:!1,selectedHistoryId:-1,historyReports:[],reportTypes:[{name:"日报生成",icon:"el-icon-date",description:"快速生成每日工作报告，记录日常工作内容和成果"},{name:"周报生成",icon:"el-icon-document",description:"汇总一周工作内容，突出重点项目进展和成果"},{name:"月报生成",icon:"el-icon-collection",description:"全面总结月度工作，分析成果与不足，提出下月计划"},{name:"总结生成",icon:"el-icon-data-analysis",description:"项目、季度、年度等各类总结报告，全面分析成果与经验"}],formData:{title:"",content:"",style:"concise",date:(new Date).toISOString().split("T")[0],dateRange:[new Date((new Date).setDate((new Date).getDate()-7)).toISOString().split("T")[0],(new Date).toISOString().split("T")[0]],keyProject:"",month:(new Date).toISOString().slice(0,7),department:"",kpiCompletion:80,summaryType:"project",achievements:"",improvements:""},isGenerating:!1,reportResult:""}},computed:{currentReportTitle(){return this.reportResult,this.formData.title||"生成结果"}},mounted(){const t=this.$route.query.type;if(t)switch(t){case"daily":this.selectedType=0;break;case"weekly":this.selectedType=1;break;case"monthly":this.selectedType=2;break;case"summary":this.selectedType=3;break}this.loadHistoryReports()},watch:{"$route.query.type"(t){if(t)switch(t){case"daily":this.selectedType=0;break;case"weekly":this.selectedType=1;break;case"monthly":this.selectedType=2;break;case"summary":this.selectedType=3;break}}},methods:{loadHistoryReports(){(0,r.Vd)().then((t=>{t.is_success?this.historyReports=t.data||[]:this.$message.error("加载报告历史失败")})).catch((t=>{console.error("加载历史报告失败:",t),this.$message.error("加载报告历史失败")}))},selectHistoryReport(t,e){if(!t)return this.selectedHistoryId=-1,void(this.reportResult="");this.selectedHistoryId=t.id;try{const e=t.form_data||{};this.formData={...e}}catch(i){console.error("解析表单数据失败:",i),this.formData={}}this.reportResult=t.content||"";const s=["daily","weekly","monthly","summary"],a=s.indexOf(t.report_type);a>=0&&(this.selectedType=a),this.isEditing=!1},selectReportType(t){this.selectedType=t,this.selectedHistoryId=-1,this.reportResult="",this.isEditing=!1},toggleEditMode(){this.isEditing=!this.isEditing},generateReport(){if(!this.validateForm())return;const t=this.$loading({lock:!0,text:"正在生成报告...",spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"}),e=this.getReportData();(0,r.oh)({reportType:this.getReportTypeString(),reportData:e}).then((e=>{t.close(),e.is_success?(this.reportResult=e.data.content||e.data,this.isEditing=!1,this.loadHistoryReports(),e.data.reportId&&(this.selectedHistoryId=e.data.reportId),this.$message.success("报告生成成功！")):this.$message.error(e.message||"生成报告失败")})).catch((e=>{t.close(),console.error("生成报告失败:",e),this.$message.error("生成报告失败，请重试")}))},saveReportToHistory(t,e){if(!e||""===e.trim())return console.error("报告内容为空，无法保存"),void this.$message.error("报告内容为空，无法保存");const s={reportType:t,reportData:this.formData,reportContent:e};console.log("保存历史记录，参数:",s),console.log("报告内容长度:",e?e.length:0);const a=this.$loading({lock:!0,text:"正在保存报告...",spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"});(0,r.wy)(s).then((t=>{a.close(),t.is_success?(console.log("保存历史记录成功:",t.data),this.selectedHistoryId=t.data.id,this.$refs.historyList&&this.$refs.historyList.refreshList(),this.$message.success("报告已保存到历史记录")):(console.error("保存历史记录失败:",t.message),this.$message.error(t.message||"保存历史记录失败"))})).catch((t=>{a.close(),console.error("保存历史记录出错:",t),this.$message.error("保存历史记录失败，请稍后重试")}))},resetForm(){this.formData={title:"",content:"",style:"concise",date:(new Date).toISOString().split("T")[0],dateRange:[new Date((new Date).setDate((new Date).getDate()-7)).toISOString().split("T")[0],(new Date).toISOString().split("T")[0]],keyProject:"",month:(new Date).toISOString().slice(0,7),department:"",kpiCompletion:80,summaryType:"project",achievements:"",improvements:""},this.reportResult="",this.selectedHistoryId=-1,this.isEditing=!1},copyReport(){if(!this.reportResult)return;const t=document.createElement("textarea");t.value=this.reportResult,document.body.appendChild(t),t.select();try{document.execCommand("copy"),this.$message.success("报告已复制到剪贴板")}catch(e){this.$message.error("复制失败，请手动复制")}document.body.removeChild(t)},handleDownload(t){"markdown"===t?this.downloadMarkdown():"html"===t&&this.downloadHtml()},downloadMarkdown(){if(!this.reportResult)return;const t=this.reportTypes[this.selectedType].name,e=`${t.replace(/生成$/,"")}_${(new Date).toISOString().slice(0,10)}.md`,s=new Blob([this.reportResult],{type:"text/markdown"}),a=URL.createObjectURL(s),i=document.createElement("a");i.href=a,i.download=e,document.body.appendChild(i),i.click(),setTimeout((()=>{document.body.removeChild(i),URL.revokeObjectURL(a)}),0)},downloadHtml(){if(this.reportResult)try{const t=this.$loading({lock:!0,text:"正在生成HTML文档...",spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"}),e={reportType:["daily","weekly","monthly","summary"][this.selectedType],reportData:{...this.formData},reportContent:this.reportResult};console.log("导出HTML文档，参数:",e),(0,r.Sk)(e).then((e=>{if(console.log("导出HTML结果:",e),e.is_success){let s=document.createElement("a");s.style.display="none",s.href="/api/thesis/download?fileName="+e.data.file,document.body.appendChild(s),s.click(),setTimeout((()=>{document.body.removeChild(s),t.close(),this.$notify({title:"导出成功",message:"报告已成功导出为HTML格式",type:"success",duration:3e3})}),100)}else t.close(),this.$message.error(e.message||"导出HTML文档失败")})).catch((e=>{console.error("导出HTML文档失败:",e),t.close(),this.$message.error("导出HTML文档失败，请稍后重试")}))}catch(t){console.error("生成HTML文档失败:",t),this.$message.error("生成HTML文档失败，请稍后重试")}},downloadReport(){this.downloadMarkdown()},updateReport(){if(!this.reportResult||this.selectedHistoryId<=0)return void this.$message.warning("没有可更新的报告");const t=["daily","weekly","monthly","summary"],e=t[this.selectedType],s={id:this.selectedHistoryId,reportType:e,reportData:this.formData,reportContent:this.reportResult};console.log("更新报告，参数:",s),(0,r.wy)(s).then((t=>{t.is_success?(console.log("更新报告成功:",t.data),this.$refs.historyList&&this.$refs.historyList.refreshList(),this.$message.success("报告已更新"),this.isEditing=!1):(console.error("更新报告失败:",t.message),this.$message.error(t.message||"更新报告失败"))})).catch((t=>{console.error("更新报告出错:",t),this.$message.error("更新报告失败，请稍后重试")}))},saveReportManually(){if(!this.reportResult)return void this.$message.warning("没有可保存的报告内容");const t=["daily","weekly","monthly","summary"],e=t[this.selectedType],s={reportType:e,reportData:this.formData,reportContent:this.reportResult};this.selectedHistoryId>0&&(s.id=this.selectedHistoryId),console.log("保存报告，参数:",s),(0,r.wy)(s).then((t=>{t.is_success?(console.log("保存报告成功:",t.data),this.selectedHistoryId=t.data.id,this.$refs.historyList&&this.$refs.historyList.refreshList(),this.$message.success("报告已保存到历史记录"),this.isEditing=!1):(console.error("保存报告失败:",t.message),this.$message.error(t.message||"保存报告失败"))})).catch((t=>{console.error("保存报告出错:",t),this.$message.error("保存报告失败，请稍后重试")}))},getReportTypeKey(t){const e=["daily","weekly","monthly","summary"];return e[t]||"daily"},deleteHistoryReport(t){this.$confirm("确定要删除这份报告吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((()=>{(0,r.y8)({reportId:t.id}).then((e=>{e.is_success?(this.$message.success("删除成功"),this.loadHistoryReports(),this.selectedHistoryId===t.id&&(this.selectedHistoryId=-1,this.reportResult="")):this.$message.error(e.message||"删除失败")})).catch((t=>{this.$message.error("删除失败")}))})).catch((()=>{}))},validateForm(){return!(!this.formData.title||!this.formData.content)||(this.$message.warning("请填写报告主题和工作内容"),!1)},getReportData(){return{...this.formData}},getReportTypeString(){const t=["daily","weekly","monthly","summary"];return t[this.selectedType]||"daily"}}},c=o;var d=s(1656),h=(0,d.A)(c,a,i,!1,null,"f79ac7e6",null);const p=h.exports},529:(t,e,s)=>{"use strict";s.r(e),s.d(e,{default:()=>d});var a=function(){var t=this,e=t._self._c;return e("div",{staticClass:"markdown-content",domProps:{innerHTML:t._s(t.renderedContent)}})},i=[],r=s(23);const l={name:"MarkdownRenderer",props:{content:{type:String,default:""}},computed:{renderedContent(){if(!this.content)return"";try{let t=this.content;t=t.replace(/```markdown\s*\n/g,""),t=t.replace(/\n\s*```\s*$/g,"");const e=new r.xI.Renderer,s={renderer:e,gfm:!0,breaks:!0,headerIds:!0,mangle:!1},a=r.xI.parse(t,s);return`<div class="markdown-body">${a}</div>`}catch(t){return console.error("Markdown渲染错误:",t),`<div class="error">Markdown渲染错误: ${t.message}</div>`}}},mounted(){console.log("MarkdownRenderer mounted, content:",this.content?this.content.substring(0,50)+"...":"empty")}},n=l;var o=s(1656),c=(0,o.A)(n,a,i,!1,null,null,null);const d=c.exports},1208:(t,e,s)=>{"use strict";s.r(e),s.d(e,{default:()=>b});var a=function(){var t=this,e=t._self._c;return e("div",{staticClass:"flex"},[e("div",{staticClass:"home-main grid-content bg-purple-light"},[t.isLoggedIn?e("div",{staticClass:"vip-notice"},[e("div",{staticClass:"notice-content"},[e("div",{staticClass:"notice-icon"},[e("i",{class:t.isVip?"el-icon-star-on":"el-icon-star-off"})]),e("div",{staticClass:"notice-text"},[e("div",{staticClass:"notice-title"},[t._v(" "+t._s(t.isVip?"VIP会员":"免费用户")+" ")]),e("div",{staticClass:"notice-desc"},[t._v(" "+t._s(t.isVip?"您当前是VIP会员，可以创建最多5篇论文":"您当前是免费用户，可以创建保存最多1篇论文，如果您需要多篇论文，可以先删除后再生成")+" ")]),t.isVip?t._e():e("div",{staticClass:"notice-desc"},[t._v(" VIP用户，可生成最长 10 个章节 ")]),t.isVip?e("div",{staticClass:"notice-usage"},[e("span",{staticClass:"usage-text"},[t._v("已创建："+t._s(t.thesisCount)+" / 5")]),e("el-progress",{staticClass:"usage-progress",attrs:{percentage:t.thesisCount/5*100,"stroke-width":6,"show-text":!1}})],1):e("div",{staticClass:"notice-usage"},[e("span",{staticClass:"usage-text"},[t._v("已创建："+t._s(t.thesisCount)+" / 1")]),e("el-progress",{staticClass:"usage-progress",attrs:{percentage:100*t.thesisCount,"stroke-width":6,"show-text":!1}})],1)]),t.isVip?t._e():e("div",{staticClass:"notice-action"},[e("el-button",{attrs:{type:"warning",size:"small"},on:{click:t.showUpgradeInfo}},[t._v(" 升级VIP ")])],1)])]):t._e(),e("el-form",{ref:"form",staticClass:"form-box",attrs:{inline:!0,model:t.form}},[e("el-form-item",{attrs:{label:t.$t("outlinePage.title"),required:""}},[e("el-input",{staticStyle:{width:"400px"},attrs:{placeholder:"请填写论文标题"},model:{value:t.form.title,callback:function(e){t.$set(t.form,"title",e)},expression:"form.title"}})],1),e("el-form-item",{attrs:{label:t.$t("outlinePage.level"),required:""}},[e("el-radio-group",{attrs:{size:"small"},model:{value:t.form.level,callback:function(e){t.$set(t.form,"level",e)},expression:"form.level"}},[e("el-radio-button",{attrs:{label:t.$t("education.中学")}}),e("el-radio-button",{attrs:{label:t.$t("education.大专")}}),e("el-radio-button",{attrs:{label:t.$t("education.本科")}}),e("el-radio-button",{attrs:{label:t.$t("education.硕士")}}),e("el-radio-button",{attrs:{label:t.$t("education.博士")}})],1)],1),e("el-form-item",{attrs:{label:t.$t("outlinePage.lang"),required:""}},[e("el-radio-group",{attrs:{size:"small"},model:{value:t.form.lang,callback:function(e){t.$set(t.form,"lang",e)},expression:"form.lang"}},t._l(t.$t("languageList"),(function(s){return e("el-radio-button",{key:s,attrs:{label:s}},[t._v(t._s(s))])})),1)],1),e("el-form-item",{attrs:{label:"一级段落数量",required:""}},[e("el-input-number",{attrs:{"controls-position":"right",min:1,max:12,step:1},model:{value:t.form.paragraphCount,callback:function(e){t.$set(t.form,"paragraphCount",e)},expression:"form.paragraphCount"}})],1),e("el-form-item",{attrs:{label:"每段中小标题的数量",required:""}},[e("el-input-number",{attrs:{"controls-position":"right",min:1,max:10,step:1},model:{value:t.form.secondParagraphCount,callback:function(e){t.$set(t.form,"secondParagraphCount",e)},expression:"form.secondParagraphCount"}})],1),e("el-form-item",{staticStyle:{"margin-bottom":"0"}},[e("el-button",{staticClass:"xiaolong-btn",attrs:{type:"primary",size:"medium"},on:{click:t.onSubmit}},[t._v(t._s(t.$t("outlinePage.btnSubmit")))])],1)],1),t.outlines&&t.outlines.length>0&&t.showHistory?e("div",{staticClass:"history-notice"},[e("i",{staticClass:"el-icon-time"}),t._v(" 正在显示您的历史提纲记录，您可以重新生成新的提纲 ")]):t._e(),e("div",{staticClass:"result-box"},t._l(t.outlines,(function(s,a){return e("div",{key:s.id,staticClass:"result-item"},[e("div",{staticClass:"outline-title"},[e("span",{staticClass:"t"},[t._v(" "+t._s(t.thesisType[a])+" ")]),e("el-button",{staticClass:"xiaolong-btn",attrs:{type:"primary"},on:{click:function(e){return t.selectForContent(a)}}},[t._v(" "+t._s(t.$t("outlinePage.btnSelectOutline"))+" ")]),e("el-button",{staticClass:"xiaolong-btn",attrs:{type:"primary"},on:{click:t.onSubmit}},[t._v(" "+t._s(t.$t("outlinePage.btnReSubmit"))+" ")]),e("el-button",{staticClass:"xiaolong-btn",attrs:{type:"primary"},on:{click:function(e){return t.downloadOutline(a)}}},[t._v(" 下载 ")])],1),e("div",{staticClass:"outline-box",attrs:{contenteditable:"true"},domProps:{innerHTML:t._s(s)}})])})),0),t.outlines&&0!=t.outlines.length?t._e():e("Placeholder")],1),e("div",{staticClass:"home-sidebar grid-content bg-purple"},[e("PageLeftBox")],1)])},i=[],r=s(6960),l=s(1052),n=s(9845),o=s(9171),c=s(6878),d=s(5353);const h="lastTitle",p="lastOutline",u="lastTitleForm",m={name:"GetOutline",components:{PageLeftBox:n["default"],Placeholder:o["default"]},computed:{...(0,d.L8)("user",["userInfo","isLoggedIn","isVip"]),thesisCount(){return this.userInfo&&this.userInfo.thesis_count||0}},data(){return{thesisType:["研究型","数据型","严谨型"],outlines:[],rawOutlines:[],Self_Define_Outline:"",form:{title:"",level:"本科",length:"4000",lang:"中文",paragraphCount:6,secondParagraphCount:3},historyOutlines:[],showHistory:!1}},created(){this.loadUserHistory();let t=localStorage.getItem(h);if(t){const e=(0,c.jD)(t);e?"English"==this.form.lang?this.form.title=e["tt"]:this.form.title=e["kk"]:localStorage.removeItem(h)}let e=localStorage.getItem(u);if(e){const t=(0,c.jD)(e);t?(this.form.lang=t["lang"],this.form.level=t["level"]):localStorage.removeItem(u)}let s=localStorage.getItem(p);if(s&&0===this.outlines.length){const t=(0,c.jD)(s);t?(this.outlines=t["html"],this.rawOutlines=t["raw"]):localStorage.removeItem(p)}},methods:{openLoginBox(){},closeLoginBox(){},showUpgradeInfo(){this.$router.push({path:"/my/userrank"})},generateHtml(t,e=1,s=""){let a="";for(let i of t.keys()){let r=i+1,l=t[i];a+=1==e?`<div class='title-level1'><i>${s}第${r}章</i> ${l["title"]}</div>`:`<div class='title-level${e}'><i>${s}${r}</i> ${l["title"]}</div>`,l["subtitle"]&&Array.isArray(l["subtitle"])&&l["subtitle"].length>0&&(a+=this.generateHtml(l["subtitle"],e+1,`${s}${r}.`))}return a},handleGotoUserRank(){this.$router.push({path:"/my/userrank"})},downloadOutline(t){let e={title:this.form.title,level:this.form.level,length:this.form.length,lang:this.form.lang,outline:this.rawOutlines[t]};(0,r.js)(e).then((t=>{console.log(t)})).catch((t=>{console.log(t)}))},loadUserHistory(){(0,r.Xr)().then((t=>{if(t.is_success&&t.data&&t.data.length>0&&(this.historyOutlines=t.data,0===this.outlines.length)){const e=t.data[0];if(e.form_data&&(this.form.title=e.form_data.title||"",this.form.level=e.form_data.level||"本科",this.form.lang=e.form_data.lang||"中文",this.form.length=e.form_data.length||"4000",this.form.paragraphCount=e.form_data.paragraphCount||6,this.form.secondParagraphCount=e.form_data.secondParagraphCount||3),e.raw_outlines){this.rawOutlines=e.raw_outlines;let t=[];for(let s in e.raw_outlines){let a=e.raw_outlines[s],i=this.generateHtml(a["subtitle"],1);t.push(i)}this.outlines=t}}})).catch((t=>{console.error("加载历史大纲失败:",t)}))},selectHistoryOutline(t){if(t.form_data&&(this.form.title=t.form_data.title||"",this.form.level=t.form_data.level||"本科",this.form.lang=t.form_data.lang||"中文",this.form.length=t.form_data.length||"4000",this.form.paragraphCount=t.form_data.paragraphCount||6,this.form.secondParagraphCount=t.form_data.secondParagraphCount||3),t.raw_outlines){this.rawOutlines=t.raw_outlines;let e=[];for(let s in t.raw_outlines){let a=t.raw_outlines[s],i=this.generateHtml(a["subtitle"],1);e.push(i)}this.outlines=e}this.showHistory=!1},onSubmit(){if(""==this.form.title)return this.$message({type:"error",message:"请填写你选定的论文题目，论文的题目不能为空"}),!1;let t=this.$loading({text:"正在构建论文框架，生成专业提纲...",background:"#00000033"});localStorage.removeItem(p),(0,r.be)(this.form).then((e=>{if(t.close(),!e.is_success)return void this.$message({type:"error",message:e.message});let s=[];for(let t in e.data){let a=e.data[t],i=this.generateHtml(a["subtitle"],1);s.push(i)}this.rawOutlines=e.data,this.outlines=s,this.showHistory=!1,localStorage.setItem(p,JSON.stringify({raw:e.data,html:s}))})).catch((e=>{t.close(),this.$notify.error("遇到错误："+e)}))},selectForContent(t){this.checkExistingThesis().then((e=>{e?this.$confirm("您已经有一篇正在进行中的论文，继续操作将覆盖当前论文。是否继续?","提示",{confirmButtonText:"继续",cancelButtonText:"取消",type:"warning"}).then((()=>{this.processOutlineSelection(t)})).catch((()=>{this.$message({type:"info",message:"已取消操作"})})):this.processOutlineSelection(t)})).catch((e=>{console.error("检查现有论文时出错:",e),this.processOutlineSelection(t)}))},async checkExistingThesis(){try{const t=await(0,r.up)();if(t&&t.is_success){const e=t.data;if(console.log("检查论文状态结果:",e),e.thesis_count>0&&!e.can_create){const t=e.is_vip?`您已创建 ${e.thesis_count} 篇论文，达到上限 ${e.max_thesis}。请先删除部分论文再继续。`:"免费用户最多只能创建一篇论文，请先删除已有论文再继续。";this.$notify.warning({title:"提示",message:t,duration:5e3})}return e.has_thesis}return!1}catch(t){return console.error("检查现有论文失败:",t),!1}},processOutlineSelection(t){if(t<0||t>=this.rawOutlines.length)return void this.$notify.error("提纲索引无效，请重新选择");if(!this.rawOutlines[t]||"object"!==typeof this.rawOutlines[t])return void this.$notify.error("提纲数据无效，请重新生成提纲");console.log(`选择第${t+1}个提纲，数据结构:`,JSON.stringify(this.rawOutlines[t])),console.log("提纲类型:",typeof this.rawOutlines[t]);let e=JSON.parse(JSON.stringify(this.rawOutlines[t]));try{if(!e||"object"!==typeof e||Array.isArray(e))return console.error("提纲数据不是有效的对象:",e),void this.$notify.error("提纲数据格式错误，请重新生成提纲");if(e.title||(console.log("提纲缺少title字段，使用论文标题"),e.title=this.form.title),!e.subtitle||!Array.isArray(e.subtitle)){console.log("提纲缺少有效的subtitle字段，尝试修复");const t=["children","subheadings","sections","chapters","content"];let s=!1;for(const a of t)if(e[a]&&Array.isArray(e[a])){console.log(`使用${a}字段作为subtitle`),e.subtitle=e[a],s=!0;break}s||(console.log("未找到有效的subtitle字段，创建空数组"),e.subtitle=[])}const t=e=>{if(!e||"object"!==typeof e)return{id:this.generateRandomId(),title:"无效节点",subtitle:[]};if(e.id||(e.id=this.generateRandomId()),!e.title){const t=["name","heading","text"];let s=!1;for(const a of t)if(e[a]){e.title=e[a],s=!0;break}s||(e.title="无标题章节")}return e.subtitle?Array.isArray(e.subtitle)||("object"===typeof e.subtitle?e.subtitle=[e.subtitle]:e.subtitle=[]):e.subtitle=[],e.subtitle=e.subtitle.map((e=>t(e))),e};Array.isArray(e.subtitle)&&(e.subtitle=e.subtitle.map((e=>t(e)))),console.log("处理后的提纲数据:",JSON.stringify(e));let s={title:this.form.title,level:this.form.level,length:this.form.length,lang:this.form.lang,outline:e};console.log("发送到后端的提纲数据:",JSON.stringify(s));let a=0,i=l.Loading.service({text:"正在准备生成论文内容...",background:"rgba(255, 255, 255, 0.6)",customClass:"loading-with-percentage"});const n=()=>{a<90&&(a+=10*Math.random(),a>90&&(a=90),i.setText(`正在准备生成论文内容...<br><span class="loading-percentage">${Math.floor(a)}%</span>`))},o=setInterval(n,300);(0,r.UT)(s).then((t=>{clearInterval(o),t.is_success?(a=100,i.setText(`正在准备生成论文内容...<br><span class="loading-percentage">${a}%</span>`),setTimeout((()=>{i.close(),this.$router.push({path:"/paper/content",query:{thesisId:t.data.thesisId}})}),200)):(i.close(),this.$notify.error("服务器错误，请稍后重试："+t.message))})).catch((t=>{if(clearInterval(o),i.close(),console.error("请求失败详情:",t),"string"===typeof t)this.$notify({title:"提示",message:t,type:"warning",duration:5e3});else if(t.response&&400===t.response.status){let e="请求参数错误";t.response.data&&t.response.data.message&&(e=t.response.data.message),e.includes("免费用户最多只能创建一篇论文")?this.$notify({title:"提示",message:"免费用户最多只能创建一篇论文，请先删除已有论文再继续",type:"warning",duration:5e3}):this.$notify({title:"提示",message:"您已经选择了一个提纲生成论文，请先完成或删除当前论文再选择新的提纲",type:"warning",duration:5e3})}else this.$notify.error("请求失败，请稍后重试")}))}catch(s){console.error("处理提纲数据时出错:",s),this.$notify.error("处理提纲数据时出错，请重新生成提纲")}},generateRandomId(){return Math.random().toString(36).substring(2,15)+Math.random().toString(36).substring(2,15)}}},g=m;var f=s(1656),v=(0,f.A)(g,a,i,!1,null,null,null);const b=v.exports},1574:(t,e,s)=>{"use strict";s.r(e),s.d(e,{default:()=>c});var a=function(){var t=this,e=t._self._c;return e("div",{staticClass:"page"},[e("div",{staticClass:"page_title"},[t._v(t._s(t.title))]),""!=t.content&&void 0!=t.content?e("div",{staticClass:"page_notice"},[t._v(t._s(t.notice)+"(下载word文件中无此警告)")]):t._e(),t.isEditDialogOpen?t._e():e("div",{staticClass:"page_body"},[t._v(t._s(t.content))]),t.isEditDialogOpen?e("div",{staticClass:"page_edit-form"},[e("el-form",[e("el-form-item",{attrs:{label:"内容修改"}},[e("el-input",{attrs:{type:"textarea"},model:{value:t.editableContent,callback:function(e){t.editableContent=e},expression:"editableContent"}})],1),e("el-form-item",[e("el-button",{on:{click:function(e){t.isEditDialogOpen=!1}}},[t._v("取消")]),e("el-button",{attrs:{type:"primary"},on:{click:t.save}},[t._v("保存")])],1)],1)],1):t._e(),t.isEditDialogOpen?t._e():e("div",{staticClass:"paragraph_edit_toos"},[e("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"AI生成",placement:"top"}},[e("i",{staticClass:"el-icon-refresh-left",on:{click:t.reGenerate}})]),e("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"手动编辑",placement:"top"}},[e("i",{staticClass:"el-icon-edit",on:{click:function(e){t.isEditDialogOpen=!0}}})])],1)])},i=[];const r={props:{title:String,notice:String,content:{type:String}},data(){return{isEditDialogOpen:!1,editableContent:this.content}},methods:{save(){this.$emit("onSave",this.editableContent)},reGenerate(){this.$emit("onReGenerate")}},watch:{content(t,e){this.editableContent=t,this.isEditDialogOpen=!1}},created(){}},l=r;var n=s(1656),o=(0,n.A)(l,a,i,!1,null,"3067bed9",null);const c=o.exports},1633:(t,e,s)=>{"use strict";s.r(e),s.d(e,{default:()=>d});var a=function(){var t=this,e=t._self._c;return e("div",{staticClass:"thesis-download-config"},[e("el-card",[e("div",{attrs:{slot:"header"},slot:"header"},[e("span",[t._v("论文下载收费配置")])]),e("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"config-content"},[e("el-form",{ref:"configForm",staticClass:"config-form",attrs:{model:t.configForm,"label-width":"150px"}},[e("el-card",{staticClass:"config-section",attrs:{shadow:"never"}},[e("div",{attrs:{slot:"header"},slot:"header"},[e("i",{staticClass:"el-icon-setting"}),e("span",[t._v("基础配置")])]),e("el-row",{attrs:{gutter:20}},[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"是否启用收费"}},[e("el-switch",{attrs:{"active-text":"启用","inactive-text":"禁用"},model:{value:t.configForm.is_active,callback:function(e){t.$set(t.configForm,"is_active",e)},expression:"configForm.is_active"}})],1)],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"首次下载是否免费"}},[e("el-switch",{attrs:{"active-text":"免费","inactive-text":"收费"},model:{value:t.configForm.first_free,callback:function(e){t.$set(t.configForm,"first_free",e)},expression:"configForm.first_free"}})],1)],1)],1),e("el-row",{attrs:{gutter:20}},[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"下载收费金额(元)"}},[e("el-input-number",{attrs:{min:.01,max:1e3,step:.01,precision:2},model:{value:t.configForm.price,callback:function(e){t.$set(t.configForm,"price",e)},expression:"configForm.price"}})],1)],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"VIP用户是否免费"}},[e("el-switch",{attrs:{"active-text":"免费","inactive-text":"收费"},model:{value:t.configForm.vip_free,callback:function(e){t.$set(t.configForm,"vip_free",e)},expression:"configForm.vip_free"}})],1)],1)],1)],1),e("el-card",{staticClass:"config-section",attrs:{shadow:"never"}},[e("div",{attrs:{slot:"header"},slot:"header"},[e("i",{staticClass:"el-icon-info"}),e("span",[t._v("功能说明")])]),e("div",{staticClass:"description-box"},[e("p",[e("i",{staticClass:"el-icon-check"}),t._v(" 启用收费功能后，用户下载论文时需要支付相应费用")]),e("p",[e("i",{staticClass:"el-icon-check"}),t._v(" 可以设置用户首次下载免费，后续下载收费")]),e("p",[e("i",{staticClass:"el-icon-check"}),t._v(" 可以设置VIP用户下载免费，普通用户下载收费")]),e("p",[e("i",{staticClass:"el-icon-check"}),t._v(" 收费金额可以根据实际需求进行调整")]),e("p",[e("i",{staticClass:"el-icon-warning"}),t._v(" 请确保已正确配置支付接口，否则收费功能可能无法正常使用")])])]),e("el-card",{staticClass:"config-section",attrs:{shadow:"never"}},[e("div",{staticClass:"action-buttons"},[e("el-button",{attrs:{type:"primary",loading:t.saving},on:{click:t.saveConfig}},[e("i",{staticClass:"el-icon-check"}),t._v(" 保存配置 ")]),e("el-button",{on:{click:t.loadConfig}},[e("i",{staticClass:"el-icon-refresh-right"}),t._v(" 刷新数据 ")])],1)])],1)],1)])],1)},i=[],r=s(5597);const l={name:"ThesisDownloadConfig",data(){return{loading:!1,saving:!1,configForm:{is_active:!1,first_free:!0,price:10,vip_free:!0}}},created(){this.loadConfig()},methods:{async loadConfig(){try{this.loading=!0;const t=await(0,r.jp)();if(t.success){const e=t.data||{};if(!e)return void console.warn("配置数据为空");const s=e.thesis_download||{};this.configForm={is_active:"true"===s.is_active?.value,first_free:"true"===s.first_free?.value,vip_free:"true"===s.vip_free?.value,price:s.price?.value||0},this.$message.success("配置加载成功")}else this.$message.error(t.message||"加载配置失败")}catch(t){this.$message.error("加载配置失败: "+(t.message||"未知错误"))}finally{this.loading=!1}},async saveConfig(){try{this.$refs.configForm.validate((async t=>{if(!t)return;this.saving=!0;const e={thesis_download:{is_active:this.configForm.is_active,first_free:this.configForm.first_free,vip_free:this.configForm.vip_free,price:this.configForm.price}},s=await(0,r.Mh)(e);s.success?(this.$message.success("配置保存成功"),this.loadConfig()):this.$message.error(s.message||"保存配置失败")}))}catch(t){this.$message.error("保存配置失败: "+(t.message||"未知错误"))}finally{this.saving=!1}}}},n=l;var o=s(1656),c=(0,o.A)(n,a,i,!1,null,"2c1a9c75",null);const d=c.exports},1646:(t,e,s)=>{"use strict";s.r(e),s.d(e,{default:()=>d});var a=function(){var t=this,e=t._self._c;return e("section",t._l(t.outlineTree["subtitle"],(function(s,a){return e("div",{key:s["id"],staticClass:"tree_node"},[e("div",{class:"tree_title "+(t.selectedPid==s.id?"s":""),attrs:{"data-paraid":s.id},on:{click:function(e){return t.handleClick(s)}}},[e("div",{class:"tree_title_level"+t.level},[t._v(" ["+t._s(t.chapterTitlePrefix+(a+1))+"] "+t._s(s.title)+" ")]),14==s.status?e("i",{staticClass:"el-icon-warning-outline",attrs:{title:"生成失败"}}):t._e(),13==s.status?e("i",{staticClass:"el-icon-circle-check",attrs:{title:"生成完毕"}}):t._e(),11==s.status?e("i",{staticClass:"el-icon-time",attrs:{title:"等待生成"}}):t._e(),12==s.status?e("i",{staticClass:"el-icon-loading",attrs:{title:"生成中"}}):t._e(),1==s.status?e("i",{staticClass:"el-icon-remove-outline",attrs:{title:"从未生成"}}):t._e()]),e("OutlineTree",{attrs:{outlineTree:s,level:t.level+1,chapterTitlePrefix:t.chapterTitlePrefix+(a+1)+"."}})],1)})),0)},i=[],r=s(5353);const l={name:"OutlineTree",props:{chapterTitlePrefix:{type:String,default:""},outlineTree:{type:Object,default:{}},level:{type:Number,default:1}},computed:{...(0,r.aH)({selectPara:t=>t.thesis.outlineTreeSelectPara}),selectedPid(){return this.selectPara["id"]}},methods:{handleClick(t){this.$store.commit("thesis/SET_SELECTED_PARAGRAPH",t)}}},n=l;var o=s(1656),c=(0,o.A)(n,a,i,!1,null,"1fd762f0",null);const d=c.exports},2366:(t,e,s)=>{var a={"./views/admin/Dashboard":9318,"./views/admin/Dashboard.vue":9318,"./views/admin/Login":8418,"./views/admin/Login.vue":8418,"./views/admin/PaymentSettings":8307,"./views/admin/PaymentSettings.vue":8307,"./views/admin/Settings":7859,"./views/admin/Settings.vue":7859,"./views/admin/StatsChat":3917,"./views/admin/StatsChat.vue":3917,"./views/admin/StatsOverview":5344,"./views/admin/StatsOverview.vue":5344,"./views/admin/StatsThesis":345,"./views/admin/StatsThesis.vue":345,"./views/admin/StatsUsers":4795,"./views/admin/StatsUsers.vue":4795,"./views/admin/Thesis":897,"./views/admin/Thesis.vue":897,"./views/admin/ThesisStats":6018,"./views/admin/ThesisStats.vue":6018,"./views/admin/UserStats":7307,"./views/admin/UserStats.vue":7307,"./views/admin/Users":8022,"./views/admin/Users.vue":8022,"./views/admin/accounts/Edit":6154,"./views/admin/accounts/Edit.vue":6154,"./views/admin/accounts/List":6632,"./views/admin/accounts/List.vue":6632,"./views/admin/settings/BaseConfig":7586,"./views/admin/settings/BaseConfig.vue":7586,"./views/admin/settings/ModelConfig":5008,"./views/admin/settings/ModelConfig.vue":5008,"./views/admin/settings/PaymentConfig":3388,"./views/admin/settings/PaymentConfig.vue":3388,"./views/admin/settings/PaymentConfigSimple":12,"./views/admin/settings/PaymentConfigSimple.vue":12,"./views/admin/settings/PaymentOrderDetail":9503,"./views/admin/settings/PaymentOrderDetail.vue":9503,"./views/admin/settings/PaymentOrders":7595,"./views/admin/settings/PaymentOrders.vue":7595,"./views/admin/settings/ThesisDownloadConfig":1633,"./views/admin/settings/ThesisDownloadConfig.vue":1633,"./views/admin/settings/WeChatPayConfig":2407,"./views/admin/settings/WeChatPayConfig.vue":2407,"./views/aicg":6364,"./views/aicg/":6364,"./views/aicg/index":6364,"./views/aicg/index.vue":6364,"./views/components/BreadCrumb":4511,"./views/components/BreadCrumb.vue":4511,"./views/components/DigestBox":8459,"./views/components/DigestBox.vue":8459,"./views/components/MarkdownRenderer":529,"./views/components/MarkdownRenderer.vue":529,"./views/components/OutlineTree":1646,"./views/components/OutlineTree.vue":1646,"./views/components/PageLeftBox":9845,"./views/components/PageLeftBox.vue":9845,"./views/components/PaperCover":9076,"./views/components/PaperCover.vue":9076,"./views/components/PaperSinglePara":1574,"./views/components/PaperSinglePara.vue":1574,"./views/components/ParagraphBox":9120,"./views/components/ParagraphBox.vue":9120,"./views/components/ParagraphEditBox":9680,"./views/components/ParagraphEditBox.vue":9680,"./views/components/Placeholder":9171,"./views/components/Placeholder.vue":9171,"./views/components/ProgressBox":6306,"./views/components/ProgressBox.vue":6306,"./views/components/ReportHistoryList":222,"./views/components/ReportHistoryList.vue":222,"./views/components/TableEditor":5539,"./views/components/TableEditor.vue":5539,"./views/help/guide":7585,"./views/help/guide.vue":7585,"./views/paper/chat":2826,"./views/paper/chat.vue":2826,"./views/paper/content":5017,"./views/paper/content.vue":5017,"./views/paper/outline":1208,"./views/paper/outline.vue":1208,"./views/paper/setting":6489,"./views/paper/setting.vue":6489,"./views/paper/title":8818,"./views/paper/title.vue":8818,"./views/report":387,"./views/report/":387,"./views/report/index":387,"./views/report/index.vue":387,"./views/user/Login":4981,"./views/user/Login.vue":4981,"./views/user/Profile":3970,"./views/user/Profile.vue":3970,"./views/user/Register":2495,"./views/user/Register.vue":2495};function i(t){var e=r(t);return s(e)}function r(t){if(!s.o(a,t)){var e=new Error("Cannot find module '"+t+"'");throw e.code="MODULE_NOT_FOUND",e}return a[t]}i.keys=function(){return Object.keys(a)},i.resolve=r,t.exports=i,i.id=2366},2407:(t,e,s)=>{"use strict";s.r(e),s.d(e,{default:()=>d});var a=function(){var t=this,e=t._self._c;return e("div",{staticClass:"wechat-pay-config"},[e("div",{staticClass:"page-header"},[e("h2",[t._v("微信支付配置管理")]),e("el-button",{attrs:{type:"primary",icon:"el-icon-plus"},on:{click:t.showCreateDialog}},[t._v(" 新增配置 ")])],1),e("el-card",{staticClass:"config-list"},[e("div",{attrs:{slot:"header"},slot:"header"},[e("span",[t._v("配置列表")]),e("el-button",{staticStyle:{float:"right",padding:"3px 0"},attrs:{type:"text"},on:{click:t.refreshList}},[t._v(" 刷新 ")])],1),e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticStyle:{width:"100%"},attrs:{data:t.configList}},[e("el-table-column",{attrs:{prop:"id",label:"ID",width:"80"}}),e("el-table-column",{attrs:{prop:"appid",label:"应用ID",width:"180"}}),e("el-table-column",{attrs:{prop:"mchid",label:"商户号",width:"120"}}),e("el-table-column",{attrs:{prop:"notify_url",label:"回调地址","min-width":"200"}}),e("el-table-column",{attrs:{label:"环境",width:"100"},scopedSlots:t._u([{key:"default",fn:function(s){return[e("el-tag",{attrs:{type:s.row.is_sandbox?"warning":"success"}},[t._v(" "+t._s(s.row.is_sandbox?"沙箱":"正式")+" ")])]}}])}),e("el-table-column",{attrs:{label:"状态",width:"100"},scopedSlots:t._u([{key:"default",fn:function(s){return[e("el-tag",{attrs:{type:s.row.is_enabled?"success":"info"}},[t._v(" "+t._s(s.row.is_enabled?"启用":"禁用")+" ")])]}}])}),e("el-table-column",{attrs:{label:"测试模式",width:"100"},scopedSlots:t._u([{key:"default",fn:function(s){return[e("el-tag",{attrs:{type:s.row.is_test_mode?"warning":"info"}},[t._v(" "+t._s(s.row.is_test_mode?"是":"否")+" ")])]}}])}),e("el-table-column",{attrs:{prop:"updated_at",label:"更新时间",width:"160"}}),e("el-table-column",{attrs:{label:"操作",width:"200",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(s){return[e("el-button",{attrs:{size:"mini"},on:{click:function(e){return t.showEditDialog(s.row)}}},[t._v("编辑")]),s.row.is_enabled?t._e():e("el-button",{attrs:{size:"mini",type:"success"},on:{click:function(e){return t.enableConfig(s.row.id)}}},[t._v(" 启用 ")]),s.row.is_enabled?e("el-button",{attrs:{size:"mini",type:"warning"},on:{click:function(e){return t.disableConfig(s.row.id)}}},[t._v(" 禁用 ")]):t._e(),s.row.is_enabled?t._e():e("el-button",{attrs:{size:"mini",type:"danger"},on:{click:function(e){return t.deleteConfig(s.row.id)}}},[t._v(" 删除 ")])]}}])})],1),e("div",{staticClass:"pagination-container"},[e("el-pagination",{attrs:{"current-page":t.pagination.page,"page-sizes":[10,20,50,100],"page-size":t.pagination.size,layout:"total, sizes, prev, pager, next, jumper",total:t.pagination.total},on:{"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1)],1),e("el-dialog",{attrs:{title:t.dialogTitle,visible:t.dialogVisible,width:"800px"},on:{"update:visible":function(e){t.dialogVisible=e},close:t.resetForm}},[e("el-form",{ref:"form",attrs:{model:t.form,rules:t.rules,"label-width":"140px"}},[e("el-divider",{attrs:{"content-position":"left"}},[t._v("基础配置")]),e("el-row",{attrs:{gutter:20}},[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"应用ID",prop:"appid"}},[e("el-input",{attrs:{placeholder:"请输入微信支付应用ID"},model:{value:t.form.appid,callback:function(e){t.$set(t.form,"appid",e)},expression:"form.appid"}})],1)],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"商户号",prop:"mchid"}},[e("el-input",{attrs:{placeholder:"请输入微信支付商户号"},model:{value:t.form.mchid,callback:function(e){t.$set(t.form,"mchid",e)},expression:"form.mchid"}})],1)],1)],1),e("el-row",{attrs:{gutter:20}},[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"APIv3密钥",prop:"api_v3_key"}},[e("el-input",{attrs:{placeholder:"请输入商户APIv3密钥","show-password":""},model:{value:t.form.api_v3_key,callback:function(e){t.$set(t.form,"api_v3_key",e)},expression:"form.api_v3_key"}})],1)],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"证书序列号",prop:"serial_no"}},[e("el-input",{attrs:{placeholder:"请输入商户证书序列号"},model:{value:t.form.serial_no,callback:function(e){t.$set(t.form,"serial_no",e)},expression:"form.serial_no"}})],1)],1)],1),e("el-row",{attrs:{gutter:20}},[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"私钥文件路径",prop:"private_key_path"}},[e("el-input",{attrs:{placeholder:"请输入商户私钥文件路径"},model:{value:t.form.private_key_path,callback:function(e){t.$set(t.form,"private_key_path",e)},expression:"form.private_key_path"}})],1)],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"平台证书路径"}},[e("el-input",{attrs:{placeholder:"请输入微信支付平台证书路径（可选）"},model:{value:t.form.platform_cert_path,callback:function(e){t.$set(t.form,"platform_cert_path",e)},expression:"form.platform_cert_path"}})],1)],1)],1),e("el-form-item",{attrs:{label:"回调通知地址",prop:"notify_url"}},[e("el-input",{attrs:{placeholder:"请输入回调通知地址"},model:{value:t.form.notify_url,callback:function(e){t.$set(t.form,"notify_url",e)},expression:"form.notify_url"}})],1),e("el-divider",{attrs:{"content-position":"left"}},[t._v("环境配置")]),e("el-form-item",{attrs:{label:"启用沙箱环境"}},[e("el-switch",{on:{change:t.handleSandboxChange},model:{value:t.form.is_sandbox,callback:function(e){t.$set(t.form,"is_sandbox",e)},expression:"form.is_sandbox"}})],1),t.form.is_sandbox?e("div",[e("el-row",{attrs:{gutter:20}},[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"沙箱应用ID",prop:"sandbox_appid"}},[e("el-input",{attrs:{placeholder:"请输入沙箱环境应用ID"},model:{value:t.form.sandbox_appid,callback:function(e){t.$set(t.form,"sandbox_appid",e)},expression:"form.sandbox_appid"}})],1)],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"沙箱商户号",prop:"sandbox_mchid"}},[e("el-input",{attrs:{placeholder:"请输入沙箱环境商户号"},model:{value:t.form.sandbox_mchid,callback:function(e){t.$set(t.form,"sandbox_mchid",e)},expression:"form.sandbox_mchid"}})],1)],1)],1),e("el-row",{attrs:{gutter:20}},[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"沙箱APIv3密钥",prop:"sandbox_api_v3_key"}},[e("el-input",{attrs:{placeholder:"请输入沙箱环境APIv3密钥","show-password":""},model:{value:t.form.sandbox_api_v3_key,callback:function(e){t.$set(t.form,"sandbox_api_v3_key",e)},expression:"form.sandbox_api_v3_key"}})],1)],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"沙箱证书序列号",prop:"sandbox_serial_no"}},[e("el-input",{attrs:{placeholder:"请输入沙箱环境证书序列号"},model:{value:t.form.sandbox_serial_no,callback:function(e){t.$set(t.form,"sandbox_serial_no",e)},expression:"form.sandbox_serial_no"}})],1)],1)],1),e("el-form-item",{attrs:{label:"沙箱私钥文件路径",prop:"sandbox_private_key_path"}},[e("el-input",{attrs:{placeholder:"请输入沙箱环境私钥文件路径"},model:{value:t.form.sandbox_private_key_path,callback:function(e){t.$set(t.form,"sandbox_private_key_path",e)},expression:"form.sandbox_private_key_path"}})],1)],1):t._e(),e("el-divider",{attrs:{"content-position":"left"}},[t._v("状态配置")]),e("el-row",{attrs:{gutter:20}},[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"启用配置"}},[e("el-switch",{model:{value:t.form.is_enabled,callback:function(e){t.$set(t.form,"is_enabled",e)},expression:"form.is_enabled"}})],1)],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"测试模式"}},[e("el-switch",{model:{value:t.form.is_test_mode,callback:function(e){t.$set(t.form,"is_test_mode",e)},expression:"form.is_test_mode"}})],1)],1)],1),e("el-form-item",{attrs:{label:"备注"}},[e("el-input",{attrs:{type:"textarea",rows:3,placeholder:"请输入备注信息"},model:{value:t.form.remark,callback:function(e){t.$set(t.form,"remark",e)},expression:"form.remark"}})],1)],1),e("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.dialogVisible=!1}}},[t._v("取消")]),e("el-button",{attrs:{type:"primary",loading:t.testing},on:{click:t.testConfig}},[t._v("测试配置")]),e("el-button",{attrs:{type:"success",loading:t.submitting},on:{click:t.submitForm}},[t._v("保存")])],1)],1),e("el-dialog",{attrs:{title:"配置测试结果",visible:t.testResultVisible,width:"500px"},on:{"update:visible":function(e){t.testResultVisible=e}}},[t.testResult.success?e("div",[e("el-alert",{attrs:{title:"测试成功",type:"success",closable:!1,"show-icon":""}},[e("div",{attrs:{slot:"description"},slot:"description"},[e("p",[t._v("配置测试成功！")]),t.testResult.data&&t.testResult.data.code_url?e("p",[e("strong",[t._v("测试二维码：")]),e("img",{staticStyle:{"max-width":"200px","margin-top":"10px"},attrs:{src:t.testResult.data.code_url}})]):t._e()])])],1):e("div",[e("el-alert",{attrs:{title:"测试失败",type:"error",closable:!1,"show-icon":""}},[e("div",{attrs:{slot:"description"},slot:"description"},[e("p",[t._v(t._s(t.testResult.message))])])])],1)])],1)},i=[],r=s(9192);const l={name:"WeChatPayConfig",data(){return{loading:!1,submitting:!1,testing:!1,dialogVisible:!1,testResultVisible:!1,isEdit:!1,configList:[],pagination:{page:1,size:10,total:0},form:{appid:"",mchid:"",api_v3_key:"",serial_no:"",private_key_path:"",platform_cert_path:"",notify_url:"",is_sandbox:!1,sandbox_appid:"",sandbox_mchid:"",sandbox_api_v3_key:"",sandbox_serial_no:"",sandbox_private_key_path:"",is_enabled:!0,is_test_mode:!1,remark:""},rules:{appid:[{required:!0,message:"请输入应用ID",trigger:"blur"}],mchid:[{required:!0,message:"请输入商户号",trigger:"blur"}],api_v3_key:[{required:!0,message:"请输入APIv3密钥",trigger:"blur"}],serial_no:[{required:!0,message:"请输入证书序列号",trigger:"blur"}],private_key_path:[{required:!0,message:"请输入私钥文件路径",trigger:"blur"}],notify_url:[{required:!0,message:"请输入回调通知地址",trigger:"blur"}],sandbox_appid:[{required:!0,message:"请输入沙箱应用ID",trigger:"blur"}],sandbox_mchid:[{required:!0,message:"请输入沙箱商户号",trigger:"blur"}],sandbox_api_v3_key:[{required:!0,message:"请输入沙箱APIv3密钥",trigger:"blur"}],sandbox_serial_no:[{required:!0,message:"请输入沙箱证书序列号",trigger:"blur"}],sandbox_private_key_path:[{required:!0,message:"请输入沙箱私钥文件路径",trigger:"blur"}]},testResult:{success:!1,message:"",data:null}}},computed:{dialogTitle(){return this.isEdit?"编辑微信支付配置":"新增微信支付配置"}},created(){this.loadConfigList()},methods:{async loadConfigList(){this.loading=!0;try{const t=await(0,r.getWeChatPayConfigList)({page:this.pagination.page,size:this.pagination.size});200===t.code?(this.configList=t.data.list,this.pagination.total=t.data.total):this.$message.error(t.message||"加载配置列表失败")}catch(t){console.error("加载配置列表失败:",t),this.$message.error("加载配置列表失败")}finally{this.loading=!1}},refreshList(){this.loadConfigList()},showCreateDialog(){this.isEdit=!1,this.dialogVisible=!0,this.resetForm()},showEditDialog(t){this.isEdit=!0,this.dialogVisible=!0,this.form={...t}},resetForm(){this.form={appid:"",mchid:"",api_v3_key:"",serial_no:"",private_key_path:"",platform_cert_path:"",notify_url:"",is_sandbox:!1,sandbox_appid:"",sandbox_mchid:"",sandbox_api_v3_key:"",sandbox_serial_no:"",sandbox_private_key_path:"",is_enabled:!0,is_test_mode:!1,remark:""},this.$nextTick((()=>{this.$refs.form&&this.$refs.form.clearValidate()}))},handleSandboxChange(t){t&&(this.form.sandbox_appid=this.form.appid,this.form.sandbox_mchid=this.form.mchid,this.form.sandbox_api_v3_key=this.form.api_v3_key,this.form.sandbox_serial_no=this.form.serial_no,this.form.sandbox_private_key_path=this.form.private_key_path)},async submitForm(){this.$refs.form.validate((async t=>{if(t){if(this.form.is_sandbox){const t=["sandbox_appid","sandbox_mchid","sandbox_api_v3_key","sandbox_serial_no","sandbox_private_key_path"];for(const e of t)if(!this.form[e])return void this.$message.error(`沙箱环境配置不完整，请填写${e}`)}this.submitting=!0;try{let t;t=this.isEdit?await(0,r.updateWeChatPayConfig)(this.form.id,this.form):await(0,r.createWeChatPayConfig)(this.form),200===t.code?(this.$message.success(t.message||"保存成功"),this.dialogVisible=!1,this.loadConfigList()):this.$message.error(t.message||"保存失败")}catch(e){console.error("保存配置失败:",e),this.$message.error("保存配置失败")}finally{this.submitting=!1}}}))},async testConfig(){this.$refs.form.validate((async t=>{if(t){this.testing=!0;try{const t=await(0,r.testWeChatPayConfig)(this.form);this.testResult={success:200===t.code,message:t.message,data:t.data},this.testResultVisible=!0}catch(e){console.error("测试配置失败:",e),this.testResult={success:!1,message:"测试配置失败",data:null},this.testResultVisible=!0}finally{this.testing=!1}}}))},async enableConfig(t){try{const e=await(0,r.enableWeChatPayConfig)(t);200===e.code?(this.$message.success("启用配置成功"),this.loadConfigList()):this.$message.error(e.message||"启用配置失败")}catch(e){console.error("启用配置失败:",e),this.$message.error("启用配置失败")}},async disableConfig(t){try{const e=await(0,r.disableWeChatPayConfig)(t);200===e.code?(this.$message.success("禁用配置成功"),this.loadConfigList()):this.$message.error(e.message||"禁用配置失败")}catch(e){console.error("禁用配置失败:",e),this.$message.error("禁用配置失败")}},async deleteConfig(t){this.$confirm("确定要删除这个配置吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{try{const e=await(0,r.deleteWeChatPayConfig)(t);200===e.code?(this.$message.success("删除配置成功"),this.loadConfigList()):this.$message.error(e.message||"删除配置失败")}catch(e){console.error("删除配置失败:",e),this.$message.error("删除配置失败")}})).catch((()=>{this.$message.info("已取消删除")}))},handleSizeChange(t){this.pagination.size=t,this.loadConfigList()},handleCurrentChange(t){this.pagination.page=t,this.loadConfigList()}}},n=l;var o=s(1656),c=(0,o.A)(n,a,i,!1,null,"1116f786",null);const d=c.exports},2826:(t,e,s)=>{"use strict";s.r(e),s.d(e,{default:()=>b});var a=function(){var t=this,e=t._self._c;return e("div",{staticClass:"chat-layout"},[e("div",{staticClass:"chat-left"},[t._m(0),e("div",{staticClass:"chat-bottom-area"},[e("div",{staticClass:"model-status"},[e("div",{staticClass:"model-selector"},[e("i",{staticClass:"el-icon-cpu"}),e("span",{staticClass:"label"},[t._v("当前AI模型：")]),e("el-select",{attrs:{size:"small",placeholder:"请选择AI模型"},on:{change:t.handleModelChange},model:{value:t.modelName,callback:function(e){t.modelName=e},expression:"modelName"}},t._l(t.modelList,(function(s){return e("el-option",{key:s.id,attrs:{label:s.name,value:s.id}},[e("span",{staticClass:"model-option"},[e("i",{staticClass:"model-icon",class:s.icon||"el-icon-cpu"}),t._v(" "+t._s(s.name)+" "),s.description?e("span",{staticClass:"model-desc"},[t._v(t._s(s.description))]):t._e()])])})),1)],1),t.isLoading?e("div",{staticClass:"loading-status"},[e("i",{staticClass:"el-icon-loading"}),t._v(" 正在获取结果，请稍等... ")]):t._e()]),e("div",{staticClass:"chat-input"},[e("input",{directives:[{name:"model",rawName:"v-model",value:t.userMessage,expression:"userMessage"}],ref:"chatInput",staticClass:"chat-text-input",attrs:{type:"text",disabled:t.isLoading||!t.modelName,placeholder:"请输入您的问题或要求，按回车发送"},domProps:{value:t.userMessage},on:{keydown:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:(e.preventDefault(),t.handleSend.apply(null,arguments))},input:function(e){e.target.composing||(t.userMessage=e.target.value)}}}),e("el-button",{attrs:{type:"primary",loading:t.isLoading,disabled:!t.modelName,icon:"el-icon-s-promotion"},on:{click:t.handleSend}},[t._v(" 发送 ")])],1)])]),e("div",{staticClass:"chat-right"},[e("div",{staticClass:"chat-container"},[e("div",{staticClass:"chat-header"},[e("span",[t._v("聊天记录")]),t.chatLog&&t.chatLog.length>0?e("el-button",{attrs:{type:"text",size:"mini"},on:{click:t.clearChatLog}},[e("i",{staticClass:"el-icon-delete"}),t._v(" 清空记录 ")]):t._e()],1),e("div",{staticClass:"chat-messages-wrapper"},[e("div",{staticClass:"chat-list",attrs:{id:"chatListBox"}},[t.hasMore?e("div",{staticClass:"chat-more",on:{click:function(e){return e.stopPropagation(),t.handleLoadMore.apply(null,arguments)}}},[t._v(" 加载更早消息 ")]):e("div",{staticClass:"chat-more"},[t._v("没有更多了")]),t._l(t.chatLog,(function(s){return[e("div",{key:s.id,class:["chat","recv"==s.type?"recv":"send"]},[e("div",{staticClass:"chat-title"},[e("span",{staticClass:"chat-avatar"},[e("i",{class:"recv"==s.type?"el-icon-service":"el-icon-user"})]),e("span",{staticClass:"chat-name"},[t._v(t._s("send"==s.type?"我":t.getModelDisplayName(s.modelName)))]),e("span",{staticClass:"chat-time"},[t._v(t._s(s.createAt))]),e("span",{staticClass:"chat-actions"},[e("el-button",{attrs:{type:"text",size:"mini"},on:{click:function(e){return e.stopPropagation(),t.handleDelete(s.id)}}},[e("i",{staticClass:"el-icon-delete"})])],1)]),e("div",{staticClass:"chat-body"},[t._v(t._s(s.msg))])])]}))],2)])])])])},i=[function(){var t=this,e=t._self._c;return e("div",{staticClass:"announcement"},[e("div",{staticClass:"announcement-title"},[e("i",{staticClass:"el-icon-info-filled"}),e("span",[t._v("使用说明")])]),e("div",{staticClass:"announcement-content"},[e("div",{staticClass:"step-list"},[e("div",{staticClass:"step-item"},[e("div",{staticClass:"step-number"},[t._v("1")]),e("div",{staticClass:"step-text"},[t._v("在设置页面选择并配置您想使用的 AI 模型（支持 Kimi、千问、DeepSeek 等）")])]),e("div",{staticClass:"step-item"},[e("div",{staticClass:"step-number"},[t._v("2")]),e("div",{staticClass:"step-text"},[t._v("在下方输入框中输入您的问题或要求，按发送按钮或回车键发送")])]),e("div",{staticClass:"step-item"},[e("div",{staticClass:"step-number"},[t._v("3")]),e("div",{staticClass:"step-text"},[t._v("AI 会根据您的输入给出相应的回复")])])]),e("div",{staticClass:"features-section"},[e("div",{staticClass:"features-title"},[t._v("您可以通过此功能：")]),e("div",{staticClass:"features-grid"},[e("div",{staticClass:"feature-item"},[e("i",{staticClass:"el-icon-edit-outline"}),e("span",[t._v("咨询论文写作相关问题")])]),e("div",{staticClass:"feature-item"},[e("i",{staticClass:"el-icon-refresh"}),e("span",[t._v("请求修改或优化论文内容")])]),e("div",{staticClass:"feature-item"},[e("i",{staticClass:"el-icon-reading"}),e("span",[t._v("获取写作建议和指导")])]),e("div",{staticClass:"feature-item"},[e("i",{staticClass:"el-icon-check"}),e("span",[t._v("验证 API 配置是否正确")])])])])])])}];Date.prototype.toMidnight=function(){return this.setHours(0),this.setMinutes(0),this.setSeconds(0),this.setMilliseconds(0),this},Date.prototype.daysAgo=function(t,e){t=t?t-0:0;const s=new Date(this.getTime()-864e5*t);return e?s.toMidnight():s},Date.prototype.monthBegin=function(t){t=t?t-0:0;const e=this.getDate()-1-t;return this.daysAgo(e,!0)},Date.prototype.quarterBegin=function(){const t=this.getMonth()-this.getMonth()%3;return new Date(this.getFullYear(),t,1).toMidnight()},Date.prototype.yearBegin=function(){return new Date(this.getFullYear(),0,1).toMidnight()},Date.prototype.strftime=function(t,e){if(!t){const t=new Date(this.getTime()+288e5).toISOString();return t.substr(0,16).replace("T"," ")}e=e&&e.startsWith("zh")?"zh":"en";const s=function(t,e){const s=e-t.toString().length;return(s&&s>0?"0".repeat(s):"")+t};t=t.replace("%F","%Y-%m-%d"),t=t.replace(/%D|%x/,"%m/%d/%y"),t=t.replace(/%T|%X/,"%H:%M:%S"),t=t.replace("%R","%H:%M"),t=t.replace("%r","%H:%M:%S %p"),t=t.replace("%c","%a %b %e %H:%M:%S %Y");const a=this;return t.replace(/%[A-Za-z%]/g,(function(t){let i=t;switch(t){case"%%":i="%";break;case"%Y":case"%G":i=a.getFullYear();break;case"%y":i=a.getFullYear()%100;break;case"%C":i=a.getFullYear()/100;break;case"%m":case"%n":i=a.getMonth()+1;break;case"%B":e=e.startsWith("en")?"english":e;case"%b":const l=a.getMonth();i=r.monthes[e][l];break;case"%d":case"%e":i=a.getDate();break;case"%j":i=a.getDaysOfYear();break;case"%U":case"%W":const n=a.getWeeksOfYear("%W"===t);i=s(n,2);break;case"%w":i=a.getDay();case"%u":i=0===i?7:i;break;case"%A":e=e.startsWith("en")?"english":e;case"%a":const o=a.getDay();i=r.weekdays[e][o];break;case"%H":case"%k":i=a.getHours();break;case"%I":case"%l":i=a.getHours(),i%=12;break;case"%M":i=a.getMinutes();break;case"%S":i=a.getSeconds();break;case"%s":i=parseInt(a.getTime()/1e3);break;case"%f":const c=a.getMilliseconds();i=s(1e3*c,6);break;case"%P":e=e.startsWith("en")?"english":e;case"%p":const d=a.getHours();i=r.meridians[e][d<12?0:1];break;case"%z":let h=a.getTimezoneOffset();const p=h<0?"-":"+";h=Math.abs(h);const u=s(h/60,2),m=s(h%60,2);i=p+u+m;break;default:break}return"%C"!==t&&"%y"!==t&&"%m"!==t&&"%d"!==t&&"%H"!==t&&"%M"!==t&&"%S"!==t||(i=s(i,2)),i.toString()}))},Date.prototype.humanize=function(t){t=t&&t.startsWith("zh")?"zh":"en";const e=this.strftime("",t),s=(Date.today()-this.toMidnight().getTime())/864e5;if(s<=-10||s>=10)return e;const a=r.dayagos[t];let i="";return i=0===s||1===s?a[s]:-1===s?a[2]:s>=2?s+a[3]:s+a[4],i+e.substr(10,6)};const r={monthes:{english:["January","February","March","April","May","June","July","August","September","October","November","December"],en:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],zh:["一月","二月","三月","四月","五月","六月","七月","八月","九月","十月","十一月","十二月"]},weekdays:{english:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],en:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],zh:["日","一","二","三","四","五","六"]},meridians:{english:["a.m.","p.m."],en:["AM","PM"],zh:["上午","下午"]},dayagos:{english:["Today","Yesterday","Tomorrow"," days ago"," days late"],en:["Today","Yesterday","Tomorrow"," days ago"," days late"],zh:["今天","昨天","明天","天前","天后"]}},l=Date;var n=s(5545);function o(t){return(0,n["default"])({url:"/api/talk/chat",method:"post",data:t,timeout:5e5})}function c(t){return(0,n["default"])({url:"/api/talk/delChat",method:"post",data:t,timeout:5e5})}function d(t){return(0,n["default"])({url:"/api/talk/getChatLog",method:"post",data:t,timeout:5e5})}var h=s(663);function p(){return[{id:"Kimi",name:"Kimi",remark:"Kimi"},{id:"Qianwen",name:"千问Max",remark:"千问Max"},{id:"DeepSeekR1",name:"DeepSeekR1",remark:"DeepSeekR1"}]}const u=p(),m={name:"智能互动",data(){return{modelList:u,userMessage:"",isLoading:!1,modelName:"",chatLog:[],pageNo:1,pageSize:9,hasMore:!0}},mounted(){(0,h.P)({}).then((t=>{this.modelName=t.data["modelName"],this.$nextTick((()=>{this.$refs.chatInput&&this.$refs.chatInput.focus()}))})),this.handleLoadMore(!0),document.addEventListener("click",this.handleDocumentClick),this.focusInterval=setInterval((()=>{this.$refs.chatInput&&!this.isLoading&&this.modelName&&this.$refs.chatInput.focus()}),1e3)},beforeDestroy(){document.removeEventListener("click",this.handleDocumentClick),this.focusInterval&&clearInterval(this.focusInterval)},methods:{clearChatLog(){this.$confirm("确定要清空所有聊天记录吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((()=>{this.chatLog=[],this.$message({type:"success",message:"聊天记录已清空"})})).catch((()=>{}))},handleDocumentClick(){this.$refs.chatInput&&!this.isLoading&&this.modelName&&this.$refs.chatInput.focus()},handleScrollTop(){this.$nextTick((function(){var t=document.querySelector(".chat-messages-wrapper");t&&(t.scrollTop=0)}))},getModelDisplayName(t){const e=this.modelList.find((e=>e.id===t));return e?e.name:t},handleScrollButtom(){this.$nextTick((function(){var t=document.querySelector(".chat-messages-wrapper");t&&(t.scrollTop=t.scrollHeight+1e3)}))},handleLoadMore(){d({pageNo:this.pageNo,pageSize:this.pageSize}).then((t=>{const e=document.querySelector(".chat-messages-wrapper"),s=e?e.scrollHeight:0,a=e?e.scrollTop:0;var i=t.data.concat(this.chatLog);this.chatLog=i,this.pageNo+=1,t.data.length<this.pageSize&&(this.hasMore=!1),this.$nextTick((()=>{if(e){const t=e.scrollHeight,i=t-s;e.scrollTop=a+i}}))}))},handleDelete(t){t<0?window.location.reload():c({chatId:t}).then((e=>{var s=JSON.parse(JSON.stringify(this.chatLog)),a=[];for(var i in s)s[i]["id"]!=t&&a.push(s[i]);this.chatLog=a}))},appendChatLog(t,e,s=-1){var a=JSON.parse(JSON.stringify(this.chatLog));a.push({id:s,type:t,msg:e,modelName:this.modelName,createAt:(new l).strftime()}),this.chatLog=a},handleSend(){if(!this.modelName)return void this.$message({type:"error",message:"请在设置界面选择AI模型"});if(""==this.userMessage)return void this.$message({type:"error",message:"请填写输入内容"});this.appendChatLog("send",this.userMessage);const t=this.userMessage;this.userMessage="",this.$nextTick((()=>{this.$refs.chatInput&&this.$refs.chatInput.focus()})),this.handleScrollButtom(),this.isLoading=!0,o({userMessage:t,modelName:this.modelName}).then((t=>{if(this.isLoading=!1,console.log("API响应:",t),t&&(t.is_success||0===t.code)){var e=t.data||{};this.appendChatLog("recv",e.msg||"无回复内容",e.chatId||-1)}else{const e=t.message||t.msg||"请求失败，请重试";this.appendChatLog("recv",e,-1)}this.handleScrollButtom()})).catch((t=>{this.isLoading=!1,this.appendChatLog("recv","请求出错，请检查网络连接",-1),this.handleScrollButtom(),console.error("聊天请求出错:",t)}))},handleModelChange(t){this.modelName=t,(0,h.k)({setting:{modelName:t}}).then((()=>{this.$message.success("AI模型已切换")})).catch((t=>{this.$message.error("AI模型切换失败")}))}}},g=m;var f=s(1656),v=(0,f.A)(g,a,i,!1,null,"3749321a",null);const b=v.exports},3388:(t,e,s)=>{"use strict";s.r(e),s.d(e,{default:()=>o});var a,i,r=s(1656),l={},n=(0,r.A)(l,a,i,!1,null,null,null);const o=n.exports},3917:(t,e,s)=>{"use strict";s.r(e),s.d(e,{default:()=>h});var a=function(){var t=this,e=t._self._c;return e("div",{staticClass:"admin-stats-chat"},[e("el-card",[e("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[e("span",[t._v("聊天统计")]),e("div",{staticClass:"header-right"},[e("el-radio-group",{attrs:{size:"small"},on:{change:t.fetchChatStats},model:{value:t.period,callback:function(e){t.period=e},expression:"period"}},[e("el-radio-button",{attrs:{label:"7d"}},[t._v("最近7天")]),e("el-radio-button",{attrs:{label:"30d"}},[t._v("最近30天")]),e("el-radio-button",{attrs:{label:"90d"}},[t._v("最近90天")])],1),e("el-button",{staticStyle:{"margin-left":"10px"},attrs:{size:"small",type:"primary",icon:"el-icon-refresh"},on:{click:t.fetchChatStats}},[t._v(" 刷新 ")])],1)]),e("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}]},[e("div",{staticClass:"stat-cards"},[e("el-row",{attrs:{gutter:20}},[e("el-col",{attrs:{span:12}},[e("el-card",{staticClass:"stat-card",attrs:{shadow:"hover"}},[e("div",{staticClass:"stat-card-title"},[t._v("总聊天记录数")]),e("div",{staticClass:"stat-card-value"},[t._v(t._s(t.statsData.total_chat_logs||0))])])],1),e("el-col",{attrs:{span:12}},[e("el-card",{staticClass:"stat-card",attrs:{shadow:"hover"}},[e("div",{staticClass:"stat-card-title"},[t._v("新增聊天记录数")]),e("div",{staticClass:"stat-card-value"},[t._v(t._s(t.statsData.new_chat_logs||0))])])],1)],1)],1),e("div",{staticClass:"chart-container"},[e("el-row",{attrs:{gutter:20}},[e("el-col",{attrs:{span:24}},[e("el-card",{staticClass:"chart-card",attrs:{shadow:"hover"}},[e("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[e("span",[t._v("每日聊天记录趋势")])]),e("div",{staticClass:"chart",staticStyle:{height:"300px"},attrs:{id:"daily-chat-chart"}})])],1)],1),e("el-row",{staticStyle:{"margin-top":"20px"},attrs:{gutter:20}},[e("el-col",{attrs:{span:12}},[e("el-card",{staticClass:"chart-card",attrs:{shadow:"hover"}},[e("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[e("span",[t._v("模型使用分布")])]),e("div",{staticClass:"chart",staticStyle:{height:"300px"},attrs:{id:"model-chart"}})])],1),e("el-col",{attrs:{span:12}},[e("el-card",{staticClass:"chart-card",attrs:{shadow:"hover"}},[e("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[e("span",[t._v("聊天类型分布")])]),e("div",{staticClass:"chart",staticStyle:{height:"300px"},attrs:{id:"type-chart"}})])],1)],1),e("el-row",{staticStyle:{"margin-top":"20px"},attrs:{gutter:20}},[e("el-col",{attrs:{span:24}},[e("el-card",{attrs:{shadow:"hover"}},[e("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[e("span",[t._v("聊天数据表格")])]),e("el-table",{staticStyle:{width:"100%"},attrs:{data:t.tableData}},[e("el-table-column",{attrs:{prop:"date",label:"日期",width:"180"}}),e("el-table-column",{attrs:{prop:"count",label:"聊天记录数"}})],1)],1)],1)],1)],1)])])],1)},i=[],r=s(9192),l=s(9393);const n={name:"AdminStatsChat",data(){return{loading:!1,period:"7d",statsData:{},tableData:[],charts:{dailyChatChart:null,modelChart:null,typeChart:null}}},mounted(){this.fetchChatStats(),window.addEventListener("resize",this.resizeCharts)},beforeDestroy(){window.removeEventListener("resize",this.resizeCharts),Object.keys(this.charts).forEach((t=>{this.charts[t]&&this.charts[t].dispose()}))},methods:{async fetchChatStats(){this.loading=!0;try{const t=await r.bk.getChatStats({period:this.period});t.success?(this.statsData=t.data,this.tableData=this.statsData.daily_stats||[],this.$nextTick((()=>{this.initCharts()}))):this.$message.error(t.message||"获取聊天统计数据失败")}catch(t){this.$message.error("获取聊天统计数据失败："+t.message)}finally{this.loading=!1}},initCharts(){this.initDailyChatChart(),this.initModelChart(),this.initTypeChart()},initDailyChatChart(){const t=document.getElementById("daily-chat-chart");if(!t)return;this.charts.dailyChatChart&&this.charts.dailyChatChart.dispose(),this.charts.dailyChatChart=l.init(t);const e=this.statsData.daily_stats||[],s=e.map((t=>t.date)),a=e.map((t=>t.count)),i={tooltip:{trigger:"axis",axisPointer:{type:"cross"}},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:[{type:"category",data:s,axisLabel:{rotate:45}}],yAxis:[{type:"value"}],series:[{name:"聊天记录数",data:a,type:"line",smooth:!0,symbolSize:8,itemStyle:{color:"#F56C6C"},areaStyle:{color:{type:"linear",x:0,y:0,x2:0,y2:1,colorStops:[{offset:0,color:"rgba(245, 108, 108, 0.5)"},{offset:1,color:"rgba(245, 108, 108, 0.1)"}]}}}]};this.charts.dailyChatChart.setOption(i)},initModelChart(){const t=document.getElementById("model-chart");if(!t)return;this.charts.modelChart&&this.charts.modelChart.dispose(),this.charts.modelChart=l.init(t);const e=this.statsData.model_stats||[],s=e.map((t=>({name:t.model||"未知",value:t.count}))),a={tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {c} ({d}%)"},legend:{orient:"vertical",right:10,top:"center",data:s.map((t=>t.name))},series:[{name:"模型使用",type:"pie",radius:"55%",center:["40%","50%"],data:s,emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}}}]};this.charts.modelChart.setOption(a)},initTypeChart(){const t=document.getElementById("type-chart");if(!t)return;this.charts.typeChart&&this.charts.typeChart.dispose(),this.charts.typeChart=l.init(t);const e=this.statsData.type_stats||[],s=e.map((t=>{let e=t.type||"未知";return"chat"===e&&(e="普通聊天"),"title"===e&&(e="标题生成"),"outline"===e&&(e="提纲生成"),"content"===e&&(e="内容生成"),{name:e,value:t.count}})),a={tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {c} ({d}%)"},legend:{orient:"vertical",right:10,top:"center",data:s.map((t=>t.name))},series:[{name:"聊天类型",type:"pie",radius:"55%",center:["40%","50%"],data:s,emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}}}]};this.charts.typeChart.setOption(a)},resizeCharts(){Object.keys(this.charts).forEach((t=>{this.charts[t]&&this.charts[t].resize()}))}}},o=n;var c=s(1656),d=(0,c.A)(o,a,i,!1,null,"d56bd5f0",null);const h=d.exports},4163:(t,e,s)=>{"use strict";s.r(e),s.d(e,{confirmPayment:()=>d,deleteThesis:()=>n,downloadThesis:()=>l,getOutline:()=>h,getProgress:()=>p,getThesisDetail:()=>r,getThesisList:()=>i,getThesisPaymentStatus:()=>c,paragraphDelete:()=>u,paragraphMove:()=>m,payThesisDownload:()=>o,saveDigest:()=>y,saveNewParagraph:()=>v,saveReference:()=>f,saveSingleParagraph:()=>b,saveThanks:()=>g});var a=s(5545);function i(){return(0,a["default"])({url:"/api/thesis/getList",method:"post",data:{}})}function r(t){return(0,a["default"])({url:"/api/thesis/getDetail",method:"post",data:t})}function l(t){return(0,a["default"])({url:"/api/thesis/downloadThesis",method:"post",data:t})}function n(t){return(0,a["default"])({url:"/api/thesis/deleteThesis",method:"post",data:t})}function o(t){return(0,a["default"])({url:"/api/thesis/payDownload",method:"post",data:t,timeout:3e4,retry:0,cancelToken:void 0})}function c(t){return(0,a["default"])({url:"/api/thesis/paymentStatus",method:"post",data:t})}function d(t){return(0,a["default"])({url:"/api/thesis/confirmPayment",method:"post",data:t})}function h(t){return(0,a["default"])({url:"/api/thesis/getOutline",method:"post",data:t})}function p(t){return(0,a["default"])({url:"/api/thesis/getProgress",method:"post",data:t})}function u(t){return(0,a["default"])({url:"/api/thesis/paragraphDelete",method:"post",data:t})}function m(t){return(0,a["default"])({url:"/api/thesis/paragraphMove",method:"post",data:t})}function g(t){return(0,a["default"])({url:"/api/thesis/saveThanks",method:"post",data:t})}function f(t){return(0,a["default"])({url:"/api/thesis/saveReference",method:"post",data:t})}function v(t){return(0,a["default"])({url:"/api/thesis/saveNewParagraph",method:"post",data:t})}function b(t){return(0,a["default"])({url:"/api/thesis/saveSingleParagraph",method:"post",data:t})}function y(t){return(0,a["default"])({url:"/api/thesis/saveDigest",method:"post",data:t})}},4511:(t,e,s)=>{"use strict";s.r(e),s.d(e,{default:()=>c});var a=function(){var t=this;t._self._c;return t._e()},i=[];const r={name:"BreadCrumb",props:{title:String,info:String}},l=r;var n=s(1656),o=(0,n.A)(l,a,i,!1,null,"5ee84936",null);const c=o.exports},4795:(t,e,s)=>{"use strict";s.r(e),s.d(e,{default:()=>h});var a=function(){var t=this,e=t._self._c;return e("div",{staticClass:"admin-stats-users"},[e("el-card",[e("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[e("span",[t._v("用户统计")]),e("div",{staticClass:"header-right"},[e("el-radio-group",{attrs:{size:"small"},on:{change:t.fetchUserStats},model:{value:t.period,callback:function(e){t.period=e},expression:"period"}},[e("el-radio-button",{attrs:{label:"7d"}},[t._v("最近7天")]),e("el-radio-button",{attrs:{label:"30d"}},[t._v("最近30天")]),e("el-radio-button",{attrs:{label:"90d"}},[t._v("最近90天")])],1),e("el-button",{staticStyle:{"margin-left":"10px"},attrs:{size:"small",type:"primary",icon:"el-icon-refresh"},on:{click:t.fetchUserStats}},[t._v(" 刷新 ")])],1)]),e("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}]},[e("div",{staticClass:"stat-cards"},[e("el-row",{attrs:{gutter:20}},[e("el-col",{attrs:{span:6}},[e("el-card",{staticClass:"stat-card",attrs:{shadow:"hover"}},[e("div",{staticClass:"stat-card-title"},[t._v("总用户数")]),e("div",{staticClass:"stat-card-value"},[t._v(t._s(t.statsData.total_users||0))])])],1),e("el-col",{attrs:{span:6}},[e("el-card",{staticClass:"stat-card",attrs:{shadow:"hover"}},[e("div",{staticClass:"stat-card-title"},[t._v("VIP用户")]),e("div",{staticClass:"stat-card-value"},[t._v(t._s(t.statsData.vip_users||0))])])],1),e("el-col",{attrs:{span:6}},[e("el-card",{staticClass:"stat-card",attrs:{shadow:"hover"}},[e("div",{staticClass:"stat-card-title"},[t._v("新增用户")]),e("div",{staticClass:"stat-card-value"},[t._v(t._s(t.statsData.new_users||0))])])],1),e("el-col",{attrs:{span:6}},[e("el-card",{staticClass:"stat-card",attrs:{shadow:"hover"}},[e("div",{staticClass:"stat-card-title"},[t._v("锁定用户")]),e("div",{staticClass:"stat-card-value"},[t._v(t._s(t.statsData.locked_users||0))])])],1)],1)],1),e("div",{staticClass:"chart-container"},[e("el-row",{attrs:{gutter:20}},[e("el-col",{attrs:{span:16}},[e("el-card",{staticClass:"chart-card",attrs:{shadow:"hover"}},[e("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[e("span",[t._v("每日新增用户趋势")])]),e("div",{staticClass:"chart",staticStyle:{height:"300px"},attrs:{id:"daily-users-chart"}})])],1),e("el-col",{attrs:{span:8}},[e("el-card",{staticClass:"chart-card",attrs:{shadow:"hover"}},[e("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[e("span",[t._v("用户性别分布")])]),e("div",{staticClass:"chart",staticStyle:{height:"300px"},attrs:{id:"gender-chart"}})])],1)],1),e("el-row",{staticStyle:{"margin-top":"20px"},attrs:{gutter:20}},[e("el-col",{attrs:{span:12}},[e("el-card",{staticClass:"chart-card",attrs:{shadow:"hover"}},[e("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[e("span",[t._v("VIP等级分布")])]),e("div",{staticClass:"chart",staticStyle:{height:"300px"},attrs:{id:"vip-level-chart"}})])],1),e("el-col",{attrs:{span:12}},[e("el-card",{staticClass:"chart-card",attrs:{shadow:"hover"}},[e("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[e("span",[t._v("用户数据表格")])]),e("el-table",{staticStyle:{width:"100%"},attrs:{data:t.tableData}},[e("el-table-column",{attrs:{prop:"date",label:"日期",width:"180"}}),e("el-table-column",{attrs:{prop:"count",label:"新增用户数"}})],1)],1)],1)],1)],1)])])],1)},i=[],r=s(9192),l=s(9393);const n={name:"AdminStatsUsers",data(){return{loading:!1,period:"7d",statsData:{},tableData:[],charts:{dailyUsersChart:null,genderChart:null,vipLevelChart:null}}},mounted(){this.fetchUserStats(),window.addEventListener("resize",this.resizeCharts)},beforeDestroy(){window.removeEventListener("resize",this.resizeCharts),Object.keys(this.charts).forEach((t=>{this.charts[t]&&this.charts[t].dispose()}))},methods:{async fetchUserStats(){this.loading=!0;try{const t=await r.bk.getUserStats({period:this.period});t.success?(this.statsData=t.data,this.tableData=this.statsData.daily_stats||[],this.$nextTick((()=>{this.initCharts()}))):this.$message.error(t.message||"获取用户统计数据失败")}catch(t){this.$message.error("获取用户统计数据失败："+t.message)}finally{this.loading=!1}},initCharts(){this.initDailyUsersChart(),this.initGenderChart(),this.initVipLevelChart()},initDailyUsersChart(){const t=document.getElementById("daily-users-chart");if(!t)return;this.charts.dailyUsersChart&&this.charts.dailyUsersChart.dispose(),this.charts.dailyUsersChart=l.init(t);const e=this.statsData.daily_stats||[],s=e.map((t=>t.date)),a=e.map((t=>t.count)),i={tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:[{type:"category",data:s,axisLabel:{rotate:45}}],yAxis:[{type:"value"}],series:[{name:"新增用户",data:a,type:"line",smooth:!0,itemStyle:{color:"#409EFF"},areaStyle:{color:{type:"linear",x:0,y:0,x2:0,y2:1,colorStops:[{offset:0,color:"rgba(64, 158, 255, 0.7)"},{offset:1,color:"rgba(64, 158, 255, 0.1)"}]}}}]};this.charts.dailyUsersChart.setOption(i)},initGenderChart(){const t=document.getElementById("gender-chart");if(!t)return;this.charts.genderChart&&this.charts.genderChart.dispose(),this.charts.genderChart=l.init(t);const e=this.statsData.gender_stats||[],s=e.map((t=>{let e=t.gender||"未知";return"male"===e&&(e="男"),"female"===e&&(e="女"),{name:e,value:t.count}})),a={tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {c} ({d}%)"},legend:{orient:"vertical",right:10,top:"center",data:s.map((t=>t.name))},series:[{name:"性别分布",type:"pie",radius:["50%","70%"],avoidLabelOverlap:!1,label:{show:!1,position:"center"},emphasis:{label:{show:!0,fontSize:"16",fontWeight:"bold"}},labelLine:{show:!1},data:s}]};this.charts.genderChart.setOption(a)},initVipLevelChart(){const t=document.getElementById("vip-level-chart");if(!t)return;this.charts.vipLevelChart&&this.charts.vipLevelChart.dispose(),this.charts.vipLevelChart=l.init(t);const e=this.statsData.vip_level_stats||[],s=e.map((t=>({name:t.level?`VIP${t.level}`:"普通用户",value:t.count}))),a={tooltip:{trigger:"item"},legend:{top:"5%",left:"center"},series:[{name:"VIP等级",type:"pie",radius:["40%","70%"],avoidLabelOverlap:!1,itemStyle:{borderRadius:10,borderColor:"#fff",borderWidth:2},label:{show:!1,position:"center"},emphasis:{label:{show:!0,fontSize:"16",fontWeight:"bold"}},labelLine:{show:!1},data:s}]};this.charts.vipLevelChart.setOption(a)},resizeCharts(){Object.keys(this.charts).forEach((t=>{this.charts[t]&&this.charts[t].resize()}))}}},o=n;var c=s(1656),d=(0,c.A)(o,a,i,!1,null,"696c1eb6",null);const h=d.exports},5008:(t,e,s)=>{"use strict";s.r(e),s.d(e,{default:()=>d});var a=function(){var t=this,e=t._self._c;return e("div",{staticClass:"model-config"},[e("h2",[t._v("大模型密钥配置")]),e("el-form",{staticStyle:{"max-width":"600px"},attrs:{model:t.form,"label-width":"140px"}},[e("el-form-item",{attrs:{label:"当前模型"}},[e("el-select",{attrs:{placeholder:"请选择当前模型"},model:{value:t.form.modelName,callback:function(e){t.$set(t.form,"modelName",e)},expression:"form.modelName"}},[e("el-option",{attrs:{label:"千问 (qianwen)",value:"qianwen"}}),e("el-option",{attrs:{label:"Kimi (kimi)",value:"kimi"}}),e("el-option",{attrs:{label:"DeepSeek (deepseek)",value:"deepseek"}}),e("el-option",{attrs:{label:"豆包 (doubao)",value:"doubao"}}),e("el-option",{attrs:{label:"OpenAI (openai)",value:"openai"}})],1)],1),e("el-form-item",{attrs:{label:"千问API Key"}},[e("el-input",{attrs:{clearable:"",placeholder:"请输入千问API Key"},model:{value:t.form.apikeyQianwen,callback:function(e){t.$set(t.form,"apikeyQianwen",e)},expression:"form.apikeyQianwen"}})],1),e("el-form-item",{attrs:{label:"DeepSeek API Key"}},[e("el-input",{attrs:{clearable:"",placeholder:"请输入DeepSeek API Key"},model:{value:t.form.apikeyDeepSeekR1,callback:function(e){t.$set(t.form,"apikeyDeepSeekR1",e)},expression:"form.apikeyDeepSeekR1"}})],1),e("el-form-item",{attrs:{label:"Kimi API Key"}},[e("el-input",{attrs:{clearable:"",placeholder:"请输入Kimi API Key"},model:{value:t.form.apikeyKimi,callback:function(e){t.$set(t.form,"apikeyKimi",e)},expression:"form.apikeyKimi"}})],1),e("el-form-item",{attrs:{label:"豆包API Key"}},[e("el-input",{attrs:{clearable:"",placeholder:"请输入豆包API Key"},model:{value:t.form.apikeyDoubao,callback:function(e){t.$set(t.form,"apikeyDoubao",e)},expression:"form.apikeyDoubao"}})],1),e("el-form-item",{attrs:{label:"OpenAI API Key"}},[e("el-input",{attrs:{clearable:"",placeholder:"请输入OpenAI API Key"},model:{value:t.form.apikeyOpenai,callback:function(e){t.$set(t.form,"apikeyOpenai",e)},expression:"form.apikeyOpenai"}})],1),e("el-form-item",[e("el-button",{attrs:{type:"primary",loading:t.saving},on:{click:t.onSave}},[t._v("保存大模型密钥")])],1)],1)],1)},i=[],r=s(663);const l={name:"ModelConfig",data(){return{form:{modelName:"",apikeyQianwen:"",apikeyDeepSeekR1:"",apikeyKimi:"",apikeyDoubao:"",apikeyOpenai:""},saving:!1}},created(){this.loadConfig()},methods:{async loadConfig(){try{const t=await(0,r.P)();if(t&&(t.success||t.is_success)&&t.data){const e=t.data;this.form.modelName=e.modelName||"",this.form.apikeyQianwen=e.apikeyQianwen||"",this.form.apikeyDeepSeekR1=e.apikeyDeepSeekR1||"",this.form.apikeyKimi=e.apikeyKimi||"",this.form.apikeyDoubao=e.apikeyDoubao||"",this.form.apikeyOpenai=e.apikeyOpenai||""}}catch(t){this.$message.error("加载大模型密钥配置失败")}},async onSave(){this.saving=!0;try{const t={setting:{modelName:this.form.modelName,apikeyQianwen:this.form.apikeyQianwen,apikeyDeepSeekR1:this.form.apikeyDeepSeekR1,apikeyKimi:this.form.apikeyKimi,apikeyDoubao:this.form.apikeyDoubao,apikeyOpenai:this.form.apikeyOpenai}},e=await(0,r.k)(t);e&&(e.success||e.is_success)?(this.$message.success("大模型密钥保存成功"),this.loadConfig()):this.$message.error(e.message||"大模型密钥保存失败")}catch(t){this.$message.error("大模型密钥保存异常")}finally{this.saving=!1}}}},n=l;var o=s(1656),c=(0,o.A)(n,a,i,!1,null,"4d59f30c",null);const d=c.exports},5017:(t,e,s)=>{"use strict";s.r(e),s.d(e,{default:()=>S});var a=function(){var t=this,e=t._self._c;return e("div",{staticClass:"flex"},[e("div",{staticClass:"home-main grid-content bg-purple-light"},[t.selecedThesis.id?e("div",{staticClass:"thesis-info-section"},[e("div",{staticClass:"thesis-info-header"},[e("div",{staticClass:"thesis-basic-info"},[e("div",{staticClass:"info-row"},[e("div",{staticClass:"info-item"},[e("span",{staticClass:"label"},[t._v("编号：")]),e("span",{staticClass:"value"},[t._v(t._s(t.selecedThesis.id))])]),e("div",{staticClass:"info-item"},[e("span",{staticClass:"label"},[t._v("水平：")]),e("span",{staticClass:"value"},[t._v(t._s(t.selecedThesis.level))])]),e("div",{staticClass:"info-item"},[e("span",{staticClass:"label"},[t._v("语言：")]),e("span",{staticClass:"value"},[t._v(t._s(t.selecedThesis.lang))])]),e("div",{staticClass:"info-item"},[e("span",{staticClass:"label"},[t._v("总字数：")]),e("span",{staticClass:"value"},[t._v(t._s(t.totalLength))])])]),e("div",{staticClass:"info-row"},[e("div",{staticClass:"info-item title-item"},[e("span",{staticClass:"label"},[t._v("标题：")]),e("span",{staticClass:"value title-text"},[t._v(t._s(t.selecedThesis.title))])])])]),t._m(0)]),e("div",{staticClass:"thesis-actions-container"},[e("div",{staticClass:"thesis-actions"},[t.statusIsRunning()?t._e():e("el-button",{staticClass:"action-btn",attrs:{type:"primary",size:"small"},on:{click:t.handlerGenerateAll}},[t._v(" 一键生成 ")]),e("el-button",{staticClass:"action-btn",attrs:{type:"primary",size:"small"},on:{click:function(e){t.onlyShowTitle=!t.onlyShowTitle}}},[t._v(" "+t._s(t.onlyShowTitle?"文本模式":"大纲模式")+" ")]),e("el-button",{staticClass:"action-btn",attrs:{type:"primary",size:"small"},on:{click:t.handlerDownload}},[t._v(" 下载 ")]),e("el-popover",{attrs:{placement:"bottom",width:"400",trigger:"click"},model:{value:t.paragraphLengthSettingsVisible,callback:function(e){t.paragraphLengthSettingsVisible=e},expression:"paragraphLengthSettingsVisible"}},[e("div",{staticClass:"paragraph-length-settings"},[e("h4",{staticStyle:{margin:"0 0 15px 0",color:"#333"}},[t._v("段落字数控制设置")]),e("el-form",{attrs:{"label-width":"120px",size:"small"}},[e("el-form-item",{attrs:{label:"默认段落字数"}},[e("el-input-number",{staticStyle:{width:"150px"},attrs:{min:100,max:3e3,step:50,"controls-position":"right"},model:{value:t.globalParagraphLength,callback:function(e){t.globalParagraphLength=e},expression:"globalParagraphLength"}}),e("span",{staticStyle:{"margin-left":"10px",color:"#666"}},[t._v("字")])],1),e("el-form-item",{attrs:{label:"字数控制模式"}},[e("el-radio-group",{attrs:{size:"small"},model:{value:t.lengthControlMode,callback:function(e){t.lengthControlMode=e},expression:"lengthControlMode"}},[e("el-radio",{attrs:{label:"fixed"}},[t._v("固定字数")]),e("el-radio",{attrs:{label:"range"}},[t._v("字数范围")]),e("el-radio",{attrs:{label:"auto"}},[t._v("智能调整")])],1)],1),"range"===t.lengthControlMode?e("el-form-item",{attrs:{label:"字数范围"}},[e("div",{staticStyle:{display:"flex","align-items":"center",gap:"10px"}},[e("el-input-number",{staticStyle:{width:"120px"},attrs:{min:100,max:t.maxParagraphLength,step:50,"controls-position":"right"},model:{value:t.minParagraphLength,callback:function(e){t.minParagraphLength=e},expression:"minParagraphLength"}}),e("span",[t._v("至")]),e("el-input-number",{staticStyle:{width:"120px"},attrs:{min:t.minParagraphLength,max:3e3,step:50,"controls-position":"right"},model:{value:t.maxParagraphLength,callback:function(e){t.maxParagraphLength=e},expression:"maxParagraphLength"}}),e("span",{staticStyle:{color:"#666"}},[t._v("字")])],1)]):t._e(),e("el-form-item",{attrs:{label:"特殊要求"}},[e("el-input",{attrs:{type:"textarea",placeholder:"对所有段落的生成要求，如：'内容要简洁明了'、'要有具体案例'等",maxlength:"200","show-word-limit":"",rows:3},model:{value:t.globalInstruction,callback:function(e){t.globalInstruction=e},expression:"globalInstruction"}})],1)],1),e("div",{staticStyle:{"text-align":"right","margin-top":"15px"}},[e("el-button",{attrs:{size:"small"},on:{click:function(e){t.paragraphLengthSettingsVisible=!1}}},[t._v("取消")]),e("el-button",{attrs:{type:"primary",size:"small"},on:{click:t.saveParagraphLengthSettings}},[t._v("保存设置")])],1)],1),e("el-button",{staticClass:"action-btn",attrs:{slot:"reference",type:"info",size:"small",icon:"el-icon-setting"},slot:"reference"},[t._v(" 字数设置 ")])],1)],1),t.statusIsRunning()?e("div",{staticClass:"thesis-actions"},[e("el-button",{staticClass:"action-btn",attrs:{type:"primary",plain:"",size:"small"}},[e("i",{staticClass:"el-icon-loading"}),t._v("一键生成中 ")]),e("el-button",{staticClass:"action-btn",attrs:{type:"primary",plain:"",size:"small"},on:{click:function(e){t.leftVisiable="outline"}}},[t._v(" 查看进度 ")])],1):t._e(),e("div",{staticClass:"thesis-actions-secondary"},[e("el-button",{staticClass:"action-btn",attrs:{type:"primary",size:"small"},on:{click:function(e){return t.leftVisiableToggle("list")}}},[t._v(" 论文列表 ")]),e("el-button",{staticClass:"action-btn",attrs:{type:"primary",size:"small"},on:{click:function(e){return t.leftVisiableToggle("outline")}}},[t._v(" 论文大纲 ")])],1)])]):t._e(),""==t.title?e("div",{staticClass:"result-box-wrapper",staticStyle:{flex:"1"}},[e("Placeholder")],1):t._e(),t.thesisList&&0!=t.thesisList.length?t._e():e("div",{staticClass:"empty-thesis-container"},[e("div",{staticClass:"empty-thesis-content"},[e("i",{staticClass:"el-icon-document-add",staticStyle:{"font-size":"64px",color:"#909399","margin-bottom":"20px"}}),e("h2",[t._v("暂无论文")]),e("p",[t._v("您还没有创建任何论文，请先拟定一个论文提纲")]),e("el-button",{staticStyle:{"margin-top":"20px"},attrs:{type:"primary",size:"medium"},on:{click:t.gotoOutline}},[t._v(" 去创建论文 ")])],1)]),""!=t.title?e("div",{staticClass:"result-box-wrapper",attrs:{id:"resultBox"}},[e("div",{staticClass:"result-box"},[t.onlyShowTitle?t._e():e("PaperCover",{attrs:{title:t.title}}),t.onlyShowTitle?t._e():e("DigestBox",{attrs:{digestValue:t.digest,thesisId:t.selecedThesisId},on:{onDigestUpdate:t.onDigestUpdate}}),e("ParagraphBox",{attrs:{onlyShowTitle:t.onlyShowTitle,thesisId:t.selecedThesisId,paragraphs:t.paragraphs,lang:t.form.lang},on:{onUpdate:t.onParasUpdate,onReGen:t.regenerateParagraph,onEdit:t.onEditPara,onTableTools:t.onTableTools,findParagraph:t.findParagraphById}}),e("div",{staticClass:"delete-box"},[t._m(1),t._m(2),e("el-button",{attrs:{type:"danger",size:"small"},on:{click:t.handlerDelete}},[t._v(" 删除本论文 ")])],1)],1)]):t._e(),e("WeChatPayDialog",{attrs:{visible:t.wechatPayDialogVisible,amount:t.paymentData.price||10,thesisId:t.paymentData.thesisId},on:{"update:visible":function(e){t.wechatPayDialogVisible=e},"payment-success":t.handleWeChatPaymentSuccess,close:t.handleWeChatPaymentClose}})],1),"outline"==t.leftVisiable?e("div",{staticClass:"outline-siderbar left-siderbar"},[e("div",{staticClass:"outline-siderbar_headeer"},[e("i",{staticClass:"el-icon-tickets"}),t._v(t._s(t.$t("contentPage.leftTitleOutline"))+" ")]),e("OutlineTree",{attrs:{outlineTree:t.outlineTree}})],1):t._e(),"list"==t.leftVisiable?e("div",{staticClass:"thesis-list-siderbar left-siderbar"},[e("div",{staticClass:"thesis-list-siderbar_header"},[e("i",{staticClass:"el-icon-document-copy"}),t._v(t._s(t.$t("contentPage.leftTitleThesisList"))+" ")]),t._l(t.thesisList,(function(s){return e("div",{key:s.id,class:t.selecedThesisId==s.id?"thesis-list-siderbar_title s":"thesis-list-siderbar_title ",on:{click:function(e){return t.handlerGotoThesisId(s.id)}}},[e("span",[t._v(t._s(s.id)+"."+t._s(s.title))])])}))],2):t._e(),e("ParagraphEditBox",{attrs:{isShow:t.editBoxVisible,para:t.editPara},on:{onClose:t.onEditBoxClose,onParaUpdated:t.onParaUpdated}}),t.paragraphRegenFormVisible?e("div",{staticClass:"dialog-box_bg",on:{click:function(e){t.paragraphRegenFormVisible=!1}}}):t._e(),t.paragraphRegenFormVisible?e("div",{staticClass:"dialog-box"},[e("div",{staticClass:"dialog-box_header"},[e("div",{staticClass:"dialog-box_header_left"},[t._v("AI生成本段内容")]),e("div",{staticClass:"dialog-box_header_right"},[e("i",{staticClass:"el-icon-circle-close",on:{click:function(e){t.paragraphRegenFormVisible=!1}}})])]),e("div",{staticClass:"dialog-box_body"},[e("el-form",{staticClass:"paragraph_regen_form"},[e("el-form-item",{attrs:{label:"段落标题"}},[e("el-input",{attrs:{placeholder:"段落标题",disabled:""},model:{value:t.paragraphRegenForm.paragraphTitle,callback:function(e){t.$set(t.paragraphRegenForm,"paragraphTitle",e)},expression:"paragraphRegenForm.paragraphTitle"}})],1),e("el-form-item",{attrs:{label:"字数控制方式",required:""}},[e("el-radio-group",{on:{change:t.onLengthModeChange},model:{value:t.paragraphRegenForm.lengthMode,callback:function(e){t.$set(t.paragraphRegenForm,"lengthMode",e)},expression:"paragraphRegenForm.lengthMode"}},[e("el-radio",{attrs:{label:"fixed"}},[t._v("固定字数")]),e("el-radio",{attrs:{label:"range"}},[t._v("字数范围")]),e("el-radio",{attrs:{label:"auto"}},[t._v("智能调整")])],1)],1),"fixed"===t.paragraphRegenForm.lengthMode?e("el-form-item",{attrs:{label:"期望字数",required:""}},[e("el-input-number",{staticStyle:{width:"200px"},attrs:{min:100,max:3e3,step:50,"controls-position":"right",placeholder:"请填写本段你期望的字数"},model:{value:t.paragraphRegenForm.length,callback:function(e){t.$set(t.paragraphRegenForm,"length",e)},expression:"paragraphRegenForm.length"}}),e("span",{staticStyle:{"margin-left":"10px",color:"#666"}},[t._v("字")])],1):t._e(),"range"===t.paragraphRegenForm.lengthMode?e("el-form-item",{attrs:{label:"字数范围",required:""}},[e("div",{staticStyle:{display:"flex","align-items":"center",gap:"10px"}},[e("el-input-number",{staticStyle:{width:"150px"},attrs:{min:100,max:t.paragraphRegenForm.maxLength,step:50,"controls-position":"right",placeholder:"最小字数"},model:{value:t.paragraphRegenForm.minLength,callback:function(e){t.$set(t.paragraphRegenForm,"minLength",e)},expression:"paragraphRegenForm.minLength"}}),e("span",[t._v("至")]),e("el-input-number",{staticStyle:{width:"150px"},attrs:{min:t.paragraphRegenForm.minLength,max:3e3,step:50,"controls-position":"right",placeholder:"最大字数"},model:{value:t.paragraphRegenForm.maxLength,callback:function(e){t.$set(t.paragraphRegenForm,"maxLength",e)},expression:"paragraphRegenForm.maxLength"}}),e("span",{staticStyle:{color:"#666"}},[t._v("字")])],1)]):t._e(),"auto"===t.paragraphRegenForm.lengthMode?e("el-form-item",{attrs:{label:"智能调整"}},[e("div",{staticStyle:{color:"#666","font-size":"12px"}},[t._v(" 系统将根据段落重要性和上下文自动调整字数，预计在 "+t._s(t.paragraphRegenForm.estimatedLength)+" 字左右 ")])]):t._e(),e("el-form-item",{attrs:{label:"内容风格"}},[e("el-select",{staticStyle:{width:"200px"},attrs:{placeholder:"选择内容风格"},model:{value:t.paragraphRegenForm.contentStyle,callback:function(e){t.$set(t.paragraphRegenForm,"contentStyle",e)},expression:"paragraphRegenForm.contentStyle"}},[e("el-option",{attrs:{label:"学术严谨",value:"academic"}}),e("el-option",{attrs:{label:"通俗易懂",value:"popular"}}),e("el-option",{attrs:{label:"简洁明了",value:"concise"}}),e("el-option",{attrs:{label:"详细深入",value:"detailed"}}),e("el-option",{attrs:{label:"案例丰富",value:"case_rich"}})],1)],1),e("el-form-item",{attrs:{label:"给AI的建议和提示:"}},[e("el-input",{attrs:{type:"textarea",placeholder:"本次生成，你希望AI如何来做呢？(可留空,可以写：'请内容简洁一点','请添加数据来证明','要有案例说明'...等)",maxlength:"200","show-word-limit":"",rows:3},model:{value:t.paragraphRegenForm.instruction,callback:function(e){t.$set(t.paragraphRegenForm,"instruction",e)},expression:"paragraphRegenForm.instruction"}})],1)],1)],1),e("div",{staticClass:"dialog-box_footer"},[e("el-button",{attrs:{type:"primary",size:"medium"},on:{click:function(e){t.paragraphRegenFormVisible=!1}}},[t._v(" 取消 ")]),e("el-button",{attrs:{type:"primary"},on:{click:t.handlerGenerateSinglePara2}},[t._v("开始生成")])],1)]):t._e(),e("TableEditor",{attrs:{visible:t.tableEditorVisible,context:t.currentContext},on:{"update:visible":function(e){t.tableEditorVisible=e},onConfirm:t.onTableConfirm}})],1)},i=[function(){var t=this,e=t._self._c;return e("div",{staticClass:"thesis-help-link"},[e("a",{attrs:{href:"http://ai.zaoniao.vip/question",target:"_blank"}},[t._v(" 如何控制全文字数？ 查重率是多少？ 早鸟论文生成内容的缺点有哪些？ ")])])},function(){var t=this,e=t._self._c;return e("div",{staticClass:"freeduty"},[t._v(" 免责声明：本网站生成内容均为阿里千问AI大模型生成。无法保证全部正确，无法保证全部真实。仅供学习参考使用，禁止将生成内容直接用于正式严肃场合。 "),e("br"),t._v(" 本网站仅提供生成功能，内容的创作由用户自行完成，生成文档版权归用户所有。 ")])},function(){var t=this,e=t._self._c;return e("div",{staticClass:"agreement"},[e("a",{attrs:{href:"/api/agreement/privacy",target:"_blank"}},[t._v("隐私政策协议 ")]),e("a",{attrs:{href:"/api/agreement/user",target:"_blank"}},[t._v("用户服务协议 ")])])}],r=s(6960),l=s(4163),n=s(9171),o=s(9120),c=s(9680),d=s(8459),h=s(1646),p=s(9076),u=s(1574),m=s(5539),g=s(5353),f=function(){var t=this,e=t._self._c;return e("el-dialog",{attrs:{title:"微信支付",visible:t.dialogVisible,width:"400px","before-close":t.handleClose,center:""},on:{"update:visible":function(e){t.dialogVisible=e}}},[e("div",{staticClass:"payment-content"},[e("div",{staticClass:"payment-amount"},[e("div",{staticClass:"amount-label"},[t._v("支付金额")]),e("div",{staticClass:"amount-value"},[t._v("¥"+t._s(t.amount.toFixed(2)))])]),e("div",{staticClass:"qr-code-container"},[t.qrCodeUrl?e("img",{staticClass:"qr-code",attrs:{src:t.qrCodeUrl,alt:"微信支付二维码"}}):e("div",{staticClass:"qr-code-placeholder"},[e("i",{staticClass:"el-icon-loading"}),e("p",[t._v("正在生成支付二维码...")])]),e("p",{staticClass:"qr-tip"},[t._v("请使用微信扫描二维码完成支付")])])]),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:t.handleClose}},[t._v("取消支付")]),e("el-button",{attrs:{type:"primary"},on:{click:t.checkPaymentStatus}},[t._v("支付完成")])],1)])},v=[];const b={name:"WeChatPayDialog",props:{visible:{type:Boolean,default:!1},amount:{type:Number,default:10},thesisId:{type:[Number,String],required:!0}},data(){return{dialogVisible:!1,qrCodeUrl:"",orderId:"",paymentInterval:null,isPaymentProcessing:!1,retryCount:0,maxRetries:3,isInitializing:!1}},watch:{visible(t){this.dialogVisible=t,t?(this.retryCount=0,this.isInitializing=!1,this.$nextTick((()=>{setTimeout((()=>{console.log("检查初始化条件:",{dialogVisible:this.dialogVisible,thesisId:this.thesisId,isInitializing:this.isInitializing}),this.dialogVisible&&this.thesisId&&!this.isInitializing?(console.log("弹窗显示，开始初始化支付，论文ID:",this.thesisId),this.initPayment()):console.log("跳过初始化支付，条件不满足")}),300)}))):(this.clearPaymentInterval(),this.isInitializing=!1,this.retryCount=0)}},methods:{async initPayment(){try{if(this.isInitializing)return void console.log("支付初始化正在进行中，跳过重复调用");if(!this.dialogVisible)return void console.log("弹窗已关闭，取消初始化支付");if(!this.thesisId)return void console.log("论文ID无效，取消初始化支付");if(console.log("开始初始化支付，论文ID:",this.thesisId,"弹窗状态:",this.dialogVisible),this.isInitializing=!0,this.qrCodeUrl="",this.orderId="",!this.thesisId)return console.error("缺少论文ID"),this.$message.error("缺少论文ID，无法创建支付订单"),void this.handleClose();const e=this.$loading({lock:!0,text:"正在创建支付订单...",spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"});try{const t=await(0,l.payThesisDownload)({thesisId:this.thesisId,paymentMethod:"wechat"});e.close(),this.isInitializing=!1,console.log("支付订单API响应:",t),t&&(t.success||t.is_success)&&t.data?(this.orderId=t.data.order_id,this.qrCodeUrl=t.data.qr_code_url||s(8934),this.startPaymentStatusPolling()):(this.$message.error("创建支付订单失败："+(t.message||"未知错误")),this.handleClose())}catch(t){e.close(),this.isInitializing=!1,console.error("创建支付订单请求失败:",t),t.message&&(t.message.includes("请求被取消")||t.message.includes("canceled")||t.message.includes("aborted"))?(console.log("请求被取消，尝试重新初始化支付，当前重试次数:",this.retryCount),this.retryCount<this.maxRetries?(this.retryCount++,console.log(`准备第 ${this.retryCount} 次重试，延迟 3 秒`),this.$message({message:`网络请求被中断，将在3秒后自动重试 (${this.retryCount}/${this.maxRetries})`,type:"warning",duration:3e3}),setTimeout((()=>{this.dialogVisible&&!this.isInitializing&&this.thesisId?(console.log("执行自动重试"),this.initPayment()):console.log("跳过重试，弹窗状态已变化")}),3e3)):(console.log("重试次数已达上限，停止重试"),this.$message.error("网络连接不稳定，请手动关闭弹窗后重新尝试"))):(this.$message.error("创建支付订单失败："+(t.message||"未知错误")),console.log("非网络错误，延迟关闭弹窗"),setTimeout((()=>{this.dialogVisible&&this.handleClose()}),3e3))}}catch(t){this.isInitializing=!1,console.error("初始化支付失败:",t),this.$message.error("创建支付订单失败，请稍后重试"),this.handleClose()}},startPaymentStatusPolling(){this.clearPaymentInterval(),this.paymentInterval=setInterval((async()=>{await this.checkPaymentStatus(!0)}),3e3)},clearPaymentInterval(){this.paymentInterval&&(clearInterval(this.paymentInterval),this.paymentInterval=null)},async checkPaymentStatus(t=!1){if(!this.isPaymentProcessing)try{this.isPaymentProcessing=!0;let e=null;t||(e=this.$loading({lock:!0,text:"正在检查支付状态...",spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"}));const s=await(0,l.getThesisPaymentStatus)({thesisId:this.thesisId,orderId:this.orderId});e&&e.close(),console.log("支付状态API响应:",s),s&&(s.success||s.is_success)&&s.data?s.data.is_paid||"paid"===s.data.payment_status?(this.clearPaymentInterval(),this.$message.success("支付成功！正在自动下载论文..."),s.data.download_file?(console.log("支付成功，自动下载文件:",s.data.download_file),this.triggerDownload(s.data.download_file)):await this.confirmPaymentAndDownload(),this.$emit("payment-success",{orderId:this.orderId,amount:this.amount,thesisId:this.thesisId,downloadFile:s.data.download_file}),setTimeout((()=>{this.handleClose()}),2e3)):t||this.$message.info("支付尚未完成，请扫码支付或稍后再试"):t||this.$message.error("查询支付状态失败："+(s&&s.message?s.message:"未知错误"))}catch(e){console.error("查询支付状态失败:",e),t?this.retryCount<this.maxRetries?(this.retryCount++,console.log(`自动查询支付状态失败，已重试 ${this.retryCount}/${this.maxRetries} 次`)):this.retryCount>=this.maxRetries&&(console.error("查询支付状态失败次数过多，停止自动查询"),this.clearPaymentInterval()):this.$message.error("查询支付状态失败，请稍后再试")}finally{this.isPaymentProcessing=!1}},async confirmPaymentAndDownload(){try{console.log("确认支付并准备下载论文");const t=this.$loading({lock:!0,text:"正在确认支付并准备下载...",spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"}),e=await(0,l.confirmPayment)({thesisId:this.thesisId,orderId:this.orderId});if(t.close(),e&&(e.success||e.is_success)&&e.data&&e.data.file){console.log("确认支付成功，开始下载论文");let t=document.createElement("a");return t.style.display="none",t.href="/api/thesis/download?fileName="+e.data.file,document.body.appendChild(t),t.click(),document.body.removeChild(t),!0}return console.error("确认支付失败或无法获取下载文件"),this.$message.error('确认支付成功，但无法下载论文，请稍后在"我的论文"中重试下载'),!1}catch(t){return console.error("确认支付并下载论文失败:",t),this.$message.error('确认支付失败，请稍后在"我的论文"中重试下载'),!1}},handleClose(){if(console.log("微信支付弹窗关闭请求"),this.isInitializing)return console.log("正在初始化支付，询问用户是否确认关闭"),void this.$confirm("支付正在初始化中，确定要关闭吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((()=>{this.forceClose()})).catch((()=>{console.log("用户取消关闭弹窗")}));this.forceClose()},forceClose(){console.log("强制关闭微信支付弹窗"),this.clearPaymentInterval(),this.isInitializing=!1,this.retryCount=0,this.qrCodeUrl="",this.orderId="",this.dialogVisible=!1,this.$emit("update:visible",!1),this.$emit("close")},triggerDownload(t){try{console.log("触发自动下载:",t);const e=document.createElement("a");e.href=t,e.download=t.split("/").pop()||"thesis.docx",document.body.appendChild(e),e.click(),document.body.removeChild(e),console.log("文件下载已触发")}catch(e){console.error("触发下载失败:",e),this.$message.error("自动下载失败，请手动下载")}}},beforeDestroy(){this.clearPaymentInterval()}},y=b;var _=s(1656),C=(0,_.A)(y,f,v,!1,null,"587e8ce7",null);const w=C.exports,k={name:"GetContent",components:{Placeholder:n["default"],ParagraphBox:o["default"],ParagraphEditBox:c["default"],DigestBox:d["default"],OutlineTree:h["default"],PaperCover:p["default"],PaperSinglePara:u["default"],TableEditor:m["default"],WeChatPayDialog:w},data(){return{TaskStatus:{DEFAULT:1,INIT:11,RUNING:12,SUCCESS:13,ERROR:14},thesisList:[],title:"",digest:{digest:"",digestEn:"",keywords:"",keywordsEn:""},thanks:"",paragraphs:[],selecedThesisId:0,selecedThesis:{},selecedThesisStatus:"",form:{title:"自媒体对经济学的影响",lang:"中文",length:"短(约4000字左右)"},editBoxVisible:!1,editPara:{},leftVisiable:"outline",isVip:!0,outlineTree:{},progressList:[],onlyShowTitle:!1,paragraphRegenFormVisible:!1,paragraphRegenForm:{paragraphId:"",thesisId:"",paragraphTitle:"",length:500,lengthMode:"fixed",minLength:300,maxLength:800,estimatedLength:500,contentStyle:"academic",instruction:""},paragraphLengthSettingsVisible:!1,wechatPayDialogVisible:!1,paymentData:{thesisId:0,price:10},paymentLoading:!1}},ProgressTimerInterval:null,created(){this.pageInit(),this.loadParagraphLengthSettings()},mounted(){this.handleKeydown=t=>{"Escape"===t.key&&this.paragraphRegenFormVisible&&(this.paragraphRegenFormVisible=!1)},document.addEventListener("keydown",this.handleKeydown)},beforeDestroy(){this.paragraphRegenFormVisible=!1,this.handleKeydown&&document.removeEventListener("keydown",this.handleKeydown),this.ProgressTimerInterval&&(clearInterval(this.ProgressTimerInterval),this.ProgressTimerInterval=null),this.ContentRefreshInterval&&(clearInterval(this.ContentRefreshInterval),this.ContentRefreshInterval=null)},computed:{...(0,g.aH)({selectedPara:t=>t.thesis.outlineTreeSelectPara}),totalLength(){if(!this.paragraphs||!Array.isArray(this.paragraphs))return 0;var t=e=>{if(!e||"undefined"===typeof e.length)return 0;var s=e.length;if(void 0!=e["subtitle"]&&e["subtitle"]&&Array.isArray(e["subtitle"])&&e["subtitle"].length>0)for(var a in e["subtitle"])s+=t(e["subtitle"][a]);return s},e=0;for(var s in this.paragraphs)e+=t(this.paragraphs[s]);return e}},watch:{$route(t,e){this.pageInit()},selectedPara(t,e){let s=document.querySelector('.paragraph_one[data-paraId="'+e.id+'"]');null!=s&&s.classList.remove("s");let a=document.querySelector('.paragraph_one[data-paraId="'+t.id+'"]');a.classList.add("s");let i=0;this.$nextTick((()=>{let t=document.getElementById("resultBox");const e=a.offsetTop-200,s=setInterval((()=>{if(i>20)clearInterval(s);else{var a=t.scrollTop;e-a<30&&e-a>-30?clearInterval(s):(i+=1,t.scrollTop+=.1*(e-a))}}),5)}))}},methods:{pageInit(){let t=-1;if(void 0!=this.$route.query["thesisId"]){let e=parseInt(this.$route.query["thesisId"]);e>0&&(t=e)}clearInterval(this.ProgressTimerInterval),clearInterval(this.ContentRefreshInterval),this.paragraphRegenFormVisible=!1,this.editBoxVisible=!1,this.refreshThesisList(t)},openLoginBox(){alert("need login")},onSingleParagraphGenerate(t){let e="",s="";if("thanks"==t?(e=r.bs,s="#signelParaThanks"):"reference"==t&&(e=r.JB,s="#signelParaReference"),""!=e){var a=this.$loading({text:"AI生成中，请稍后",target:s,background:"#000000aa"});e({thesisId:this.selecedThesisId}).then((t=>{a.close(),this.refreshCurrentThesis()})).catch((t=>{a.close(),this.$notify.error(t)}))}},onSaveReference(t){var e=this.$loading({text:"保存中",target:"#signelParaReference",background:"#000000aa"});(0,l.saveReference)({thesisId:this.selecedThesisId,content:t}).then((t=>{e.close(),this.refreshCurrentThesis()})).catch((t=>{console.log(t),e.close(),this.$notify.error(t)}))},onSaveThanks(t){var e=this.$loading({text:"保存中",target:"#signelParaThanks"});(0,l.saveThanks)({thesisId:this.selecedThesisId,content:t}).then((t=>{e.close(),this.refreshCurrentThesis()})).catch((t=>{console.log(t),e.close(),this.$notify.error(t)}))},statusIsRunning(){return this.selecedThesisStatus==this.TaskStatus.INIT||this.selecedThesisStatus==this.TaskStatus.RUNING},leftVisiableToggle(t){this.leftVisiable=t,localStorage.setItem("contentPageLeftBar",this.leftVisiable)},gotoOutline(){this.$router.push({path:"/paper/outline"})},onParasUpdate(){this.refreshCurrentThesis()},onDigestUpdate(){this.refreshCurrentThesis()},onParaUpdated(){this.refreshCurrentThesis()},onEditBoxClose(){this.editBoxVisible=!1},onEditPara(t){this.editBoxVisible=!0,this.editPara={thesisId:this.selecedThesisId,paragraphId:t["id"],title:t["title"],text:t["text"]}},onTableTools(t){this.$store.commit("thesis/SET_SELECTED_PARAGRAPH",t),this.showTableEditor()},findParagraphById(t,e){console.log("在父组件中查找段落，paraId:",t),console.log("父组件段落数据:",this.paragraphs),console.log("父组件段落数据类型:",typeof this.paragraphs),console.log("父组件段落数据长度:",this.paragraphs?this.paragraphs.length:"undefined");const s=e=>{if(!e||!Array.isArray(e))return console.log("段落数据无效:",e),null;for(let a of e){if(console.log("检查段落:",{id:a.id,title:a.title}),a.id===t||String(a.id)===String(t))return console.log("找到匹配的段落:",a),a;if(a.subtitle&&Array.isArray(a.subtitle)){const t=s(a.subtitle);if(t)return t}}return null},a=s(this.paragraphs);console.log("父组件查找结果:",a),e&&"function"===typeof e?(console.log("调用回调函数，传递结果:",a),e(a)):console.error("回调函数无效:",e)},showTableEditor(){if(!this.selectedPara||!this.selectedPara.id)return void this.$message.warning("请先选择一个段落");const t=this.selectedPara,e=t.text||"",s=t.title||"";console.log("显示智能表格编辑器，当前段落:",{id:t.id,title:s,text:e.substring(0,100)+"...",textLength:e.length}),this.currentContext={title:s,content:e,paragraphId:t.id,thesisId:this.selecedThesisId,type:"paragraph",thesisTitle:this.title,paragraphIndex:this.getParagraphIndex(t.id),keywords:this.extractKeywords(e),dataPoints:this.extractDataPoints(e)},console.log("传递给TableEditor的上下文:",this.currentContext),this.tableEditorVisible=!0,this.$message.info("正在分析段落内容并生成智能表格...")},getParagraphIndex(t){const e=(t,s,a=0)=>{for(let i=0;i<t.length;i++){if(t[i].id===s)return a+i;if(t[i].subtitle&&t[i].subtitle.length>0){const r=e(t[i].subtitle,s,a+i+1);if(-1!==r)return r}}return-1};return e(this.paragraphs,t)},extractKeywords(t){if(!t)return[];const e=[],s=t.split(/[。！？；]/);return s.forEach((t=>{if(t.length>10){const s=t.match(/[0-9]+%?|[一二三四五六七八九十]+%?|增加|减少|上升|下降|提高|降低/g);s&&e.push(...s)}})),[...new Set(e)]},extractDataPoints(t){if(!t)return[];const e=[],s=t.match(/[0-9]+%|[一二三四五六七八九十]+%/g);s&&e.push(...s.map((t=>({type:"percentage",value:t}))));const a=t.match(/[0-9]+/g);return a&&e.push(...a.map((t=>({type:"number",value:t})))),e},onTableConfirm(t){if(console.log("表格确认，HTML内容:",t),!this.selectedPara||!this.selectedPara.id)return this.$message.error("未找到目标段落"),void(this.tableEditorVisible=!1);this.insertTableToParagraph(t),this.$message.success("表格已成功插入到段落中！"),this.tableEditorVisible=!1},insertTableToParagraph(t){const e=this.selectedPara.text||"",s=e+"\n\n"+t,a={thesisId:this.selecedThesisId,paragraphId:this.selectedPara.id,title:this.selectedPara.title,text:s};(0,l.saveSingleParagraph)(a).then((t=>{this.$message.success("表格已成功插入到段落中！"),this.refreshCurrentThesis()})).catch((t=>{this.$message.error("保存失败: "+t)}))},insertSimpleTable(){if(!this.selectedPara||!this.selectedPara.id)return void this.$message.warning("请先选择一个段落");const t=this.selectedPara.title||"数据表格",e=`\n<table border="1" style="border-collapse: collapse; width: 100%; margin: 10px 0; font-size: 14px;">\n  <caption style="caption-side: top; text-align: center; font-weight: bold; margin-bottom: 10px; color: #333;">${t}</caption>\n  <thead>\n    <tr>\n      <th style="border: 1px solid #ddd; padding: 12px; text-align: center; background-color: #f5f5f5; font-weight: bold;">项目</th>\n      <th style="border: 1px solid #ddd; padding: 12px; text-align: center; background-color: #f5f5f5; font-weight: bold;">数值</th>\n      <th style="border: 1px solid #ddd; padding: 12px; text-align: center; background-color: #f5f5f5; font-weight: bold;">说明</th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <td style="border: 1px solid #ddd; padding: 10px; text-align: center;">示例项目1</td>\n      <td style="border: 1px solid #ddd; padding: 10px; text-align: center;">100</td>\n      <td style="border: 1px solid #ddd; padding: 10px; text-align: left;">这是第一个示例项目</td>\n    </tr>\n    <tr>\n      <td style="border: 1px solid #ddd; padding: 10px; text-align: center;">示例项目2</td>\n      <td style="border: 1px solid #ddd; padding: 10px; text-align: center;">200</td>\n      <td style="border: 1px solid #ddd; padding: 10px; text-align: left;">这是第二个示例项目</td>\n    </tr>\n    <tr>\n      <td style="border: 1px solid #ddd; padding: 10px; text-align: center;">示例项目3</td>\n      <td style="border: 1px solid #ddd; padding: 10px; text-align: center;">300</td>\n      <td style="border: 1px solid #ddd; padding: 10px; text-align: left;">这是第三个示例项目</td>\n    </tr>\n  </tbody>\n</table>`;this.insertTableToParagraph(e),this.$message.success("基础表格已插入到段落中")},insertTableRow(){if(!this.selectedPara||!this.selectedPara.id)return void this.$message.warning("请先选择一个段落");const t='\n    <tr>\n      <td style="border: 1px solid #ddd; padding: 10px; text-align: center;">新数据</td>\n      <td style="border: 1px solid #ddd; padding: 10px; text-align: center;">新数值</td>\n      <td style="border: 1px solid #ddd; padding: 10px; text-align: left;">新说明</td>\n    </tr>';if(this.selectedPara.text&&this.selectedPara.text.includes("<table")){const e=this.selectedPara.text.replace("</tbody>",t+"\n    </tbody>"),s={thesisId:this.selecedThesisId,paragraphId:this.selectedPara.id,title:this.selectedPara.title,text:e};this.saveParagraphContent(s),this.$message.success("表格行已插入到现有表格中")}else this.$message.warning("当前段落没有表格，请先插入表格")},insertTableColumn(){if(!this.selectedPara||!this.selectedPara.id)return void this.$message.warning("请先选择一个段落");const t='\n      <th style="border: 1px solid #ddd; padding: 12px; text-align: center; background-color: #f5f5f5; font-weight: bold;">新列</th>';if(this.selectedPara.text&&this.selectedPara.text.includes("<table")){const e=this.selectedPara.text.replace("</tr>",t+"\n      </tr>"),s={thesisId:this.selecedThesisId,paragraphId:this.selectedPara.id,title:this.selectedPara.title,text:e};this.saveParagraphContent(s),this.$message.success("表格列已插入到现有表格中")}else this.$message.warning("当前段落没有表格，请先插入表格")},refreshThesisList(t=-1){(0,l.getThesisList)().then((e=>{if(this.thesisList=e.data,this.thesisList.length<1)return this.selecedThesisId=null,this.selecedThesisStatus=null,this.selecedThesis={},this.title="",void(this.paragraphs=[]);t<0&&(t=e.data[0]["id"]),this.queryThesisStatusById(t)})).catch((t=>console.log(t)))},queryThesisStatusById(t){clearInterval(this.ProgressTimerInterval);var e=this.$loading({text:"加载中"});(0,l.getThesisDetail)({thesisId:t}).then((t=>{e.close(),this._setThesisField(t.data),this.$nextTick((()=>{this.statusIsRunning()?this.refreshOutlineInterval():this.refreshOutlineOnce()}))})).catch((t=>{e.close(),this._msgError(t)}))},refreshCurrentThesis(){this.refreshThesisList(this.selecedThesisId),this.statusIsRunning()?(clearInterval(this.ContentRefreshInterval),this.ContentRefreshInterval=setInterval((()=>{this.refreshThesisList(this.selecedThesisId)}),5e3)):clearInterval(this.ContentRefreshInterval)},regenerateParagraph(t){let e=this.$loading({target:'.paragraph_one[data-paraid="'+t["paragraphId"]+'"]',text:"请稍等"});(0,r.gd)(t).then((t=>{e.close(),this.refreshCurrentThesis()})).catch((t=>{e.close(),this.$notify.error(t)}))},handlerGenerateSinglePara(t){this.paragraphRegenFormVisible=!0,this.paragraphRegenForm["paragraphId"]=t["id"],this.paragraphRegenForm["thesisId"]=this.selecedThesisId,this.paragraphRegenForm["paragraphTitle"]=t["title"],this.paragraphRegenForm["length"]=this.globalParagraphLength,this.paragraphRegenForm["lengthMode"]=this.lengthControlMode,this.paragraphRegenForm["minLength"]=this.minParagraphLength,this.paragraphRegenForm["maxLength"]=this.maxParagraphLength,this.paragraphRegenForm["instruction"]=this.globalInstruction,this.calculateEstimatedLength(t)},onLengthModeChange(t){"auto"===t&&this.calculateEstimatedLength()},calculateEstimatedLength(t){let e=500;if(t&&t.title){const s=t.title.toLowerCase();s.includes("引言")||s.includes("结论")?e=800:s.includes("摘要")?e=300:s.includes("方法")||s.includes("实验")?e=600:(s.includes("讨论")||s.includes("分析"))&&(e=700)}this.paragraphRegenForm.estimatedLength=e},handlerGenerateSinglePara2(){let t=0;switch(this.paragraphRegenForm.lengthMode){case"fixed":if(t=parseInt(this.paragraphRegenForm["length"],0),isNaN(t)||t<100||t>3e3)return void this.$message({type:"error",message:"单个段落的字数应该在100-3000字之间"});break;case"range":const e=parseInt(this.paragraphRegenForm["minLength"],0),s=parseInt(this.paragraphRegenForm["maxLength"],0);if(isNaN(e)||isNaN(s)||e<100||s>3e3||e>=s)return void this.$message({type:"error",message:"字数范围设置不正确，最小字数应小于最大字数，且都在100-3000字之间"});t=Math.floor((e+s)/2);break;case"auto":t=this.paragraphRegenForm.estimatedLength;break;default:return void this.$message({type:"error",message:"请选择字数控制方式"})}const e={paragraphId:this.paragraphRegenForm["paragraphId"],thesisId:this.paragraphRegenForm["thesisId"],length:t,instruction:this.paragraphRegenForm["instruction"],contentStyle:this.paragraphRegenForm["contentStyle"],lengthMode:this.paragraphRegenForm["lengthMode"]};"range"===this.paragraphRegenForm.lengthMode&&(e.minLength=this.paragraphRegenForm["minLength"],e.maxLength=this.paragraphRegenForm["maxLength"]),this.paragraphRegenFormVisible=!1,this.$confirm("当前内容将会被覆盖掉,确定要重新生成吗？","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((t=>this.regenerateParagraph(e))).catch((t=>{console.log(t),this.paragraphRegenFormVisible=!1}))},handlerStopGenerate(){this.$confirm("确定要终止当前生成任务吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((()=>{(0,r.n1)({thesisId:this.selecedThesisId}).then((t=>{this._msgSuccess("操作成功"),this.refreshCurrentThesis()})).catch((t=>this.$notify.error("操作失败:"+t)))})).catch((t=>this._msgSuccess("已取消")))},handlerGenerateAll(){if(!this.selecedThesisId)return void this.$notify.info("请先拟定一套论文提纲");if("range"===this.lengthControlMode&&this.minParagraphLength>=this.maxParagraphLength)return void this.$message({type:"error",message:"字数范围设置不正确，最小字数应小于最大字数"});let t=this.$loading({text:"请求中"});const e={thesisId:this.selecedThesisId,globalSettings:{paragraphLength:this.globalParagraphLength,lengthControlMode:this.lengthControlMode,minParagraphLength:this.minParagraphLength,maxParagraphLength:this.maxParagraphLength,globalInstruction:this.globalInstruction}};(0,r.VY)(e).then((e=>{t.close(),this.leftVisiableToggle("outline"),this._msgSuccess(this.$t("contentPage.notifyStartGeneratTask")),this.selecedThesisStatus=this.TaskStatus.RUNING,this.refreshOutlineInterval()})).catch((e=>{t.close(),this._msgError(e)}))},refreshOutlineOnce(){(0,l.getOutline)({thesisId:this.selecedThesisId}).then((t=>this.outlineTree=t.data["outline"])).catch((t=>console.log(t)))},refreshOutlineInterval(){this.ProgressTimerInterval&&(clearInterval(this.ProgressTimerInterval),this.ProgressTimerInterval=null),this.ContentRefreshInterval&&(clearInterval(this.ContentRefreshInterval),this.ContentRefreshInterval=null);let t=()=>{(0,l.getOutline)({thesisId:this.selecedThesisId}).then((t=>{this.outlineTree=t.data["outline"],t.data["thesisStatus"]>12&&(this.ProgressTimerInterval&&(clearInterval(this.ProgressTimerInterval),this.ProgressTimerInterval=null),this.ContentRefreshInterval&&(clearInterval(this.ContentRefreshInterval),this.ContentRefreshInterval=null),this.refreshCurrentThesis(),this.$nextTick((()=>{this.$confirm("大王,全部段落都已经过AI自动生成","提示",{confirmButtonText:"朕知道了",type:"success",closeOnClickModal:!0,closeOnPressEscape:!0,showClose:!0})})))})).catch((t=>{console.log(t),this.ProgressTimerInterval&&(clearInterval(this.ProgressTimerInterval),this.ProgressTimerInterval=null),this.ContentRefreshInterval&&(clearInterval(this.ContentRefreshInterval),this.ContentRefreshInterval=null),console.log("进度查询遇到错误，查询退出")}))};t(),console.log("安装定时器，更新进度"),this.ProgressTimerInterval=setInterval(t,3e3)},async handlerDownload(){try{console.log("开始处理论文下载请求，论文ID:",this.selecedThesisId);const t=this.$loading({lock:!0,text:"正在检查下载权限...",spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"});console.log("调用下载API，检查支付状态");const e=await(0,l.downloadThesis)({thesisId:this.selecedThesisId});if(t.close(),console.log("下载API响应:",e),console.log("响应数据详情:",{success:e.success,data:e.data,need_payment:e.data?.need_payment,price:e.data?.price,file:e.data?.file}),e.data&&!0===e.data.need_payment){console.log("需要支付才能下载论文，价格:",e.data.price);let t="此论文需要付费下载";return e.data.reason&&(t=e.data.reason),this.$notify({title:"论文下载收费提示",message:`${t}，需要支付 ${e.data.price} 元`,type:"warning",duration:5e3,position:"top-right"}),this.paymentData={thesisId:this.selecedThesisId,price:e.data.price||10},this.wechatPayDialogVisible=!0,void console.log("已设置wechatPayDialogVisible为true")}if(e.data&&e.data.file){if(console.log("论文可以下载，文件名:",e.data.file),e.data.free_reason){console.log("免费下载原因:",e.data.free_reason,"消息:",e.data.message);let t="下载成功",s=e.data.message||"论文下载成功",a="success";"first_free"===e.data.free_reason?(t="首次下载免费",s="您是首次下载论文，系统已为您免费开通下载权限！",a="success",this.$notify({title:"🎉 首次下载免费特权",message:"恭喜您获得首次下载免费特权！后续下载将需要支付费用。",type:"success",duration:8e3,position:"top-right"})):"vip_free"===e.data.free_reason?(t="VIP特权",s="您是VIP用户，可以免费下载所有论文！",a="success"):"already_paid"===e.data.free_reason&&(t="下载成功",s="您已支付过此论文，可以免费下载",a="success"),this.$message({message:s,type:a})}console.log("开始下载文件");const t=document.createElement("a");t.href=`/api/thesis/download?fileName=${e.data.file}`,t.target="_blank",document.body.appendChild(t),t.click(),console.log("文件下载链接已点击"),document.body.removeChild(t)}else this.$message.error("下载失败，请稍后重试")}catch(t){console.error("下载论文失败:",t),this.$message.error("下载失败，请稍后重试")}},handleWeChatPaymentSuccess(t){console.log("微信支付成功:",t),this.$message.success("支付成功，正在准备下载..."),this.wechatPayDialogVisible=!1,setTimeout((()=>{this.handlerDownload()}),1e3)},handleWeChatPaymentClose(){console.log("微信支付弹窗关闭"),this.wechatPayDialogVisible=!1},handlerDelete(){this.$confirm("确定要删除《"+this.title+"》论文吗？","提示",{confirmButtonText:"继续删除",cancelButtonText:"不删除了",type:"warning"}).then((()=>{this.$confirm("删除是物理删除，删除后无法找回，请切实确认好?","提示",{confirmButtonText:"执行删除命令",cancelButtonText:"不删除了",type:"warning"}).then((()=>{(0,l.deleteThesis)({thesisId:this.selecedThesisId}).then((t=>{this.$confirm("删除成功","成功",{confirmButtonText:"刷新页面",showCancelButton:!1,showClose:!1,closeOnClickModal:!1,type:"info"}).then((()=>{this.$router.push({path:"/paper/content"})}))})).catch((t=>this.$notify.error("删除失败错误:"+t)))})).catch((t=>this._msgSuccess("已取消删除")))})).catch((t=>this._msgSuccess("已取消删除")))},handlerGotoThesisId(t){this.$router.push({path:"/paper/content",query:{thesisId:t}})},_msgSuccess(t){this.$message({type:"success",message:t})},_msgError(t){this.$message({type:"error",message:t})},_setThesisField(t){this.selecedThesisId=t.id,this.selecedThesisStatus=t.status,this.selecedThesis=t,this.title=t.title,this.thanks=t.thanks,this.references=t.references,this.digest["digest"]=t.digest,this.digest["digestEn"]=t.digestEn,this.digest["keywords"]=t.keywords,this.digest["keywordsEn"]=t.keywordsEn,t.outline&&t.outline.subtitle?(this.paragraphs=t.outline.subtitle,console.log("Content - 设置段落数据成功，段落数量:",this.paragraphs.length)):t.outline?(this.paragraphs=[t.outline],console.log("Content - 使用outline作为段落数据，段落数量:",this.paragraphs.length)):(this.paragraphs=[],console.log("Content - 没有找到段落数据")),this._msgSuccess("加载完成")},saveParagraphLengthSettings(){if("range"===this.lengthControlMode&&this.minParagraphLength>=this.maxParagraphLength)return void this.$message({type:"error",message:"字数范围设置不正确，最小字数应小于最大字数"});const t={globalParagraphLength:this.globalParagraphLength,lengthControlMode:this.lengthControlMode,minParagraphLength:this.minParagraphLength,maxParagraphLength:this.maxParagraphLength,globalInstruction:this.globalInstruction};localStorage.setItem("paragraphLengthSettings",JSON.stringify(t)),this.paragraphLengthSettingsVisible=!1,this.$message({type:"success",message:"段落字数设置已保存"})},loadParagraphLengthSettings(){const t=localStorage.getItem("paragraphLengthSettings");if(t)try{const e=JSON.parse(t);this.globalParagraphLength=e.globalParagraphLength||1e3,this.lengthControlMode=e.lengthControlMode||"fixed",this.minParagraphLength=e.minParagraphLength||100,this.maxParagraphLength=e.maxParagraphLength||3e3,this.globalInstruction=e.globalInstruction||""}catch(e){console.error("加载段落字数设置失败:",e)}},downloadFile(t){console.log("开始下载文件"),this.$notify.success({title:"下载成功",message:"论文下载成功",duration:3e3});let e=document.createElement("a");e.style.display="none",e.href="/api/thesis/download?fileName="+t,document.body.appendChild(e),e.click(),document.body.removeChild(e),console.log("文件下载链接已点击")}}},x=k;var $=(0,_.A)(x,a,i,!1,null,"d41e74b8",null);const S=$.exports},5344:(t,e,s)=>{"use strict";s.r(e),s.d(e,{default:()=>d});var a=function(){var t=this,e=t._self._c;return e("div",{staticClass:"admin-stats-overview"},[e("div",{staticClass:"page-header"},[e("h2",[t._v("数据概览")]),e("div",{staticClass:"header-actions"},[e("el-button",{attrs:{type:"primary",icon:"el-icon-refresh",loading:t.loading},on:{click:t.refreshData}},[t._v(" 刷新数据 ")])],1)]),e("el-row",{staticClass:"stats-cards",attrs:{gutter:20}},[e("el-col",{attrs:{span:6}},[e("el-card",{staticClass:"stats-card",attrs:{shadow:"hover"}},[e("div",{staticClass:"card-content"},[e("div",{staticClass:"card-icon users-icon"},[e("i",{staticClass:"el-icon-user"})]),e("div",{staticClass:"card-info"},[e("div",{staticClass:"card-title"},[t._v("总用户数")]),e("div",{staticClass:"card-value"},[t._v(t._s(t.overview.total_users||0))]),e("div",{staticClass:"card-trend"},[e("span",{staticClass:"trend-label"},[t._v("今日新增:")]),e("span",{staticClass:"trend-value positive"},[t._v("+"+t._s(t.today.new_users||0))])])])])])],1),e("el-col",{attrs:{span:6}},[e("el-card",{staticClass:"stats-card",attrs:{shadow:"hover"}},[e("div",{staticClass:"card-content"},[e("div",{staticClass:"card-icon thesis-icon"},[e("i",{staticClass:"el-icon-document"})]),e("div",{staticClass:"card-info"},[e("div",{staticClass:"card-title"},[t._v("总论文数")]),e("div",{staticClass:"card-value"},[t._v(t._s(t.overview.total_thesis||0))]),e("div",{staticClass:"card-trend"},[e("span",{staticClass:"trend-label"},[t._v("今日新增:")]),e("span",{staticClass:"trend-value positive"},[t._v("+"+t._s(t.today.new_thesis||0))])])])])])],1),e("el-col",{attrs:{span:6}},[e("el-card",{staticClass:"stats-card",attrs:{shadow:"hover"}},[e("div",{staticClass:"card-content"},[e("div",{staticClass:"card-icon chat-icon"},[e("i",{staticClass:"el-icon-chat-dot-round"})]),e("div",{staticClass:"card-info"},[e("div",{staticClass:"card-title"},[t._v("聊天记录")]),e("div",{staticClass:"card-value"},[t._v(t._s(t.overview.total_chat_logs||0))]),e("div",{staticClass:"card-trend"},[e("span",{staticClass:"trend-label"},[t._v("今日新增:")]),e("span",{staticClass:"trend-value positive"},[t._v("+"+t._s(t.today.chat_logs||0))])])])])])],1),e("el-col",{attrs:{span:6}},[e("el-card",{staticClass:"stats-card",attrs:{shadow:"hover"}},[e("div",{staticClass:"card-content"},[e("div",{staticClass:"card-icon vip-icon"},[e("i",{staticClass:"el-icon-medal"})]),e("div",{staticClass:"card-info"},[e("div",{staticClass:"card-title"},[t._v("VIP用户")]),e("div",{staticClass:"card-value"},[t._v(t._s(t.overview.vip_users||0))]),e("div",{staticClass:"card-trend"},[e("span",{staticClass:"trend-label"},[t._v("活跃用户:")]),e("span",{staticClass:"trend-value"},[t._v(t._s(t.overview.active_users||0))])])])])])],1)],1),e("el-row",{staticClass:"stats-details",attrs:{gutter:20}},[e("el-col",{attrs:{span:12}},[e("el-card",{staticClass:"detail-card"},[e("div",{attrs:{slot:"header"},slot:"header"},[e("span",[t._v("用户统计详情")])]),e("div",{staticClass:"detail-content"},[e("div",{staticClass:"detail-item"},[e("span",{staticClass:"item-label"},[t._v("VIP用户:")]),e("span",{staticClass:"item-value"},[t._v(t._s(t.overview.vip_users||0))])]),e("div",{staticClass:"detail-item"},[e("span",{staticClass:"item-label"},[t._v("过期VIP:")]),e("span",{staticClass:"item-value"},[t._v(t._s(t.overview.expired_vip_users||0))])]),e("div",{staticClass:"detail-item"},[e("span",{staticClass:"item-label"},[t._v("活跃用户(7天):")]),e("span",{staticClass:"item-value"},[t._v(t._s(t.overview.active_users||0))])]),e("div",{staticClass:"detail-item"},[e("span",{staticClass:"item-label"},[t._v("昨日新增用户:")]),e("span",{staticClass:"item-value"},[t._v(t._s(t.yesterday.new_users||0))])]),e("div",{staticClass:"detail-item"},[e("span",{staticClass:"item-label"},[t._v("昨日新增论文:")]),e("span",{staticClass:"item-value"},[t._v(t._s(t.yesterday.new_thesis||0))])])])])],1),e("el-col",{attrs:{span:12}},[e("el-card",{staticClass:"detail-card"},[e("div",{attrs:{slot:"header"},slot:"header"},[e("span",[t._v("系统状态")])]),e("div",{staticClass:"detail-content"},[e("div",{staticClass:"status-item"},[e("div",{staticClass:"status-icon success"},[e("i",{staticClass:"el-icon-success"})]),e("div",{staticClass:"status-info"},[e("div",{staticClass:"status-title"},[t._v("系统运行正常")]),e("div",{staticClass:"status-desc"},[t._v("所有服务运行正常")])])]),e("div",{staticClass:"status-item"},[e("div",{staticClass:"status-icon info"},[e("i",{staticClass:"el-icon-info"})]),e("div",{staticClass:"status-info"},[e("div",{staticClass:"status-title"},[t._v("数据库连接")]),e("div",{staticClass:"status-desc"},[t._v("连接正常")])])]),e("div",{staticClass:"status-item"},[e("div",{staticClass:"status-icon warning"},[e("i",{staticClass:"el-icon-warning"})]),e("div",{staticClass:"status-info"},[e("div",{staticClass:"status-title"},[t._v("存储空间")]),e("div",{staticClass:"status-desc"},[t._v("使用率 75%")])])])])])],1)],1)],1)},i=[],r=s(9192);const l={name:"AdminStatsOverview",data(){return{loading:!1,overview:{},today:{},yesterday:{}}},mounted(){this.loadData()},methods:{async loadData(){try{this.loading=!0;const t=await r.bk.getOverview();t.success?(this.overview=t.data.overview||{},this.today=t.data.today||{},this.yesterday=t.data.yesterday||{}):this.$message.error(t.message||"获取数据失败")}catch(t){console.error("加载数据概览失败:",t),this.$message.error("加载数据失败")}finally{this.loading=!1}},refreshData(){this.loadData()}}},n=l;var o=s(1656),c=(0,o.A)(n,a,i,!1,null,"9d1d41c6",null);const d=c.exports},5539:(t,e,s)=>{"use strict";s.r(e),s.d(e,{default:()=>c});var a=function(){var t=this,e=t._self._c;return e("el-dialog",{attrs:{title:"智能表格",visible:t.visible,width:"800px","append-to-body":"","before-close":t.handleClose},on:{"update:visible":function(e){t.visible=e}}},[e("div",{staticClass:"table-editor"},[e("div",{staticClass:"ai-section"},[e("h4",[t._v("AI生成表格")]),e("el-form",{attrs:{model:t.aiConfig,"label-width":"80px",size:"small"}},[e("el-form-item",{attrs:{label:"内容"}},[t.context&&t.context.content?e("div",{staticClass:"auto-fill-tip"},[e("el-alert",{attrs:{title:"✅ 已自动填充当前段落内容",type:"success",closable:!1,"show-icon":"",size:"small"}},[e("template",{slot:"default"},[t._v(' 系统已自动读取当前段落内容，AI将根据内容自动判断合适的表格结构。您可以直接点击"生成表格"按钮，或修改内容后重新生成。 ')])],2)],1):t._e(),e("el-input",{attrs:{type:"textarea",rows:4,placeholder:"输入要生成表格的内容"},model:{value:t.aiConfig.content,callback:function(e){t.$set(t.aiConfig,"content",e)},expression:"aiConfig.content"}})],1),e("el-form-item",{attrs:{label:"表格设置"}},[e("el-checkbox",{model:{value:t.aiConfig.autoAdjust,callback:function(e){t.$set(t.aiConfig,"autoAdjust",e)},expression:"aiConfig.autoAdjust"}},[t._v("自动调整行列数")]),t.aiConfig.autoAdjust?t._e():e("div",{staticStyle:{"margin-top":"10px"}},[e("el-row",{attrs:{gutter:10}},[e("el-col",{attrs:{span:12}},[e("el-input-number",{attrs:{min:1,max:10,size:"small",placeholder:"行数"},model:{value:t.aiConfig.rows,callback:function(e){t.$set(t.aiConfig,"rows",e)},expression:"aiConfig.rows"}}),e("span",{staticStyle:{"margin-left":"5px"}},[t._v("行")])],1),e("el-col",{attrs:{span:12}},[e("el-input-number",{attrs:{min:1,max:8,size:"small",placeholder:"列数"},model:{value:t.aiConfig.columns,callback:function(e){t.$set(t.aiConfig,"columns",e)},expression:"aiConfig.columns"}}),e("span",{staticStyle:{"margin-left":"5px"}},[t._v("列")])],1)],1)],1)],1),e("el-form-item",[e("el-button",{attrs:{type:"primary",loading:t.generating},on:{click:t.generateTableContent}},[t._v(" 生成表格 ")])],1)],1)],1),t.aiPreviewHtml?e("div",{staticClass:"preview-section"},[e("h4",[t._v("AI生成的表格预览")]),e("div",{staticClass:"preview-table",domProps:{innerHTML:t._s(t.aiPreviewHtml)}}),e("div",{staticClass:"preview-actions"},[e("el-button",{attrs:{type:"success"},on:{click:t.applyAiTable}},[t._v("直接插入")]),e("el-button",{attrs:{type:"primary"},on:{click:t.editAiTable}},[t._v("编辑表格")]),e("el-button",{on:{click:t.regenerateTable}},[t._v("重新生成")])],1)]):t._e(),t.aiPreviewHtml?t._e():e("div",{staticClass:"manual-section"},[e("h4",[t._v("手动编辑表格")]),e("div",{staticClass:"table-actions"},[e("el-button",{attrs:{size:"small"},on:{click:t.addRow}},[t._v("添加行")]),e("el-button",{attrs:{size:"small"},on:{click:t.addColumn}},[t._v("添加列")])],1),e("div",{staticClass:"table-wrapper"},[e("table",{staticClass:"editable-table"},[e("thead",[e("tr",[t._l(t.tableData[0],(function(s,a){return e("th",{key:a},[e("el-input",{attrs:{size:"small",placeholder:"表头"},model:{value:t.tableData[0][a],callback:function(e){t.$set(t.tableData[0],a,e)},expression:"tableData[0][colIndex]"}}),e("el-button",{attrs:{size:"mini",type:"danger"},on:{click:function(e){return t.deleteColumn(a)}}},[t._v("删除")])],1)})),e("th",[e("el-button",{attrs:{size:"small"},on:{click:t.addColumn}},[t._v("+")])],1)],2)]),e("tbody",t._l(t.tableData.slice(1),(function(s,a){return e("tr",{key:a},[t._l(s,(function(s,i){return e("td",{key:i},[e("el-input",{attrs:{size:"small",type:"textarea",rows:2,placeholder:"内容"},model:{value:t.tableData[a+1][i],callback:function(e){t.$set(t.tableData[a+1],i,e)},expression:"tableData[rowIndex + 1][colIndex]"}})],1)})),e("td",[e("el-button",{attrs:{size:"mini",type:"danger"},on:{click:function(e){return t.deleteRow(a+1)}}},[t._v("删除")])],1)],2)})),0)])])])]),e("div",{attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:t.handleClose}},[t._v("取消")]),e("el-button",{attrs:{type:"primary"},on:{click:t.handleConfirm}},[t._v("插入表格")])],1)])},i=[];s(6960);const r={name:"TableEditor",props:{visible:{type:Boolean,default:!1},context:{type:Object,default:()=>({})}},data(){return{visible:!1,generating:!1,tableConfig:{title:"",width:"100%"},aiConfig:{content:"",autoAdjust:!0,rows:3,columns:3},tableData:[["列1","列2","列3"],["内容1-1","内容1-2","内容1-3"],["内容2-1","内容2-2","内容2-3"]],aiPreviewHtml:"",tableTitle:""}},mounted(){this.context&&this.context.content&&(this.aiConfig.content=this.context.content,this.$message.success('已自动填充段落内容，请点击"生成表格"按钮'))},watch:{visible(t){t&&(this.updatePreview(),this.context&&this.context.content&&(this.aiConfig.content=this.context.content,this.$message.success('已自动填充段落内容，请点击"生成表格"按钮')))},context:{handler(t){t&&t.content&&this.visible&&(this.aiConfig.content=t.content,this.$message.success('已自动填充段落内容，请点击"生成表格"按钮'))},deep:!0}},methods:{addRow(){const t=new Array(this.tableData[0].length).fill("");this.tableData.push(t)},addColumn(){this.tableData.forEach((t=>t.push("")))},deleteRow(t){this.tableData.splice(t,1)},deleteColumn(t){for(let e=0;e<this.tableData.length;e++)this.tableData[e].splice(t,1)},async generateTableContent(){if(this.aiConfig.content.trim()){this.generating=!0;try{const{generateTableContent:t}=await Promise.resolve().then(s.bind(s,6960)),e=await t({content:this.aiConfig.content,rows:this.aiConfig.autoAdjust?0:this.aiConfig.rows,columns:this.aiConfig.autoAdjust?0:this.aiConfig.columns,requirements:"请基于段落内容生成详细的数据表格，包含具体的数据和对比信息，表格要清晰易读。请根据内容自动判断合适的表格结构，不要限制行列数。"});e.data?(this.aiPreviewHtml=this.generateTableHtmlFromResponse(e.data),this.$message.success("AI表格生成完成！")):this.$message.error(e.message||"生成失败")}catch(t){console.error("生成表格失败:",t),this.$message.error("生成表格失败")}finally{this.generating=!1}}else this.$message.warning("请输入要生成表格的内容")},generateTableHtmlFromResponse(t){let e=t.tableData||[];if(!e||0===e.length)if(t.content)e=this.parseContentToTableData(t.content);else if(t.html)return t.html;let s="",a="";t.title?a=t.title:this.tableTitle&&(a=this.tableTitle),a&&(s+=`<p style="text-align: center; font-weight: bold; margin-bottom: 10px; font-size: 16px;">${a}</p>`),s+='<table style="width: 100%; border-collapse: collapse; margin: 10px 0;">',e[0]&&e[0].length>0&&(s+="<thead><tr>",e[0].forEach((t=>{s+=`<th style="padding: 8px; text-align: center; border: 1px solid #ddd; background-color: #f5f5f5;">${t}</th>`})),s+="</tr></thead>"),s+="<tbody>";for(let i=1;i<e.length;i++)s+="<tr>",e[i].forEach((t=>{s+=`<td style="padding: 8px; border: 1px solid #ddd;">${t}</td>`})),s+="</tr>";return s+="</tbody></table>",s},parseContentToTableData(t){const e=t.split("\n").filter((t=>t.trim())),s=[];if(0===e.length)return s;const a=e[0],i=a.split(/\t|,|;|\|/).map((t=>t.trim())),r=["研究","技术","方法","分析","测试","检测","改进","措施","成果"],l=r.some((t=>a.includes(t)))||a.length<50||i.length<=3;if(l&&e.length>1){for(let t=1;t<e.length;t++){const a=e[t].split(/\t|,|;|\|/).map((t=>t.trim()));a.length>0&&s.push(a)}this.tableTitle=a}else e.forEach((t=>{const e=t.split(/\t|,|;|\|/).map((t=>t.trim()));e.length>0&&s.push(e)}));return s},applyAiTable(){this.aiPreviewHtml&&(this.$emit("onConfirm",this.aiPreviewHtml),this.aiPreviewHtml="",this.$message.success("表格已插入！如需删除，可在段落内容中手动编辑。"))},editAiTable(){this.convertHtmlToTableData(this.aiPreviewHtml),this.aiPreviewHtml="",this.$message.success("已切换到编辑模式，您可以手动修改表格内容")},convertHtmlToTableData(t){const e=document.createElement("div");e.innerHTML=t;const s=e.querySelector("table");if(s){const t=s.querySelectorAll("tr"),e=[];t.forEach((t=>{const s=t.querySelectorAll("th, td"),a=[];s.forEach((t=>{a.push(t.textContent.trim())})),e.push(a)})),e.length>0?(this.tableData=e,this.$message.success("AI生成的表格已加载到编辑器中")):this.$message.warning("无法解析表格数据，请重新生成")}else this.$message.error("未找到表格元素")},regenerateTable(){this.aiPreviewHtml="",this.generateTableContent()},generateTableHtml(){let t='<table style="width: 100%; border-collapse: collapse; margin: 10px 0;">';this.tableData[0]&&this.tableData[0].length>0&&(t+="<thead><tr>",this.tableData[0].forEach((e=>{t+=`<th style="padding: 8px; text-align: center; border: 1px solid #ddd; background-color: #f5f5f5;">${e}</th>`})),t+="</tr></thead>"),t+="<tbody>";for(let e=1;e<this.tableData.length;e++)t+="<tr>",this.tableData[e].forEach((e=>{t+=`<td style="padding: 8px; border: 1px solid #ddd;">${e}</td>`})),t+="</tr>";return t+="</tbody></table>",t},handleConfirm(){const t=this.aiPreviewHtml||this.generateTableHtml();this.$emit("onConfirm",t),this.$message.success("表格已插入！如需删除，可在段落内容中手动编辑。"),this.handleClose()},handleClose(){this.visible=!1,this.aiPreviewHtml="",this.tableTitle="",this.tableData=[["列1","列2","列3"],["内容1-1","内容1-2","内容1-3"],["内容2-1","内容2-2","内容2-3"]],this.aiConfig.content=""},updatePreview(){}}},l=r;var n=s(1656),o=(0,n.A)(l,a,i,!1,null,"5a989d67",null);const c=o.exports},6018:(t,e,s)=>{"use strict";s.r(e),s.d(e,{default:()=>c});var a=function(){var t=this,e=t._self._c;return e("div",{staticClass:"admin-thesis-stats"},[e("el-card",[e("div",{attrs:{slot:"header"},slot:"header"},[e("span",[t._v("论文统计")]),e("el-button",{staticStyle:{float:"right",padding:"3px 0"},attrs:{type:"text"},on:{click:t.refreshData}},[t._v(" 刷新 ")])],1),e("div",{staticClass:"stats-content"},[e("p",[t._v("论文统计功能开发中...")])])])],1)},i=[];const r={name:"AdminThesisStats",methods:{refreshData(){this.$message.info("刷新功能开发中...")}}},l=r;var n=s(1656),o=(0,n.A)(l,a,i,!1,null,"3c6afb19",null);const c=o.exports},6154:(t,e,s)=>{"use strict";s.r(e),s.d(e,{default:()=>d});var a=function(){var t=this,e=t._self._c;return e("div",{staticClass:"admin-account-edit-container"},[e("el-card",{staticClass:"box-card"},[e("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[e("span",[t._v(t._s(t.isEdit?"编辑管理员":"创建管理员"))])]),e("el-form",{ref:"form",staticClass:"form",attrs:{model:t.form,rules:t.rules,"label-width":"120px"}},[e("el-form-item",{attrs:{label:"用户名",prop:"username",disabled:t.isEdit}},[e("el-input",{attrs:{disabled:t.isEdit,placeholder:"请输入用户名"},model:{value:t.form.username,callback:function(e){t.$set(t.form,"username",e)},expression:"form.username"}})],1),t.isEdit?t._e():e("el-form-item",{attrs:{label:"密码",prop:"password"}},[e("el-input",{attrs:{type:"password","show-password":"",placeholder:"请输入密码"},model:{value:t.form.password,callback:function(e){t.$set(t.form,"password",e)},expression:"form.password"}})],1),t.isEdit?t._e():e("el-form-item",{attrs:{label:"确认密码",prop:"confirm_password"}},[e("el-input",{attrs:{type:"password","show-password":"",placeholder:"请确认密码"},model:{value:t.form.confirm_password,callback:function(e){t.$set(t.form,"confirm_password",e)},expression:"form.confirm_password"}})],1),e("el-form-item",{attrs:{label:"姓名",prop:"realname"}},[e("el-input",{attrs:{placeholder:"请输入姓名"},model:{value:t.form.realname,callback:function(e){t.$set(t.form,"realname",e)},expression:"form.realname"}})],1),e("el-form-item",{attrs:{label:"邮箱",prop:"email"}},[e("el-input",{attrs:{placeholder:"请输入邮箱"},model:{value:t.form.email,callback:function(e){t.$set(t.form,"email",e)},expression:"form.email"}})],1),e("el-form-item",{attrs:{label:"电话",prop:"phone"}},[e("el-input",{attrs:{placeholder:"请输入电话"},model:{value:t.form.phone,callback:function(e){t.$set(t.form,"phone",e)},expression:"form.phone"}})],1),e("el-form-item",{attrs:{label:"状态",prop:"is_active"}},[e("el-switch",{attrs:{"active-text":"启用","inactive-text":"禁用"},model:{value:t.form.is_active,callback:function(e){t.$set(t.form,"is_active",e)},expression:"form.is_active"}})],1),e("el-form-item",[e("el-button",{attrs:{type:"primary"},on:{click:t.submitForm}},[t._v("保存")]),e("el-button",{on:{click:t.cancel}},[t._v("取消")])],1)],1)],1)],1)},i=[],r=s(9192);const l={name:"AdminAccountEdit",data(){const t=(t,e,s)=>{e!==this.form.password?s(new Error("两次输入密码不一致")):s()};return{isEdit:!1,adminId:null,form:{username:"",password:"",confirm_password:"",realname:"",email:"",phone:"",is_active:!0},rules:{username:[{required:!0,message:"请输入用户名",trigger:"blur"},{min:3,max:20,message:"长度在 3 到 20 个字符",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:6,message:"密码长度不能少于6个字符",trigger:"blur"}],confirm_password:[{required:!0,message:"请确认密码",trigger:"blur"},{validator:t,trigger:"blur"}],email:[{type:"email",message:"请输入正确的邮箱地址",trigger:"blur"}],phone:[{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号码",trigger:"blur"}]}}},created(){this.$route.params.id&&(this.isEdit=!0,this.adminId=this.$route.params.id,this.getAdminDetail())},methods:{getAdminDetail(){r.jC.getDetail(this.adminId).then((t=>{if(t.success){const e=t.data;this.form={username:e.username,realname:e.realname||"",email:e.email||"",phone:e.phone||"",is_active:e.is_active}}else this.$message.error(t.message||"获取管理员信息失败")}))},submitForm(){this.$refs.form.validate((t=>{if(t)if(this.isEdit){const t={realname:this.form.realname,email:this.form.email,phone:this.form.phone,is_active:this.form.is_active};r.jC.update(this.adminId,t).then((t=>{t.success?(this.$message.success("管理员信息更新成功"),this.$router.push({name:"AdminAccountsList"})):this.$message.error(t.message||"管理员信息更新失败")}))}else{const t={username:this.form.username,password:this.form.password,realname:this.form.realname,email:this.form.email,phone:this.form.phone,is_active:this.form.is_active};r.jC.create(t).then((t=>{t.success?(this.$message.success("管理员创建成功"),this.$router.push({name:"AdminAccountsList"})):this.$message.error(t.message||"管理员创建失败")}))}}))},cancel(){this.$router.push({name:"AdminAccountsList"})}}},n=l;var o=s(1656),c=(0,o.A)(n,a,i,!1,null,"2ae70c77",null);const d=c.exports},6306:(t,e,s)=>{"use strict";s.r(e),s.d(e,{default:()=>c});var a=function(){var t=this,e=t._self._c;return t.isShow?e("div",{staticClass:"progress-box-wrapper"},[e("div",{staticClass:"progress-box_bg"}),e("div",{class:t.isFold?"progress-box progress-box_fold":"progress-box progress-box_unfold"},[e("div",{staticClass:"progress-box_title"},[e("i",{staticClass:"el-icon-loading"}),t._v("AI引擎逐个段落生成内容。 "),e("small",{staticStyle:{display:"block","margin-top":"10px"}},[t._v("共 "+t._s(t.totalParasCount)+" 段,已完成 "+t._s(t.doneParasCount)+" 段")])]),e("div",{class:"progress-box_body "+t.bodyFadeClass},t._l(t.progressList,(function(s,a){return e("div",{key:s.paraId,class:"task "+s.state},[e("div",{staticClass:"task-name"},[t._v(t._s(a+1)+". "+t._s(s.title))]),e("div",{staticClass:"task-state"},["e"==s.state?e("i",{staticClass:"el-icon-close",attrs:{alt:"生成错误"}}):t._e(),"s"==s.state?e("i",{staticClass:"el-icon-check",attrs:{alt:"已经生成完毕"}}):t._e(),"w"==s.state?e("i",{staticClass:"el-icon-timer",attrs:{alt:"等待生成"}}):t._e(),"r"==s.state?e("i",{staticClass:"el-icon-loading",attrs:{alt:"生成中"}}):t._e()])])})),0),e("div",{staticClass:"progress-box_bottom"},[t.isFold?t._e():e("el-button",{attrs:{type:"success",plain:""},on:{click:function(e){return e.stopPropagation(),t.handlerFold.apply(null,arguments)}}},[t._v("继续等待")]),t.isFold?e("el-button",{attrs:{type:"success",plain:""},on:{click:function(e){return e.stopPropagation(),t.handlerUnfold.apply(null,arguments)}}},[t._v("查看内容生成进度")]):t._e(),e("el-button",{attrs:{type:"warning"},on:{click:function(e){return e.stopPropagation(),t.handlerStop.apply(null,arguments)}}},[t._v("终止生成")])],1)])]):t._e()},i=[];const r={props:{progressList:{default:[],type:Array}},data(){return{title:"",tasks:[],isFold:!1,bodyFadeClass:""}},computed:{isShow(){return this.progressList&&this.progressList.length>0},totalParasCount(){return this.progressList?this.progressList.length:0},doneParasCount(){if(!this.progressList)return 0;let t=0;for(let e in this.progressList)"w"!=this.progressList[e]["state"]&&(t+=1);return t}},watch:{isFold(t,e){this.bodyFadeClass=t?"fadeout":"fadein"},progressList(t,e){0==t.length&&e.length>0&&this.$confirm("任务运行结束","提示",{confirmButtonText:"确定",showCancelButton:!1,type:"success",center:!0})}},methods:{handlerFold(){this.isFold=!0},handlerUnfold(){this.isFold=!1},handlerStop(){}}},l=r;var n=s(1656),o=(0,n.A)(l,a,i,!1,null,"77277c2e",null);const c=o.exports},6364:(t,e,s)=>{"use strict";s.r(e),s.d(e,{default:()=>c});var a=function(){var t=this,e=t._self._c;return e("div",{staticClass:"aicg-container"},[e("iframe",{ref:"aicgIframe",staticClass:"aicg-iframe",attrs:{src:"https://ai.zaoniao.vip/",frameborder:"0",allowfullscreen:""}})])},i=[];const r={name:"AICG应用",data(){return{isLoading:!0}},mounted(){this.$refs.aicgIframe&&(this.$refs.aicgIframe.onload=()=>{this.isLoading=!1})}},l=r;var n=s(1656),o=(0,n.A)(l,a,i,!1,null,"bc83d668",null);const c=o.exports},6489:(t,e,s)=>{"use strict";s.r(e),s.d(e,{default:()=>c});var a=function(){var t=this,e=t._self._c;return e("div",{staticClass:"setting-container"},[e("div",{staticClass:"setting-card"},[t._m(0),e("div",{staticClass:"setting-disabled"},[t._m(1),e("div",{staticClass:"disabled-content"},[e("h3",[t._v("设置功能已迁移")]),e("p",[t._v("系统设置功能已迁移到后台管理系统，用户端不再提供设置功能。")]),e("p",[t._v("如需修改系统配置，请联系管理员在后台管理系统中进行操作。")]),e("div",{staticClass:"action-buttons"},[e("el-button",{attrs:{type:"primary"},on:{click:t.goBack}},[e("i",{staticClass:"el-icon-back"}),t._v(" 返回首页 ")]),e("el-button",{on:{click:t.goToAdmin}},[e("i",{staticClass:"el-icon-s-custom"}),t._v(" 管理员登录 ")])],1)])])])])},i=[function(){var t=this,e=t._self._c;return e("div",{staticClass:"card-header"},[e("i",{staticClass:"el-icon-setting"}),e("h2",[t._v("系统设置")])])},function(){var t=this,e=t._self._c;return e("div",{staticClass:"disabled-icon"},[e("i",{staticClass:"el-icon-warning-outline"})])}];const r={name:"Setting",methods:{goBack(){this.$router.push("/")},goToAdmin(){this.$router.push("/admin/login")}}},l=r;var n=s(1656),o=(0,n.A)(l,a,i,!1,null,"ee07de4c",null);const c=o.exports},6632:(t,e,s)=>{"use strict";s.r(e),s.d(e,{default:()=>d});var a=function(){var t=this,e=t._self._c;return e("div",{staticClass:"admin-accounts-container"},[e("div",{staticClass:"page-header"},[e("div",{staticClass:"header-content"},[t._m(0),e("div",{staticClass:"header-actions"},[e("el-button",{attrs:{type:"primary",icon:"el-icon-plus",size:"medium"},on:{click:t.handleCreate}},[t._v(" 新增管理员 ")]),e("el-button",{attrs:{type:"success",icon:"el-icon-refresh",size:"medium"},on:{click:t.fetchData}},[t._v(" 刷新数据 ")])],1)])]),e("el-card",{staticClass:"search-card",attrs:{shadow:"hover"}},[e("div",{staticClass:"search-header"},[e("h3",{staticClass:"search-title"},[e("i",{staticClass:"el-icon-search"}),t._v(" 搜索筛选 ")])]),e("el-form",{staticClass:"search-form",attrs:{inline:!0,model:t.listQuery}},[e("el-form-item",{attrs:{label:"关键词"}},[e("el-input",{staticClass:"search-input",attrs:{placeholder:"用户名/姓名/邮箱",clearable:"","prefix-icon":"el-icon-search"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.handleFilter.apply(null,arguments)}},model:{value:t.listQuery.keyword,callback:function(e){t.$set(t.listQuery,"keyword",e)},expression:"listQuery.keyword"}})],1),e("el-form-item",{staticClass:"search-buttons"},[e("el-button",{staticClass:"search-btn",attrs:{type:"primary",icon:"el-icon-search"},on:{click:t.handleFilter}},[t._v(" 搜索 ")]),e("el-button",{staticClass:"reset-btn",attrs:{icon:"el-icon-refresh"},on:{click:t.resetSearch}},[t._v(" 重置 ")])],1)],1)],1),e("el-card",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"table-card",attrs:{shadow:"hover"}},[e("div",{staticClass:"table-header"},[e("h3",{staticClass:"table-title"},[e("i",{staticClass:"el-icon-document"}),t._v(" 管理员列表 ")]),e("div",{staticClass:"table-actions"},[e("el-button",{attrs:{size:"small",icon:"el-icon-download"},on:{click:t.exportData}},[t._v(" 导出数据 ")])],1)]),t.error?e("el-alert",{staticClass:"error-alert",attrs:{title:"数据加载失败",type:"error",description:t.error,"show-icon":"",closable:!1}},[e("el-button",{staticClass:"retry-btn",attrs:{size:"small",type:"primary"},on:{click:t.fetchData}},[t._v("重试")])],1):t._e(),t.error?t._e():e("el-table",{staticStyle:{width:"100%"},attrs:{data:t.list,border:"",stripe:"","header-cell-style":{background:"#f5f7fa",color:"#606266"}}},[e("el-table-column",{attrs:{prop:"id",label:"ID",width:"80",align:"center"}}),e("el-table-column",{attrs:{prop:"username",label:"用户名","min-width":"120"}}),e("el-table-column",{attrs:{prop:"realname",label:"姓名","min-width":"120"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.row.realname||"-")+" ")]}}],null,!1,1471145037)}),e("el-table-column",{attrs:{prop:"email",label:"邮箱","min-width":"180"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.row.email||"-")+" ")]}}],null,!1,2719091484)}),e("el-table-column",{attrs:{label:"角色",width:"120",align:"center"},scopedSlots:t._u([{key:"default",fn:function(s){return[s.row.is_superadmin?e("el-tag",{attrs:{type:"success",effect:"dark"}},[t._v(" 超级管理员 ")]):e("el-tag",{attrs:{type:"info",effect:"plain"}},[t._v(" 管理员 ")])]}}],null,!1,3487911326)}),e("el-table-column",{attrs:{label:"状态",width:"100",align:"center"},scopedSlots:t._u([{key:"default",fn:function(s){return[s.row.is_active?e("el-tag",{attrs:{type:"success"}},[t._v("正常")]):e("el-tag",{attrs:{type:"danger"}},[t._v("禁用")])]}}],null,!1,1047381183)}),e("el-table-column",{attrs:{prop:"created_at",label:"创建时间",width:"160",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.row.created_at||"-")+" ")]}}],null,!1,1473701626)}),e("el-table-column",{attrs:{label:"操作",width:"280",fixed:"right",align:"center"},scopedSlots:t._u([{key:"default",fn:function(s){return[e("el-button",{attrs:{size:"mini",type:"primary",icon:"el-icon-edit"},on:{click:function(e){return t.handleUpdate(s.row)}}},[t._v(" 编辑 ")]),e("el-button",{attrs:{size:"mini",type:"warning",icon:"el-icon-key"},on:{click:function(e){return t.handleResetPassword(s.row)}}},[t._v(" 重置密码 ")]),e("el-button",{attrs:{size:"mini",type:"danger",icon:"el-icon-delete",disabled:s.row.is_superadmin},on:{click:function(e){return t.handleDelete(s.row)}}},[t._v(" 删除 ")])]}}],null,!1,2548645454)}),e("template",{slot:"empty"},[e("div",{staticClass:"empty-data"},[e("i",{staticClass:"el-icon-document"}),e("p",[t._v("暂无数据")])])])],2),e("div",{staticClass:"pagination-container"},[e("el-pagination",{attrs:{background:"",layout:"total, sizes, prev, pager, next, jumper","current-page":t.listQuery.page,"page-sizes":[10,20,50,100],"page-size":t.listQuery.size,total:t.total},on:{"update:currentPage":function(e){return t.$set(t.listQuery,"page",e)},"update:current-page":function(e){return t.$set(t.listQuery,"page",e)},"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1)],1),e("el-dialog",{attrs:{title:"重置密码",visible:t.passwordDialogVisible,width:"400px","close-on-click-modal":!1},on:{"update:visible":function(e){t.passwordDialogVisible=e}}},[e("el-form",{ref:"passwordForm",attrs:{model:t.passwordForm,rules:t.passwordRules,"label-width":"100px"}},[e("el-form-item",{attrs:{label:"新密码",prop:"new_password"}},[e("el-input",{attrs:{type:"password","show-password":"",placeholder:"请输入新密码"},model:{value:t.passwordForm.new_password,callback:function(e){t.$set(t.passwordForm,"new_password",e)},expression:"passwordForm.new_password"}})],1),e("el-form-item",{attrs:{label:"确认密码",prop:"confirm_password"}},[e("el-input",{attrs:{type:"password","show-password":"",placeholder:"请再次输入密码"},model:{value:t.passwordForm.confirm_password,callback:function(e){t.$set(t.passwordForm,"confirm_password",e)},expression:"passwordForm.confirm_password"}})],1)],1),e("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.passwordDialogVisible=!1}}},[t._v("取 消")]),e("el-button",{attrs:{type:"primary",loading:t.passwordLoading},on:{click:t.confirmResetPassword}},[t._v(" 确 定 ")])],1)],1),e("el-dialog",{attrs:{title:"删除确认",visible:t.deleteDialogVisible,width:"400px","close-on-click-modal":!1},on:{"update:visible":function(e){t.deleteDialogVisible=e}}},[e("div",{staticClass:"delete-confirm-content"},[e("i",{staticClass:"el-icon-warning warning-icon"}),e("div",{staticClass:"confirm-text"},[e("p",[t._v('确定要删除管理员 "'+t._s(t.currentAdmin?t.currentAdmin.username:"")+'" 吗？')]),e("p",{staticClass:"warning-text"},[t._v("此操作不可逆，请谨慎操作！")])])]),e("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.deleteDialogVisible=!1}}},[t._v("取 消")]),e("el-button",{attrs:{type:"danger",loading:t.deleteLoading},on:{click:t.confirmDelete}},[t._v(" 确认删除 ")])],1)])],1)},i=[function(){var t=this,e=t._self._c;return e("div",{staticClass:"header-left"},[e("div",{staticClass:"title-section"},[e("h1",{staticClass:"page-title"},[e("i",{staticClass:"el-icon-user"}),t._v(" 管理员账户 ")]),e("p",{staticClass:"page-subtitle"},[t._v("管理系统中的管理员账户和权限")])])])}],r=s(9192);const l={name:"AdminAccountsList",data(){const t=(t,e,s)=>{""===e?s(new Error("请输入密码")):e.length<6?s(new Error("密码长度不能少于6个字符")):(""!==this.passwordForm.confirm_password&&this.$refs.passwordForm.validateField("confirm_password"),s())},e=(t,e,s)=>{""===e?s(new Error("请再次输入密码")):e!==this.passwordForm.new_password?s(new Error("两次输入密码不一致")):s()};return{loading:!1,error:null,listQuery:{page:1,size:10,keyword:""},list:[],total:0,passwordDialogVisible:!1,passwordLoading:!1,passwordForm:{new_password:"",confirm_password:""},passwordRules:{new_password:[{validator:t,trigger:"blur"}],confirm_password:[{validator:e,trigger:"blur"}]},currentAdmin:null,deleteDialogVisible:!1,deleteLoading:!1}},computed:{totalPages(){return Math.ceil(this.total/this.listQuery.size)||1}},created(){this.fetchData()},methods:{fetchData(){this.loading=!0,this.error=null,console.log("开始获取管理员列表数据..."),r.jC.getList(this.listQuery).then((t=>{this.loading=!1,console.log("管理员列表API响应:",t),t.success?(this.list=t.data.list||[],this.total=t.data.total||0,console.log(`管理员列表数据加载成功，共${this.total}条记录，当前显示${this.list.length}条`)):(this.error=t.message||"获取管理员列表失败",console.error("管理员列表API业务错误:",this.error),t.data&&t.data.list&&(this.list=t.data.list,this.total=t.data.total||0,this.error=null))})).catch((t=>{this.loading=!1,this.error="网络错误，请稍后重试",console.error("获取管理员列表失败:",t),this.useMockData()}))},useMockData(){console.log("使用模拟数据");const t=[{id:1,username:"admin",realname:"系统管理员",email:"<EMAIL>",phone:"13800138000",is_superadmin:!0,is_active:!0,created_at:"2025-06-21 08:59:17",last_login_at:"2025-06-25 15:30:00"},{id:2,username:"ceshiadmin",realname:"测试管理员",email:"<EMAIL>",phone:"***********",is_superadmin:!1,is_active:!0,created_at:"2025-06-25 11:26:30",last_login_at:null}];this.list=t,this.total=t.length,this.error=null},handleFilter(){this.listQuery.page=1,this.fetchData()},resetSearch(){this.listQuery.keyword="",this.handleFilter()},handleSizeChange(t){this.listQuery.size=t,this.fetchData()},handleCurrentChange(t){this.listQuery.page=t,this.fetchData()},handleCreate(){this.$router.push({name:"AdminAccountsCreate"})},handleUpdate(t){this.$router.push({name:"AdminAccountsEdit",params:{id:t.id}})},handleResetPassword(t){this.currentAdmin=t,this.passwordForm.new_password="",this.passwordForm.confirm_password="",this.passwordDialogVisible=!0,this.$refs.passwordForm&&this.$refs.passwordForm.resetFields()},confirmResetPassword(){this.$refs.passwordForm.validate((t=>{t&&(this.passwordLoading=!0,r.jC.resetPassword(this.currentAdmin.id,{new_password:this.passwordForm.new_password}).then((t=>{this.passwordLoading=!1,t.success?(this.passwordDialogVisible=!1,this.$message({type:"success",message:"密码重置成功"})):this.$message.error(t.message||"密码重置失败")})).catch((t=>{this.passwordLoading=!1,this.$message.error("网络错误，请稍后重试"),console.error("重置密码失败:",t)})))}))},handleDelete(t){t.is_superadmin?this.$message.warning("不能删除超级管理员"):(this.currentAdmin=t,this.deleteDialogVisible=!0)},confirmDelete(){this.deleteLoading=!0,r.jC.delete(this.currentAdmin.id).then((t=>{this.deleteLoading=!1,t.success?(this.deleteDialogVisible=!1,this.$message({type:"success",message:"删除成功"}),this.fetchData()):this.$message.error(t.message||"删除失败")})).catch((t=>{this.deleteLoading=!1,this.$message.error("网络错误，请稍后重试"),console.error("删除管理员失败:",t)}))},exportData(){this.$message({type:"info",message:"导出功能开发中"})}}},n=l;var o=s(1656),c=(0,o.A)(n,a,i,!1,null,"df1a2c30",null);const d=c.exports},6878:(t,e,s)=>{"use strict";function a(t,e=null){if(!t||"undefined"===t||"null"===t)return e;try{return JSON.parse(t)}catch(s){return console.warn("JSON解析失败:",s,"原始值:",t),e}}s.d(e,{jD:()=>a})},6960:(t,e,s)=>{"use strict";s.d(e,{$f:()=>m,CN:()=>u,JB:()=>r,LK:()=>c,Sk:()=>v,UT:()=>h,VY:()=>o,Vd:()=>y,Xr:()=>g,be:()=>d,bs:()=>i,gd:()=>p,generateTableContent:()=>w,js:()=>l,n1:()=>n,oh:()=>f,up:()=>C,wy:()=>b,y8:()=>_});var a=s(5545);function i(t){return(0,a["default"])({url:"/api/talk/generateThanks",method:"post",data:t,timeout:5e5})}function r(t){return(0,a["default"])({url:"/api/talk/generateReference",method:"post",data:t,timeout:5e5})}function l(t){return(0,a["default"])({url:"/api/talk/exportOutline",method:"post",data:t})}function n(t){return(0,a["default"])({url:"/api/talk/stopGenerateAll",method:"post",data:t,timeout:48e3})}function o(t){return(0,a["default"])({url:"/api/talk/generateAll",method:"post",data:t,timeout:48e4})}function c(t){return(0,a["default"])({url:"/api/talk/getTitle",method:"post",data:t,timeout:5e4})}function d(t){return(0,a["default"])({url:"/api/talk/getOutline",method:"post",timeout:5e5,data:t})}function h(t){return console.log("发送select4Content请求，数据:",JSON.stringify(t)),t.outline&&"object"===typeof t.outline||(console.error("提纲数据无效:",t.outline),t.outline={title:t.title,subtitle:[]}),t.outline.title||(console.log("提纲缺少title字段，使用论文标题"),t.outline.title=t.title),t.outline.subtitle&&Array.isArray(t.outline.subtitle)||(console.log("提纲缺少subtitle字段或非数组，创建空数组"),t.outline.subtitle=[]),(0,a["default"])({url:"/api/talk/select4Content",method:"post",data:t,timeout:3e4}).catch((t=>{if(console.error("select4Content请求失败:",t),"已有论文"===t)return Promise.reject(t);if(t.response&&400===t.response.status){let e="请求参数错误";return t.response.data&&t.response.data.message&&(e=t.response.data.message),e.includes("免费用户最多只能创建一篇论文")?Promise.reject("免费用户最多只能创建一篇论文，请先删除已有论文再继续"):Promise.reject("您已经选择了一个提纲生成论文，请先完成或删除当前论文再选择新的提纲")}throw t}))}function p(t){return(0,a["default"])({url:"/api/talk/generateSingleParagraph",method:"post",data:t,timeout:8e4})}function u(t){return(0,a["default"])({url:"/api/talk/generateDigest",method:"post",data:t,timeout:16e4})}function m(t={}){const e={pageNo:1,pageSize:10,...t};return(0,a["default"])({url:"/api/talk/getTitleHistory",method:"post",data:e})}function g(t={}){const e={pageNo:1,pageSize:10,...t};return(0,a["default"])({url:"/api/talk/getOutlineHistory",method:"post",data:e})}function f(t){return(0,a["default"])({url:"/api/talk/generateReport",method:"post",data:t,timeout:6e4})}function v(t){return(0,a["default"])({url:"/api/generate/exportReportHtml",method:"post",data:t})}function b(t){return(0,a["default"])({url:"/api/generate/saveReportHistory",method:"post",data:t})}function y(t={}){return(0,a["default"])({url:"/api/talk/getReportHistory",method:"post",data:t})}function _(t){return(0,a["default"])({url:"/api/talk/deleteReport",method:"post",data:t})}function C(){return(0,a["default"])({url:"/api/talk/checkExistingThesis",method:"post",data:{}}).catch((t=>{throw console.error("检查现有论文失败:",t),t}))}function w(t){return(0,a["default"])({url:"/api/generate/tableContent",method:"post",data:{content:t.content,rows:t.rows||0,columns:t.columns||0,requirements:t.requirements||""}})}},7307:(t,e,s)=>{"use strict";s.r(e),s.d(e,{default:()=>c});var a=function(){var t=this,e=t._self._c;return e("div",{staticClass:"admin-user-stats"},[e("el-card",[e("div",{attrs:{slot:"header"},slot:"header"},[e("span",[t._v("用户统计")]),e("el-button",{staticStyle:{float:"right",padding:"3px 0"},attrs:{type:"text"},on:{click:t.refreshData}},[t._v(" 刷新 ")])],1),e("div",{staticClass:"stats-content"},[e("p",[t._v("用户统计功能开发中...")])])])],1)},i=[];const r={name:"AdminUserStats",methods:{refreshData(){this.$message.info("刷新功能开发中...")}}},l=r;var n=s(1656),o=(0,n.A)(l,a,i,!1,null,"1c4fb559",null);const c=o.exports},7585:(t,e,s)=>{"use strict";s.r(e),s.d(e,{default:()=>c});var a=function(){var t=this,e=t._self._c;return e("div",{staticClass:"guide-container"},[t._m(0),e("div",{staticClass:"guide-content"},t._l(t.paras,(function(s,a){return e("div",{key:a,staticClass:"guide-section",class:{"fade-in":!0},style:{animationDelay:.1*a+"s"}},[e("div",{staticClass:"section-header"},[e("div",{staticClass:"section-number"},[t._v(t._s(a+1))]),e("h2",[t._v(t._s(s.title))])]),e("div",{staticClass:"section-body"},t._l(s.text,(function(s,a){return e("p",{key:a,staticClass:"section-text",domProps:{innerHTML:t._s(s)}})})),0)])})),0)])},i=[function(){var t=this,e=t._self._c;return e("div",{staticClass:"disclaimer-banner"},[e("div",{staticClass:"disclaimer-content"},[e("div",{staticClass:"disclaimer-header"},[e("i",{staticClass:"el-icon-warning-outline"}),e("span",[t._v("免责声明")])]),e("div",{staticClass:"disclaimer-text"},[e("p",[t._v("早鸟论文是一款论文写作辅助工具，托管地址：")]),e("a",{staticClass:"repo-link",attrs:{href:"https://gitee.com/zaoniao/open-paper-gui",target:"_blank"}},[e("i",{staticClass:"el-icon-link"}),t._v(" https://papaer.zaoniao.vip/ ")]),e("ul",[e("li",[t._v("本软件仅作为论文写作的辅助工具，提供选题建议、提纲规划和内容创作等参考性功能。")]),e("li",[t._v("用户应当遵守学术规范，对使用本软件生成的内容进行二次加工和修改，确保符合学术要求。")]),e("li",[t._v("本软件不支持任何形式的学术不端行为，用户的具体使用行为及其产生的后果由用户自行承担。")]),e("li",[t._v("本软件不对生成内容的准确性、时效性和完整性做出保证，用户应当自行验证和完善。")]),e("li",[t._v("本软件中使用的AI接口需要用户自行申请和管理，相关费用和安全由用户自行负责。")])]),e("p",{staticClass:"highlight"},[t._v("使用本软件，即表示您已充分理解并同意以上声明。")]),e("div",{staticClass:"contact-info"},[e("i",{staticClass:"el-icon-chat-dot-round"}),t._v(" 用户交流QQ群：544391109 ")])])])])}];const r={name:"Guide",data(){return{paras:[{title:"感谢安装和使用",text:["感谢您选择使用早鸟论文！我们致力于为您提供高效、便捷的论文写作辅助工具。您的支持是我们不断进步的动力！"]},{title:"运行环境要求",text:["早鸟论文采用现代Web技术开发，为确保最佳使用体验，我们建议：","<span class='success-text'>✓ 推荐使用 Chrome、Edge、Firefox 等主流浏览器的最新版本</span>","<span class='success-text'>✓ 建议使用分辨率1920×1080及以上的显示器，以获得最佳显示效果</span>","<span class='warning-text'>⚠ 如果使用Safari浏览器，请确保版本在15.0以上</span>","<span class='error-text'>✗ 不建议使用IE浏览器，可能会出现显示异常</span>","<span class='info-text'>ℹ 如遇到任何使用问题，欢迎加入QQ群咨询反馈</span>"]},{title:"关于AI模型接口",text:["早鸟论文软件本身是<span class='success-text'>完全免费</span>的，我们致力于为用户提供优质的论文写作辅助工具。","由于AI模型调用需要支付服务商费用，为了维持软件的正常运营，我们采用了卡密激活的方式：","<div class='api-notice'>⚡ 重要说明：</div>","<ul class='feature-list'>","  <li>软件本体永久免费，无需付费下载</li>","  <li>卡密仅用于激活AI模型使用权限</li>","  <li>激活后可以无限使用，直到卡密到期</li>","  <li>支持多种主流AI模型，如千问、DeepSeek、文心一言等</li>","  <li>我们会定期优化接口性能，确保稳定服务</li>","</ul>","<span class='info-text'>ℹ 首次使用时，请在\"系统设置\"中完成卡密激活，即可开始使用全部功能。</span>"]}]}}},l=r;var n=s(1656),o=(0,n.A)(l,a,i,!1,null,"5083abf0",null);const c=o.exports},7586:(t,e,s)=>{"use strict";s.r(e),s.d(e,{default:()=>d});var a=function(){var t=this,e=t._self._c;return e("div",{staticClass:"base-config"},[e("h2",[t._v("系统基础信息设置")]),e("el-form",{attrs:{model:t.form,"label-width":"150px"}},[e("div",{staticClass:"card-container"},[e("el-card",{staticClass:"config-card"},[e("div",{attrs:{slot:"header"},slot:"header"},[e("span",[t._v("基础信息设置")])]),e("el-form-item",{attrs:{label:"系统名称"}},[e("el-input",{attrs:{placeholder:"请输入系统名称"},model:{value:t.form.systemName,callback:function(e){t.$set(t.form,"systemName",e)},expression:"form.systemName"}})],1),e("el-form-item",{attrs:{label:"系统Logo"}},[e("el-upload",{staticClass:"logo-uploader",attrs:{action:"","show-file-list":!1,"before-upload":t.beforeLogoUpload,"on-change":t.handleLogoChange}},[t.form.logoUrl?e("img",{staticClass:"logo-img",attrs:{src:t.form.logoUrl}}):e("i",{staticClass:"el-icon-plus logo-upload-icon"})])],1),e("el-form-item",{attrs:{label:"系统描述"}},[e("el-input",{attrs:{type:"textarea",rows:3,placeholder:"请输入系统描述"},model:{value:t.form.description,callback:function(e){t.$set(t.form,"description",e)},expression:"form.description"}})],1)],1),e("el-card",{staticClass:"config-card"},[e("div",{attrs:{slot:"header"},slot:"header"},[e("span",[t._v("论文下载价格设置")])]),e("el-form-item",{attrs:{label:"启用论文下载收费"}},[e("el-switch",{model:{value:t.form.thesisDownloadEnabled,callback:function(e){t.$set(t.form,"thesisDownloadEnabled",e)},expression:"form.thesisDownloadEnabled"}}),e("span",{staticClass:"form-tip"},[t._v("开启后，用户下载论文需要支付费用")])],1),e("el-form-item",{attrs:{label:"论文下载价格"}},[e("el-input-number",{attrs:{min:.01,max:999.99,step:.01,precision:2,disabled:!t.form.thesisDownloadEnabled},model:{value:t.form.thesisDownloadPrice,callback:function(e){t.$set(t.form,"thesisDownloadPrice",e)},expression:"form.thesisDownloadPrice"}}),e("span",{staticClass:"form-tip"},[t._v("单位：元")])],1),e("el-form-item",{attrs:{label:"VIP用户免费下载"}},[e("el-switch",{attrs:{disabled:!t.form.thesisDownloadEnabled},model:{value:t.form.thesisDownloadVipFree,callback:function(e){t.$set(t.form,"thesisDownloadVipFree",e)},expression:"form.thesisDownloadVipFree"}}),e("span",{staticClass:"form-tip"},[t._v("开启后，VIP用户可以免费下载论文")])],1),e("el-form-item",{attrs:{label:"首次下载免费"}},[e("el-switch",{attrs:{disabled:!t.form.thesisDownloadEnabled},model:{value:t.form.thesisDownloadFirstFree,callback:function(e){t.$set(t.form,"thesisDownloadFirstFree",e)},expression:"form.thesisDownloadFirstFree"}}),e("span",{staticClass:"form-tip"},[t._v("开启后，用户首次下载论文免费")])],1)],1)],1),e("el-form-item",[e("el-button",{attrs:{type:"primary"},on:{click:t.onSave}},[t._v("保存设置")]),e("el-button",{on:{click:t.resetForm}},[t._v("重置")])],1)],1)],1)},i=[],r=s(9192);const l={name:"BaseConfig",data(){return{form:{systemName:"",logoUrl:"",description:"",thesisDownloadEnabled:!1,thesisDownloadPrice:10,thesisDownloadVipFree:!0,thesisDownloadFirstFree:!0},loading:!1}},created(){this.loadConfig()},methods:{async loadConfig(){try{this.loading=!0;const e=localStorage.getItem("baseConfig");if(e){const t=JSON.parse(e);this.form={...this.form,...t}}try{const t=await r.XN.getSettings();t.success&&t.data&&(t.data.system&&(this.form.systemName=t.data.system.name||this.form.systemName,this.form.logoUrl=t.data.system.logo||this.form.logoUrl,this.form.description=t.data.system.description||this.form.description),t.data.thesis_download&&(this.form.thesisDownloadEnabled="true"===t.data.thesis_download.is_active,this.form.thesisDownloadPrice=parseFloat(t.data.thesis_download.price)||this.form.thesisDownloadPrice,this.form.thesisDownloadVipFree="true"===t.data.thesis_download.vip_free,this.form.thesisDownloadFirstFree="true"===t.data.thesis_download.first_free))}catch(t){console.warn("从后端加载配置失败，使用本地配置",t)}}finally{this.loading=!1}},async onSave(){try{this.loading=!0,localStorage.setItem("baseConfig",JSON.stringify(this.form));try{const t={system:{name:this.form.systemName,logo:this.form.logoUrl,description:this.form.description},thesis_download:{is_active:this.form.thesisDownloadEnabled?"true":"false",price:this.form.thesisDownloadPrice.toString(),vip_free:this.form.thesisDownloadVipFree?"true":"false",first_free:this.form.thesisDownloadFirstFree?"true":"false"}},e=await r.XN.saveSettings(t);e.success?this.$message.success("设置保存成功"):this.$message.warning("保存到后端失败: "+(e.message||"未知错误"))}catch(t){console.error("保存到后端失败",t),this.$message.warning("保存到后端失败，但已保存到本地")}}finally{this.loading=!1}},resetForm(){this.loadConfig()},beforeLogoUpload(t){const e=t.type.startsWith("image/");return e||this.$message.error("只能上传图片文件！"),e},handleLogoChange(t){const e=new FileReader;e.onload=t=>{this.form.logoUrl=t.target.result},e.readAsDataURL(t.raw)}}},n=l;var o=s(1656),c=(0,o.A)(n,a,i,!1,null,"e566386e",null);const d=c.exports},7859:(t,e,s)=>{"use strict";s.r(e),s.d(e,{default:()=>c});var a=function(){var t=this;t._self._c;return t._m(0)},i=[function(){var t=this,e=t._self._c;return e("div",{staticClass:"admin-settings"},[e("h2",[t._v("系统基础设置")]),e("div",{staticStyle:{color:"#888","margin-top":"30px"}},[t._v('请通过左侧菜单进入"支付配置"或"大模型配置"进行详细设置。')])])}];const r={name:"AdminSettings"},l=r;var n=s(1656),o=(0,n.A)(l,a,i,!1,null,"7fa96834",null);const c=o.exports},8307:(t,e,s)=>{"use strict";s.r(e),s.d(e,{default:()=>d});var a=function(){var t=this,e=t._self._c;return e("div",{staticClass:"payment-settings"},[e("div",{staticClass:"page-header"},[e("div",{staticClass:"header-content"},[t._m(0),e("div",{staticClass:"header-right"},[e("el-button",{attrs:{type:"primary",icon:"el-icon-refresh",loading:t.loading,size:"small"},on:{click:t.loadConfig}},[t._v(" 刷新配置 ")])],1)])]),e("div",{staticClass:"config-container"},[e("el-card",{staticClass:"config-card",attrs:{shadow:"hover"}},[e("div",{staticClass:"card-header",attrs:{slot:"header"},slot:"header"},[e("div",{staticClass:"header-title"},[e("i",{staticClass:"el-icon-key"}),e("span",[t._v("微信支付配置")])]),e("el-switch",{attrs:{"active-text":"启用","inactive-text":"禁用"},on:{change:t.handleEnableChange},model:{value:t.formData.is_enabled,callback:function(e){t.$set(t.formData,"is_enabled",e)},expression:"formData.is_enabled"}})],1),e("el-form",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],ref:"configForm",attrs:{model:t.formData,rules:t.formRules,"label-width":"140px"}},[e("div",{staticClass:"config-section"},[e("h3",{staticClass:"section-title"},[e("i",{staticClass:"el-icon-setting"}),t._v(" 微信支付基础配置 ")]),e("el-row",{attrs:{gutter:20}},[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"配置名称",prop:"name"}},[e("el-input",{attrs:{placeholder:"请输入配置名称，如：早鸟论文微信支付","prefix-icon":"el-icon-edit"},model:{value:t.formData.name,callback:function(e){t.$set(t.formData,"name",e)},expression:"formData.name"}}),e("div",{staticClass:"form-item-tip"},[t._v("用于标识这个支付配置的名称")])],1)],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"公众号APPID",prop:"app_id"}},[e("el-input",{attrs:{placeholder:"例如：wxfb9c26d95fcd8b21","prefix-icon":"el-icon-mobile"},model:{value:t.formData.app_id,callback:function(e){t.$set(t.formData,"app_id",e)},expression:"formData.app_id"}}),e("div",{staticClass:"form-item-tip"},[t._v("微信公众号的应用ID")])],1)],1)],1),e("el-row",{attrs:{gutter:20}},[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"商户号",prop:"mch_id"}},[e("el-input",{attrs:{placeholder:"例如：**********","prefix-icon":"el-icon-bank-card"},model:{value:t.formData.mch_id,callback:function(e){t.$set(t.formData,"mch_id",e)},expression:"formData.mch_id"}}),e("div",{staticClass:"form-item-tip"},[t._v("微信支付商户号，纯数字")])],1)],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"商户证书序列号",prop:"serial_no"}},[e("el-input",{attrs:{placeholder:"例如：56D0EF3D9FFBAF91F2E8C477C732449D503A2290","prefix-icon":"el-icon-document"},model:{value:t.formData.serial_no,callback:function(e){t.$set(t.formData,"serial_no",e)},expression:"formData.serial_no"}}),e("div",{staticClass:"form-item-tip"},[t._v("商户API证书的序列号，大写字母+数字")])],1)],1)],1),e("el-row",{attrs:{gutter:20}},[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"用户私钥路径",prop:"private_key_path"}},[e("el-input",{attrs:{placeholder:"例如：/static/cert/privateKey.txt","prefix-icon":"el-icon-folder"},model:{value:t.formData.private_key_path,callback:function(e){t.$set(t.formData,"private_key_path",e)},expression:"formData.private_key_path"}}),e("div",{staticClass:"form-item-tip"},[t._v("商户私钥文件的服务器路径")])],1)],1),e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"API V3密钥",prop:"api_v3_key"}},[e("el-input",{attrs:{type:"password",placeholder:"例如：yx159357QWERasdfZXCVtgbYHNujmIKO","prefix-icon":"el-icon-key","show-password":""},model:{value:t.formData.api_v3_key,callback:function(e){t.$set(t.formData,"api_v3_key",e)},expression:"formData.api_v3_key"}}),e("div",{staticClass:"form-item-tip"},[t._v("微信支付API v3密钥，32位字符")])],1)],1)],1),e("el-row",{attrs:{gutter:20}},[e("el-col",{attrs:{span:24}},[e("el-form-item",{attrs:{label:"异步通知URL",prop:"notify_url"}},[e("el-input",{attrs:{placeholder:"请输入支付结果通知地址，例如：https://blog.zaoniao.vip/api/pay/notify","prefix-icon":"el-icon-link"},model:{value:t.formData.notify_url,callback:function(e){t.$set(t.formData,"notify_url",e)},expression:"formData.notify_url"}}),e("div",{staticClass:"form-item-tip"},[t._v("微信支付完成后的异步通知回调地址，必须是HTTPS")])],1)],1)],1)],1),e("div",{staticClass:"config-section"},[e("h3",{staticClass:"section-title"},[e("i",{staticClass:"el-icon-cpu"}),t._v(" 模式配置 ")]),e("el-row",{attrs:{gutter:20}},[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"测试模式"}},[e("el-switch",{attrs:{"active-text":"启用","inactive-text":"禁用"},model:{value:t.formData.is_test_mode,callback:function(e){t.$set(t.formData,"is_test_mode",e)},expression:"formData.is_test_mode"}}),e("div",{staticClass:"form-item-tip"},[t._v("开启后将使用测试环境")])],1)],1)],1),e("el-form-item",{attrs:{label:"备注信息"}},[e("el-input",{attrs:{type:"textarea",rows:3,placeholder:"请输入配置备注信息",maxlength:"500","show-word-limit":""},model:{value:t.formData.remark,callback:function(e){t.$set(t.formData,"remark",e)},expression:"formData.remark"}})],1)],1),e("div",{staticClass:"form-actions"},[e("el-button",{attrs:{type:"primary",loading:t.saving,icon:"el-icon-check"},on:{click:t.saveConfig}},[t._v(" 保存配置 ")]),e("el-button",{attrs:{icon:"el-icon-refresh-left"},on:{click:t.resetForm}},[t._v(" 重置表单 ")]),e("el-button",{attrs:{type:"success",loading:t.testing,icon:"el-icon-connection"},on:{click:t.testConnection}},[t._v(" 测试连接 ")])],1)])],1)],1),e("div",{staticClass:"status-info"},[t.lastSaveTime?e("el-alert",{attrs:{title:`最后保存时间：${t.lastSaveTime}`,type:"info",closable:!1,"show-icon":""}}):t._e()],1)])},i=[function(){var t=this,e=t._self._c;return e("div",{staticClass:"header-left"},[e("i",{staticClass:"el-icon-wallet header-icon"}),e("h1",{staticClass:"page-title"},[t._v("支付配置管理")])])}],r=s(5597);const l={name:"PaymentSettings",data(){return{formData:{name:"早鸟论文微信支付",app_id:"wxfb9c26d95fcd8b21",mch_id:"**********",api_key:"",api_v3_key:"yx159357QWERasdfZXCVtgbYHNujmIKO",serial_no:"56D0EF3D9FFBAF91F2E8C477C732449D503A2290",private_key_path:"/static/cert/privateKey.txt",notify_url:"https://blog.zaoniao.vip/api/pay/notify",is_enabled:!0,is_test_mode:!1,remark:"早鸟论文系统微信支付配置，支持论文生成服务付费"},formRules:{name:[{required:!0,message:"请输入配置名称",trigger:"blur"},{min:2,max:50,message:"长度在 2 到 50 个字符",trigger:"blur"}],app_id:[{required:!0,message:"请输入公众号APPID",trigger:"blur"},{pattern:/^wx[a-zA-Z0-9]{16}$/,message:"APPID格式不正确，应以wx开头共18位",trigger:"blur"}],mch_id:[{required:!0,message:"请输入商户号",trigger:"blur"},{pattern:/^\d{8,10}$/,message:"商户号应为8-10位数字",trigger:"blur"}],api_v3_key:[{required:!0,message:"请输入API V3密钥",trigger:"blur"},{min:32,max:32,message:"API V3密钥长度必须为32位",trigger:"blur"},{pattern:/^[a-zA-Z0-9]{32}$/,message:"API V3密钥只能包含字母和数字",trigger:"blur"}],serial_no:[{required:!0,message:"请输入商户证书序列号",trigger:"blur"},{min:40,max:40,message:"证书序列号长度必须为40位",trigger:"blur"},{pattern:/^[A-F0-9]{40}$/,message:"证书序列号格式不正确，应为40位大写字母和数字",trigger:"blur"}],private_key_path:[{required:!0,message:"请输入用户私钥文件路径",trigger:"blur"},{pattern:/^\/.*\.(txt|pem|key)$/,message:"私钥路径应以/开头，文件扩展名为.txt、.pem或.key",trigger:"blur"}],notify_url:[{required:!0,message:"请输入异步通知URL",trigger:"blur"},{type:"url",message:"请输入正确的URL格式",trigger:"blur"},{pattern:/^https:\/\//,message:"通知URL必须使用HTTPS协议",trigger:"blur"}]},loading:!1,saving:!1,testing:!1,lastSaveTime:null}},created(){this.loadConfig()},methods:{async loadConfig(){this.loading=!0;try{const t=await r.Ay.getWeChatPayConfig();if(!t||!t.success)throw new Error(t?.message||"加载配置失败");this.formData={...this.formData,...t.data},this.$message.success("配置加载成功")}catch(t){console.error("加载配置失败:",t),this.$message.error(`加载配置失败: ${t.message}`)}finally{this.loading=!1}},async saveConfig(){try{await this.$refs.configForm.validate()}catch(t){return void this.$message.warning("请检查表单填写是否正确")}this.saving=!0;try{const t=await r.Ay.saveWeChatPayConfig(this.formData);if(!t||!t.success)throw new Error(t?.message||"保存配置失败");this.$message.success("配置保存成功"),this.lastSaveTime=(new Date).toLocaleString(),await this.loadConfig()}catch(t){console.error("保存配置失败:",t),this.$message.error(`保存配置失败: ${t.message}`)}finally{this.saving=!1}},resetForm(){this.$refs.configForm.resetFields(),this.$message.info("表单已重置")},handleEnableChange(t){t?this.$message.success("支付配置已启用"):this.$message.warning("支付配置已禁用")},async testConnection(){this.testing=!0;try{await new Promise((t=>setTimeout(t,2e3))),this.$message.success("连接测试成功！支付配置有效")}catch(t){console.error("连接测试失败:",t),this.$message.error("连接测试失败，请检查配置")}finally{this.testing=!1}}}},n=l;var o=s(1656),c=(0,o.A)(n,a,i,!1,null,"f8467962",null);const d=c.exports},8459:(t,e,s)=>{"use strict";s.r(e),s.d(e,{default:()=>h});var a=function(){var t=this,e=t._self._c;return e("div",{staticClass:"digest"},[e("div",{staticClass:"digest_wrapper"},[e("div",{staticClass:"digest_label"},[t._v("摘       要")]),t.isEditDialogOpen?t._e():e("div",{staticClass:"digest_text"},[e("div",{staticClass:"text"},[t._v(" "+t._s(null==t.digestValue["digest"]?"尚未生成":t.digestValue["digest"])+" ")]),e("div",{staticClass:"keywords"},[t._v("关键字："+t._s(t.digestValue["keywords"]))])]),t.isEditDialogOpen?t._e():e("div",{staticClass:"paragraph_one_edit"},[e("span",{on:{click:t.reGenDigest}},[t._v("重新生成摘要")]),e("span",{on:{click:function(e){t.isEditDialogOpen=!0}}},[t._v("手动编辑")])]),t.isEditDialogOpen?e("div",{staticClass:"digest_form"},[e("el-form",[e("el-form-item",{attrs:{label:"摘要"}},[e("el-input",{attrs:{type:"textarea"},model:{value:t.digestEdited["digest"],callback:function(e){t.$set(t.digestEdited,"digest",e)},expression:"digestEdited['digest']"}})],1),e("el-form-item",{attrs:{label:"关键字"}},[e("el-input",{attrs:{type:"textarea"},model:{value:t.digestEdited["keywords"],callback:function(e){t.$set(t.digestEdited,"keywords",e)},expression:"digestEdited['keywords']"}})],1),e("el-form-item",[e("el-button",{attrs:{type:"primary"},on:{click:t.save}},[t._v("保存内容")]),e("el-button",{on:{click:function(e){t.isEditDialogOpen=!1}}},[t._v("放弃修改")])],1)],1)],1):t._e()]),e("div",{staticClass:"digest_wrapper"},[e("div",{staticClass:"digest_label"},[t._v("ABSTRACT")]),t.isEditDialogOpen?t._e():e("div",{staticClass:"digest_text"},[e("div",{staticClass:"text"},[t._v(" "+t._s(null==t.digestValue["digestEn"]?"尚未生成":t.digestValue["digestEn"])+" ")]),e("div",{staticClass:"keywords"},[t._v("Keywords ："+t._s(t.digestValue["keywordsEn"]))])]),t.isEditDialogOpen?e("div",{staticClass:"digest_form"},[e("el-form",[e("el-form-item",{attrs:{label:"英文摘要"}},[e("el-input",{attrs:{type:"textarea"},model:{value:t.digestEdited["digestEn"],callback:function(e){t.$set(t.digestEdited,"digestEn",e)},expression:"digestEdited['digestEn']"}})],1),e("el-form-item",{attrs:{label:"英文关键字"}},[e("el-input",{attrs:{type:"textarea"},model:{value:t.digestEdited["keywordsEn"],callback:function(e){t.$set(t.digestEdited,"keywordsEn",e)},expression:"digestEdited['keywordsEn']"}})],1),e("el-form-item",[e("el-button",{attrs:{type:"primary"},on:{click:t.save}},[t._v("保存内容")]),e("el-button",{on:{click:function(e){t.isEditDialogOpen=!1}}},[t._v("放弃修改")])],1)],1)],1):t._e()])])},i=[],r=s(4163),l=s(6960);const n={name:"Digest",props:{digestValue:{type:Object,default:{digest:"",digestEn:"",keywords:"",keywordsEn:""}},thesisId:Number},data(){return{digestEdited:this.digestValue,isEditDialogOpen:!1}},created(){},watch:{digestValue(t,e){this.digestEdited=t}},methods:{openEditDialog(){},save(){let t=this.$loading({text:"保存中，请稍等",target:".digest"});(0,r.saveDigest)({...this.digestEdited,thesisId:this.thesisId}).then((e=>{this.isEditDialogOpen=!1,t.close(),this.$emit("onDigestUpdate")})).catch((e=>{t.close(),this.$notify.error(e)}))},reGenDigest(){let t=0,e=this.$loading({text:"正在生成中英文摘要，请稍候...",target:".digest",customClass:"loading-with-percentage"});const s=setInterval((()=>{t+=10*Math.random(),t>90&&(t=90),e.setText(`正在生成中英文摘要，请稍候...<br><span class="loading-percentage">${Math.floor(t)}%</span>`)}),300);(0,l.CN)({thesisId:this.thesisId}).then((t=>{clearInterval(s),e.setText('正在生成中英文摘要，请稍候...<br><span class="loading-percentage">100%</span>'),setTimeout((()=>{e.close(),this.$emit("onDigestUpdate")}),200)})).catch((t=>{clearInterval(s),e.close(),this.$notify.error(t)}))}}},o=n;var c=s(1656),d=(0,c.A)(o,a,i,!1,null,"6ed8caaa",null);const h=d.exports},8818:(t,e,s)=>{"use strict";s.r(e),s.d(e,{default:()=>k});var a=function(){var t=this,e=t._self._c;return e("div",{staticClass:"flex"},[e("div",{staticClass:"home-main grid-content bg-purple-light"},[t.isLoggedIn?e("div",{staticClass:"vip-notice"},[e("div",{staticClass:"notice-content"},[e("div",{staticClass:"notice-icon"},[e("i",{class:t.isVip?"el-icon-star-on":"el-icon-star-off"})]),e("div",{staticClass:"notice-text"},[e("div",{staticClass:"notice-title"},[t._v(" "+t._s(t.isVip?"VIP会员":"免费用户")+" ")]),e("div",{staticClass:"notice-desc"},[t._v(" "+t._s(t.isVip?"您当前是VIP会员，一次最多可生成5个论文标题":"您当前是免费用户，一次最多可生成2个论文标题")+" ")]),t.isVip?e("div",{staticClass:"notice-usage"},[e("span",{staticClass:"usage-text"},[t._v("本次可生成：5个标题")]),e("el-progress",{staticClass:"usage-progress",attrs:{percentage:100,"stroke-width":6,"show-text":!1}})],1):e("div",{staticClass:"notice-usage"},[e("span",{staticClass:"usage-text"},[t._v("本次可生成：2个标题")]),e("el-progress",{staticClass:"usage-progress",attrs:{percentage:100,"stroke-width":6,"show-text":!1}})],1)]),t.isVip?t._e():e("div",{staticClass:"notice-action"},[e("el-button",{attrs:{type:"warning",size:"small"},on:{click:t.showUpgradeInfo}},[t._v(" 升级VIP ")])],1)])]):t._e(),e("el-form",{ref:"form",staticClass:"form-box",attrs:{inline:"",model:t.form,"label-width":"80px"}},[e("el-form-item",{attrs:{label:t.$t("titlePage.major"),required:""}},[e("el-input",{staticStyle:{width:"400px"},attrs:{placeholder:t.$t("titlePage.placeholderMajor"),clearable:""},model:{value:t.form.domain,callback:function(e){t.$set(t.form,"domain",e)},expression:"form.domain"}},[e("template",{slot:"append"},[e("el-button",{staticStyle:{color:"#409eff"},on:{click:function(e){return e.preventDefault(),t.openMajorSelector.apply(null,arguments)}}},[t._v(t._s(t.$t("titlePage.btnSelectMajor")))])],1)],2)],1),e("el-form-item",{attrs:{label:t.$t("titlePage.topic"),required:""}},[e("el-input",{attrs:{placeholder:t.$t("titlePage.placeholderTopic"),clearable:""},model:{value:t.form.topic,callback:function(e){t.$set(t.form,"topic",e)},expression:"form.topic"}})],1),e("el-form-item",{attrs:{label:t.$t("titlePage.lang"),required:""}},[e("el-radio-group",{attrs:{size:"small"},model:{value:t.form.lang,callback:function(e){t.$set(t.form,"lang",e)},expression:"form.lang"}},t._l(t.$t("languageList"),(function(s){return e("el-radio-button",{key:s,attrs:{label:s}},[t._v(t._s(s))])})),1)],1),e("el-form-item",{attrs:{label:t.$t("titlePage.level"),required:""}},[e("el-radio-group",{attrs:{size:"small"},model:{value:t.form.level,callback:function(e){t.$set(t.form,"level",e)},expression:"form.level"}},[e("el-radio-button",{attrs:{label:t.$t("education.中学")}}),e("el-radio-button",{attrs:{label:t.$t("education.大专")}}),e("el-radio-button",{attrs:{label:t.$t("education.本科")}}),e("el-radio-button",{attrs:{label:t.$t("education.硕士")}}),e("el-radio-button",{attrs:{label:t.$t("education.博士")}})],1)],1),e("el-form-item",{attrs:{label:t.$t("titlePage.keyword")}},[e("el-input",{attrs:{width:"200px",placeholder:t.$t("titlePage.relativeKeyword")},model:{value:t.form.keyword,callback:function(e){t.$set(t.form,"keyword",e)},expression:"form.keyword"}})],1),e("el-form-item",[e("el-button",{staticClass:"xiaolong-btn",attrs:{type:"primary",size:"medium"},on:{click:t.handleSubmit}},[t._v(t._s(t.$t("titlePage.btnSubmit")))])],1)],1),t.titles&&t.titles.length>0?e("div",{staticClass:"result-title"},[t.showHistory?e("span",{staticClass:"history-notice"},[e("i",{staticClass:"el-icon-time"}),t._v(" 正在显示您的历史选题记录，您可以重新生成新的选题 ")]):e("span",[t._v(" "+t._s(t.$t("titlePage.tipSelectThis"))+" ")])]):t._e(),t.titles&&t.titles.length>0?e("div",{staticClass:"result-box"},t._l(t.titles,(function(s,a){return e("div",{key:a,staticClass:"title"},[e("span",[t._v(t._s(t.$t("titlePage.title"))+" "+t._s(a+1)+" : "+t._s(s.kk)+" "),e("el-button",{attrs:{type:"primary",size:"small"},on:{click:function(e){return e.preventDefault(),t.selectTitle(s)}}},[t._v(" "+t._s(t.$t("titlePage.btnSelectTitle"))+" ")])],1),e("span",[t._v(t._s(s.tt))]),e("div",{staticClass:"kw"},t._l(s.kws,(function(s){return e("el-tag",{key:s,attrs:{type:"info",size:"medium"}},[t._v(t._s(s))])})),1)])})),0):t._e(),t.titles&&0!=t.titles.length?t._e():e("Placeholder")],1),e("div",{staticClass:"home-sidebar grid-content bg-purple"},[e("PageLeftBox")],1),e("el-dialog",{attrs:{title:t.$t("titlePage.btnSelectMajor"),visible:t.majorSelectorVisible,width:"70%",top:"30px"},on:{"update:visible":function(e){t.majorSelectorVisible=e}}},[e("div",{staticClass:"major-seletor"},t._l(t.majorList,(function(s,a){return e("div",{key:s,staticClass:"major"},[e("div",{staticClass:"major-title"},[t._v(t._s(a))]),e("div",{staticClass:"major-list"},t._l(s,(function(s){return e("span",{key:s,on:{click:function(e){return t.selectMajor(s)}}},[t._v(t._s(s))])})),0)])})),0),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.majorSelectorVisible=!1}}},[t._v(t._s(t.$t("titlePage.btnClose")))])],1)])],1)},i=[],r=s(6960),l=s(1052),n=s(9845),o=s(9171),c=s(6916);const d={哲学:"Z",理论经济学:"L",应用经济学:"Y",法学:"F",政治学:"Z",社会学:"S",民族学:"M",教育学:"J",心理学:"X",体育学:"T",中国语言文学:"Z",外国语言文学:"W",新闻传播学:"X",艺术学:"Y",历史学:"L",数学:"S",物理学:"W",化学:"H",天文学:"T",地理学:"W",大气科学:"D",海洋科学:"H",地球物理学:"D",地质学:"D",生物学:"S",系统科学:"X",力学:"L",机械工程:"J",光学工程:"G",仪器科学与技术:"Y",材料科学与工程:"C",冶金工程:"Y",动力工程及工程热物理:"D",电气工程:"D",电子科学与技术:"D",信息与通信工程:"X",控制科学与工程:"K",计算机科学与技术:"J",建筑学:"J",土木工程:"T",水利工程:"S",测绘科学与技术:"C",化学工程与技术:"H",地质资源与地质工程:"D",矿业工程:"K",石油与天然气工程:"S",纺织科学与工程:"F",轻工技术与工程:"Q",交通运输工程:"J",船舶与海洋工程:"C",航空宇航科学与技术:"H",农业工程:"N",林业工程:"L",环境科学与工程:"H",生物医学工程:"S",食品科学与工程:"S",作物学:"Z",园艺学:"Y",农业资源利用:"N",植物保护:"Z",畜牧学:"X",兽医学:"S",林学:"L",水产:"S",管理科学与工程:"G",工商管理:"G",农林经济管理:"N",公共管理:"G"},h={哲学:"Philosophy",经济学:"Economics",法学:"Law",政治学:"Political Science",社会学:"Sociology",民族学:"Ethnology",教育学:"Education",心理学:"Psychology",体育学:"Physical Education",中国语言文学:"Chinese Language and Literature",外国语言文学:"Foreign Language and Literature",新闻传播学:"Journalism and Communication",艺术学:"Art",历史学:"History",数学:"Mathematics",物理学:"Physics",化学:"Chemistry",天文学:"Astronomy",地理学:"Geography",大气科学:"Atmospheric Sciences",海洋科学:"Oceanography",地球物理学:"Geophysics",地质学:"Geology",生物学:"Biology",系统科学:"Systems Science",力学:"Mechanics",机械工程:"Mechanical Engineering",光学工程:"Optical Engineering",仪器科学与技术:"Instrument Science and Technology",材料科学与工程:"Materials Science and Engineering",冶金工程:"Metallurgical Engineering",动力工程及工程热物理:"Power Engineering and Engineering Thermophysics",电气工程:"Electrical Engineering",电子科学与技术:"Electronic Science and Technology",信息与通信工程:"Information and Communication Engineering",控制科学与工程:"Control Science and Engineering",计算机科学与技术:"Computer Science and Technology",建筑学:"Architecture",土木工程:"Civil Engineering",水利工程:"Hydraulic Engineering",测绘科学与技术:"Surveying and Mapping Science and Technology",化学工程与技术:"Chemical Engineering and Technology",地质资源与地质工程:"Geological Resources and Geological Engineering",矿业工程:"Mining Engineering",石油与天然气工程:"Petroleum and Natural Gas Engineering",纺织科学与工程:"Textile Science and Engineering",轻工技术与工程:"Light Industry Technology and Engineering",交通运输工程:"Transportation Engineering",船舶与海洋工程:"Ship and Ocean Engineering",航空宇航科学与技术:"Aerospace Science and Technology",农业工程:"Agricultural Engineering",林业工程:"Forestry Engineering",环境科学与工程:"Environmental Science and Engineering",生物医学工程:"Biomedical Engineering",食品科学与工程:"Food Science and Engineering",作物学:"Crop Science",园艺学:"Horticulture",农业资源利用:"Agricultural Resource Utilization",植物保护:"Plant Protection",畜牧学:"Animal Husbandry",兽医学:"Veterinary Medicine",林学:"Forestry",水产:"Aquaculture",管理科学与工程:"Management Science and Engineering",工商管理:"Business Administration",农林经济管理:"Agricultural and Forestry Economics and Management",公共管理:"Public Administration"};function p(){if("en"==c.A.locale){for(var t={},e=65;e<91;e++)t[String.fromCharCode(e)]=[];for(let e in h){var s=h[e],a=s.trim().substring(0,1);t[a].push(s)}for(var i in t)0==t[i].length&&delete t[i];return t}for(t={},e=65;e<91;e++)t[String.fromCharCode(e)]=[];for(let r in d){let e=d[r];t[e].push(r)}for(var i in t)0==t[i].length&&delete t[i];return t}var u=s(5353),m=s(6878);const g="lastTitleForm",f="lastTitleResult",v="lastTitle",b="lastOutline",y={name:"GetOutline",components:{PageLeftBox:n["default"],Placeholder:o["default"]},computed:{...(0,u.L8)("user",["userInfo","isLoggedIn","isVip"])},data(){return{titles:[],majorSelectorVisible:!1,majorList:[],form:{domain:"",level:"本科",lang:"中文",topic:"",keyword:""},historyTitles:[],showHistory:!1}},created(){this.loadUserHistory();let t=localStorage.getItem(g);if(t){const e=(0,m.jD)(t);e?this.form=e:localStorage.removeItem(g)}let e=localStorage.getItem(f);if(e&&0===this.titles.length){const t=(0,m.jD)(e);t?this.titles=t:localStorage.removeItem(f)}},methods:{loadUserHistory(){(0,r.$f)().then((t=>{if(t.is_success&&t.data&&t.data.list&&t.data.list.length>0){this.historyTitles=t.data.list;const s=t.data.list[0];if(s.formData&&(this.form.domain=s.formData.domain||"",this.form.topic=s.formData.topic||"",this.form.level=s.formData.level||"本科",this.form.lang=s.formData.lang||"中文",this.form.keyword=s.formData.keyword||""),s.titles){let t=s.titles;if("string"===typeof t)try{t=JSON.parse(t)}catch(e){console.log("⚠️ JSON解析失败:",e)}if("object"===typeof t&&null!==t&&t.titles&&Array.isArray(t.titles)&&(t=t.titles),Array.isArray(t))this.titles=t;else if("object"===typeof t&&null!==t){const e=Object.values(t).find((t=>Array.isArray(t)));e&&(this.titles=e)}}}else if(t.is_success&&t.data&&Array.isArray(t.data)&&t.data.length>0){this.historyTitles=t.data;const s=t.data[0];if(s.form_data&&(this.form.domain=s.form_data.domain||"",this.form.topic=s.form_data.topic||"",this.form.level=s.form_data.level||"本科",this.form.lang=s.form_data.lang||"中文",this.form.keyword=s.form_data.keyword||""),s.titles){let t=s.titles;if("string"===typeof t)try{t=JSON.parse(t)}catch(e){console.log("⚠️ JSON解析失败:",e)}if("object"===typeof t&&null!==t&&t.titles&&Array.isArray(t.titles)&&(t=t.titles),Array.isArray(t))this.titles=t;else if("object"===typeof t&&null!==t){const e=Object.values(t).find((t=>Array.isArray(t)));e&&(this.titles=e)}}}})).catch((t=>{console.error("加载历史标题失败:",t)}))},updateHistoryList(){(0,r.$f)().then((t=>{t.is_success&&t.data&&t.data.list?this.historyTitles=t.data.list:t.is_success&&t.data&&Array.isArray(t.data)&&(this.historyTitles=t.data)})).catch((t=>{console.error("更新历史标题列表失败:",t)}))},selectHistoryTitle(t){if(t.formData?(this.form.domain=t.formData.domain||"",this.form.topic=t.formData.topic||"",this.form.level=t.formData.level||"本科",this.form.lang=t.formData.lang||"中文",this.form.keyword=t.formData.keyword||""):t.form_data&&(this.form.domain=t.form_data.domain||"",this.form.topic=t.form_data.topic||"",this.form.level=t.form_data.level||"本科",this.form.lang=t.form_data.lang||"中文",this.form.keyword=t.form_data.keyword||""),t.titles){let s=t.titles;if("string"===typeof s)try{s=JSON.parse(s)}catch(e){console.log("⚠️ JSON解析失败:",e)}if("object"===typeof s&&null!==s&&Array.isArray(s.titles)&&(s=s.titles),Array.isArray(s))this.titles=s;else if("object"===typeof s&&null!==s){const t=Object.values(s).find((t=>Array.isArray(t)));t&&(this.titles=t)}}this.showHistory=!1},openLoginBox(){},handleGotoUserRank(){this.$router.push({path:"/my/userrank"})},selectTitle(t){localStorage.removeItem(b),localStorage.setItem(v,JSON.stringify(t)),this.$router.push({path:"/paper/outline"})},selectMajor(t){this.form.domain=t,this.majorSelectorVisible=!1},openMajorSelector(){this.majorSelectorVisible=!0,this.majorList=p()},errMsg(t){this.$notify.error({title:t,duration:3e3})},handleSubmit(){if(""==this.form.domain)return this.$message({message:"请选择研究学科",type:"error"}),!1;if(""==this.form.topic)return this.$message({message:"请填写细分研究方向",type:"error"}),!1;let t=0,e=l.Loading.service({text:"正在分析研究方向，生成专业论文题目...",background:"rgba(255, 255, 255, 0.6)",customClass:"loading-with-percentage"});localStorage.setItem(g,JSON.stringify(this.form)),localStorage.removeItem(f);const s=()=>{t<90&&(t+=10*Math.random(),t>90&&(t=90),e.setText(`正在分析研究方向，生成专业论文题目...<br><span class="loading-percentage">${Math.floor(t)}%</span>`))},a=setInterval(s,300);(0,r.LK)(this.form).then((s=>{clearInterval(a),s.is_success?(t=100,e.setText(`正在分析研究方向，生成专业论文题目...<br><span class="loading-percentage">${t}%</span>`),setTimeout((()=>{e.close();let t=s.data;if("string"===typeof t)try{t=JSON.parse(t)}catch(a){console.log("⚠️ JSON解析失败:",a)}if("object"===typeof t&&null!==t&&t.titles&&Array.isArray(t.titles)&&(t=t.titles),Array.isArray(t))this.titles=t,localStorage.setItem(f,JSON.stringify(this.titles)),this.showHistory=!1,this.updateHistoryList();else if("object"===typeof t&&null!==t){const e=Object.values(t).find((t=>Array.isArray(t)));e?(this.titles=e,localStorage.setItem(f,JSON.stringify(this.titles)),this.showHistory=!1,this.updateHistoryList()):this.$message({type:"error",message:"标题数据格式错误"})}else this.$message({type:"error",message:"标题数据格式错误"})}),200)):(e.close(),this.$message({type:"error",message:s.message}))})).catch((t=>{clearInterval(a),e.close(),console.error("请求失败:",t),this.$message({type:"error",message:"string"===typeof t?t:"请求失败，请稍后重试"})}))},showUpgradeInfo(){this.$alert('\n        <div style="text-align: left;">\n          <h3 style="color: #E6A23C; margin-bottom: 15px;">🌟 升级VIP会员</h3>\n          <div style="margin-bottom: 10px;">\n            <strong>📝 论文标题：</strong>从2个提升至5个（每次生成）\n          </div>\n          <div style="margin-bottom: 10px;">\n            <strong>🚀 生成速度：</strong>享受优先处理\n          </div>\n          <div style="margin-bottom: 10px;">\n            <strong>💎 高级功能：</strong>解锁所有AI功能\n          </div>\n          <div style="margin-bottom: 10px;">\n            <strong>🎯 专属服务：</strong>优先客服支持\n          </div>\n          <div style="color: #F56C6C; font-weight: bold;">\n            立即升级，享受更多权益！\n          </div>\n        </div>\n      ',"升级VIP",{dangerouslyUseHTMLString:!0,confirmButtonText:"立即升级",cancelButtonText:"稍后再说",type:"warning"}).then((()=>{this.$message.info("升级功能开发中...")})).catch((()=>{}))}}},_=y;var C=s(1656),w=(0,C.A)(_,a,i,!1,null,"680a90e4",null);const k=w.exports},8934:t=>{"use strict";t.exports="data:image/png;base64,6L+Z5piv5LiA5Liq5LqM6L+b5Yi25paH5Lu277yM5peg5rOV55u05o6l57yW6L6R44CC6K+35L2/55So546w5pyJ55qEdGFvYmFvLXFyY29kZS5wbmfkvZzkuLrkuLTml7bmm7/ku6PjgIIg"},9076:(t,e,s)=>{"use strict";s.r(e),s.d(e,{default:()=>c});var a=function(){var t=this,e=t._self._c;return e("div",{staticClass:"cover"},[e("div",{staticClass:"cover_uptitle"},[t._v("早鸟论文课程(设计)论文")]),e("div",{staticClass:"cover_title"},[t._v(" 题目:"),e("span",[t._v(t._s(t.title))])]),t._m(0),t._m(1),t._m(2),t._m(3),t._m(4),e("div",{staticClass:"cover_school"},[t._v("学校名称")]),e("div",{staticClass:"cover_major"},[t._v("专业名称")]),e("div",{staticClass:"cover_student"},[t._v("学生姓名")]),e("div",{staticClass:"cover_teacher"},[t._v("指导教师")]),e("div",{staticClass:"cover_date"},[t._v("完成日期")])])},i=[function(){var t=this,e=t._self._c;return e("div",{staticClass:"cover_meta"},[e("div",[t._v("学  号：")]),t._v(" "),e("span",[t._v("(下载后填写)")])])},function(){var t=this,e=t._self._c;return e("div",{staticClass:"cover_meta"},[e("div",[t._v("姓  名：")]),t._v(" "),e("span",[t._v("(下载后填写)")])])},function(){var t=this,e=t._self._c;return e("div",{staticClass:"cover_meta"},[e("div",[t._v("学  院：")]),t._v(" "),e("span",[t._v("(下载后填写)")])])},function(){var t=this,e=t._self._c;return e("div",{staticClass:"cover_meta"},[e("div",[t._v("专  业：")]),t._v(" "),e("span",[t._v("(下载后填写)")])])},function(){var t=this,e=t._self._c;return e("div",{staticClass:"cover_meta"},[e("div",[t._v("指导教师：")]),t._v(" "),e("span",[t._v("(下载后填写)")])])}];const r={props:{title:String},components:{},data(){return{}},computed:{},watch:{},methods:{},created(){},mounted(){}},l=r;var n=s(1656),o=(0,n.A)(l,a,i,!1,null,"75c70254",null);const c=o.exports},9120:(t,e,s)=>{"use strict";s.r(e),s.d(e,{default:()=>h});var a=function(){var t=this,e=t._self._c;return e("section",t._l(t.paragraphs,(function(s,a){return e("div",{key:s.id,staticClass:"paragraphs"},[e("div",{class:"paragraph_one ",attrs:{"data-paraId":s.id},on:{click:function(e){return t.handlerClickPara(s)}}},[e("div",{class:"paragraph_one_title title-level"+t.level},[t._v(" ["+t._s(t.chapterTitlePrefix+(a+1))+"] "+t._s(s.title)+" "),e("small",[t._v("共 "+t._s(s.length||0)+" 字")])]),t.onlyShowTitle?t._e():e("div",{staticClass:"paragraph_one_text"},[e("div",{staticClass:"result-text-inner"},t._l(t.replaceNewLine(s.text),(function(a,i){return e("div",{key:i,domProps:{innerHTML:t._s(t.processTableContent(a,s.id,i))}})})),0)]),e("div",{staticClass:"paragraph_one_edit"},[e("button",{on:{click:function(e){return t.handlerReGen(s)}}},[t._v("AI生成本段")]),e("button",{on:{click:function(e){return t.handlerEdit(s)}}},[t._v("手动编辑")]),e("button",{staticStyle:{"background-color":"#007bff",color:"white"},on:{click:function(e){return t.handlerTableTools(s)}}},[t._v("📊 表格工具")]),e("button",{on:{click:function(e){return t.handlerAddPara(s,"next")}}},[t._v("段后增加段落")]),e("button",{on:{click:function(e){return t.handlerAddPara(s,"child")}}},[t._v("给本段增加子段")]),e("button",{on:{click:function(e){return t.handlerMove(s,"up")}}},[t._v("上移")]),e("button",{on:{click:function(e){return t.handlerMove(s,"down")}}},[t._v("下移")])])]),e("ParagraphBox",{attrs:{thesisId:t.thesisId,onlyShowTitle:t.onlyShowTitle,paragraphs:s.subtitle,chapterTitlePrefix:t.chapterTitlePrefix+(a+1)+".",level:t.level+1,lang:t.lang},on:{onReGen:t.handlerReGen,onUpdate:t.handlerUpdate,onEdit:t.handlerEdit,onDelete:t.handlerDelete,onTableTools:t.handlerTableTools}}),t.newParaFormVisible?e("div",{staticClass:"dialog-box_bg"}):t._e(),t.newParaFormVisible?e("div",{staticClass:"dialog-box"},[e("div",{staticClass:"dialog-box_header"},[e("div",{staticClass:"dialog-box_header_left"},[t._v(" "+t._s("child"==t.newParaForm.addType?"本段中插入子段落":"本段后新增一段")+" ")]),e("div",{staticClass:"dialog-box_header_right"},[e("i",{staticClass:"el-icon-circle-close",on:{click:t.dialogClose}})])]),e("div",{staticClass:"dialog-box_body"},[e("el-form",{ref:"newParaFormRef",refInFor:!0,attrs:{model:t.newParaForm,"label-width":"150px","label-position":"top"}},[e("el-form-item",{attrs:{label:""}},[t._v(" 在段落 【"+t._s(t.newParaForm["basePara"]["title"])+"】 "+t._s("child"==t.newParaForm.addType?"章中，插入一个子段落":"后,新增一段")+" ")]),e("el-form-item",{attrs:{label:t.$t("paragraphBox.newParaDialog.formTitle"),prop:"newTitle",required:""}},[e("el-input",{attrs:{placeholder:"请输入内容",clearable:""},model:{value:t.newParaForm.newTitle,callback:function(e){t.$set(t.newParaForm,"newTitle",e)},expression:"newParaForm.newTitle"}})],1),e("el-form-item",{attrs:{label:t.$t("paragraphBox.newParaDialog.formContent"),prop:"newContent"}},[e("el-input",{attrs:{type:"textarea",placeholder:"请输入内容，可留白",maxlength:"500","show-word-limit":""},model:{value:t.newParaForm.newContent,callback:function(e){t.$set(t.newParaForm,"newContent",e)},expression:"newParaForm.newContent"}})],1)],1)],1),e("div",{staticClass:"dialog-box_footer"},[e("el-popconfirm",{attrs:{confirmButtonText:t.$t("paragraphBox.newParaDialog.btnContinue"),cancelButtonText:t.$t("paragraphBox.newParaDialog.btnCancel"),icon:"el-icon-info",iconColor:"red",title:t.$t("paragraphBox.newParaDialog.tipCancel")},on:{cancel:t.dialogClose}},[e("el-button",{attrs:{slot:"reference",type:"primary",size:"medium",plain:""},slot:"reference"},[t._v(t._s(t.$t("paragraphBox.newParaDialog.btnCancel")))])],1),e("el-button",{attrs:{type:"primary",size:"medium"},on:{click:t.dialogSubmit}},[t._v(t._s(t.$t("paragraphBox.newParaDialog.btnSubmit")))])],1)]):t._e()],1)})),0)},i=[],r=s(4163),l=s(5353);const n={name:"ParagraphBox",props:{paragraphs:{type:Array,default:()=>[]},chapterTitlePrefix:{type:String,default:""},thesisId:{type:Number,default:-1},level:{type:Number,default:1},lang:{type:String,default:"中文"},onlyShowTitle:{type:Boolean,default:!1}},computed:{...(0,l.aH)({selectPara:t=>t.thesis.outlineTreeSelectPara}),selectedPid(){return this.selectPara["id"]}},watch:{selectedPid(t,e){const s=document.querySelector('.paragraph_one[data-paraId="'+t+'"]');s.classList.add("s")}},data(){return{paragraphRegenFormVisible:!1,paragraphRegenForm:{length:500,instruction:""},newParaFormVisible:!1,newParaForm:{basePara:{},thesisId:this.thesisId,baseParaId:"",addType:"",newTitle:"",newContent:""}}},methods:{handlerClickPara(t){this.$store.commit("thesis/SET_SELECTED_PARAGRAPH",t)},handlerAddPara(t,e="next"){this.newParaFormVisible=!0,this.newParaForm["addType"]=e,this.newParaForm["thesisId"]=this.thesisId,this.newParaForm["basePara"]=t,this.newParaForm["baseParaId"]=t.id},handlerMove(t,e){(0,r.paragraphMove)({thesisId:this.thesisId,paraId:t.id,moveType:e}).then((t=>{this.$message({type:"success",message:"操作完成"}),this.$emit("onUpdate")})).catch((t=>this.$message({type:"error",message:"操作完成"+t})))},dialogClose(){this.newParaForm["thesisId"]=this.thesisId,this.newParaForm["basePara"]={},this.newParaForm["baseParaId"]="",this.newParaForm["newTitle"]="",this.newParaForm["newContent"]="",this.newParaFormVisible=!1},dialogSubmit(){""!=this.newParaForm.newTitle?(0,r.saveNewParagraph)(this.newParaForm).then((t=>{this.$message({type:"success",message:"保存完成"}),this.dialogClose(),this.$emit("onUpdate")})).catch((t=>{this.$message({type:"error",message:"遇到错误 "+t})})):this.$message({type:"error",message:"请填写标题"})},replaceNewLine(t){if(void 0==t||""==t)return["尚未生成..."];if(t.includes("<table")||t.includes("<div")||t.includes("<p>"))return[t];const e=t.split("\n"),s=[];for(let a in e)""!=e[a]&&"```"!=e[a]&&s.push(e[a]);return s},getParagraphTitle(t,e){return"中文"==this.lang?"["+t+(e+1)+"]":t+(e+1)},handlerUpdate(){this.$emit("onUpdate")},handlerEdit(t){this.$emit("onEdit",t)},handlerDelete(t){this.$confirm("删除后无法恢复,确认删除本段？","提示",{confirmButtonText:"确定删除",cancelButtonText:"取消",type:"warning"}).then((e=>{(0,r.paragraphDelete)({thesisId:this.thesisId,paraId:t.id}).then((t=>{this.$message({type:"success",message:"删除完毕"}),this.$emit("onUpdate")})).catch((t=>this.$message({type:"error",message:t})))}))},handlerReGen(t){this.$emit("onReGen",t)},handlerTableTools(t){this.$emit("onTableTools",t)},processTableContent(t,e,s){return t.includes("<table"),t},saveParagraphContent(t,e){const a=this.paragraphs.find((e=>e.id===t));if(a){a.text=e;const i={userId:this.$store.state.user.userInfo.id,thesisId:this.thesisId,paragraphId:t,title:a.title,text:e};Promise.resolve().then(s.bind(s,4163)).then((({saveSingleParagraph:e})=>{e(i).then((e=>{console.log("段落内容已保存到后端:",t)})).catch((t=>{console.error("保存段落内容失败:",t),this.$message.error("保存失败: "+t)}))})).catch((t=>{console.error("导入API失败:",t)}))}else console.error("未找到段落，无法保存:",t)}},mounted(){}},o=n;var c=s(1656),d=(0,c.A)(o,a,i,!1,null,"316bcf57",null);const h=d.exports},9171:(t,e,s)=>{"use strict";s.r(e),s.d(e,{default:()=>c});var a=function(){var t=this,e=t._self._c;return e("div",{staticClass:"placeholer"})},i=[];const r={name:"Placeholder"},l=r;var n=s(1656),o=(0,n.A)(l,a,i,!1,null,"d977f222",null);const c=o.exports},9318:(t,e,s)=>{"use strict";s.r(e),s.d(e,{default:()=>d});var a=function(){var t=this,e=t._self._c;return e("div",{staticClass:"dashboard"},[e("div",{staticClass:"welcome-banner"},[e("div",{staticClass:"welcome-content"},[e("div",{staticClass:"welcome-text"},[e("h1",[t._v("欢迎回来，"+t._s(t.adminInfo?.username||"管理员"))]),e("p",[t._v("今天是 "+t._s(t.currentDate)+"，祝您工作愉快！")])]),e("div",{staticClass:"welcome-actions"},[e("el-button",{attrs:{type:"primary",icon:"el-icon-refresh"},on:{click:t.refreshData}},[t._v(" 刷新数据 ")])],1)])]),e("el-row",{staticClass:"stats-cards",attrs:{gutter:24}},[e("el-col",{attrs:{xs:24,sm:12,md:6}},[e("div",{staticClass:"stats-card user-card"},[e("div",{staticClass:"stats-icon"},[e("i",{staticClass:"el-icon-user"})]),e("div",{staticClass:"stats-content"},[e("div",{staticClass:"stats-number"},[t._v(t._s(t.overviewData?.overview?.total_users||0))]),e("div",{staticClass:"stats-label"},[t._v("总用户数")]),e("div",{staticClass:"stats-trend"},[e("i",{staticClass:"el-icon-arrow-up"}),t._v(" 今日新增: "+t._s(t.overviewData?.today?.new_users||0)+" ")])])])]),e("el-col",{attrs:{xs:24,sm:12,md:6}},[e("div",{staticClass:"stats-card thesis-card"},[e("div",{staticClass:"stats-icon"},[e("i",{staticClass:"el-icon-document"})]),e("div",{staticClass:"stats-content"},[e("div",{staticClass:"stats-number"},[t._v(t._s(t.overviewData?.overview?.total_thesis||0))]),e("div",{staticClass:"stats-label"},[t._v("总论文数")]),e("div",{staticClass:"stats-trend"},[e("i",{staticClass:"el-icon-arrow-up"}),t._v(" 今日新增: "+t._s(t.overviewData?.today?.new_thesis||0)+" ")])])])]),e("el-col",{attrs:{xs:24,sm:12,md:6}},[e("div",{staticClass:"stats-card vip-card"},[e("div",{staticClass:"stats-icon"},[e("i",{staticClass:"el-icon-star-on"})]),e("div",{staticClass:"stats-content"},[e("div",{staticClass:"stats-number"},[t._v(t._s(t.overviewData?.overview?.vip_users||0))]),e("div",{staticClass:"stats-label"},[t._v("VIP用户")]),e("div",{staticClass:"stats-trend"},[e("i",{staticClass:"el-icon-warning"}),t._v(" 过期: "+t._s(t.overviewData?.overview?.expired_vip_users||0)+" ")])])])]),e("el-col",{attrs:{xs:24,sm:12,md:6}},[e("div",{staticClass:"stats-card chat-card"},[e("div",{staticClass:"stats-icon"},[e("i",{staticClass:"el-icon-chat-dot-round"})]),e("div",{staticClass:"stats-content"},[e("div",{staticClass:"stats-number"},[t._v(t._s(t.overviewData?.overview?.total_chat_logs||0))]),e("div",{staticClass:"stats-label"},[t._v("聊天记录")]),e("div",{staticClass:"stats-trend"},[e("i",{staticClass:"el-icon-arrow-up"}),t._v(" 今日: "+t._s(t.overviewData?.today?.chat_logs||0)+" ")])])])])],1),e("el-row",{staticClass:"charts-section",attrs:{gutter:24}},[e("el-col",{attrs:{xs:24,lg:16}},[e("div",{staticClass:"chart-card"},[e("div",{staticClass:"chart-header"},[e("h3",[t._v("用户增长趋势")]),e("div",{staticClass:"chart-actions"},[e("el-button",{attrs:{size:"small",type:"text"},on:{click:t.refreshData}},[e("i",{staticClass:"el-icon-refresh"})])],1)]),e("div",{staticClass:"chart-container"},[t.userStats?e("div",{staticClass:"chart-content"},t._l(t.userStats.daily_stats,(function(s,a){return e("div",{key:a,staticClass:"chart-item"},[e("div",{staticClass:"chart-bar"},[e("div",{staticClass:"chart-bar-fill",style:{height:t.getBarHeight(s.count,t.userStats.daily_stats)}})]),e("div",{staticClass:"chart-label"},[t._v(t._s(s.date))]),e("div",{staticClass:"chart-value"},[t._v(t._s(s.count))])])})),0):e("div",{staticClass:"chart-loading"},[e("div",{staticClass:"loading-spinner"}),e("p",[t._v("加载中...")])])])])]),e("el-col",{attrs:{xs:24,lg:8}},[e("div",{staticClass:"chart-card"},[e("div",{staticClass:"chart-header"},[e("h3",[t._v("VIP用户分布")])]),e("div",{staticClass:"chart-container"},[t.userStats?e("div",{staticClass:"vip-distribution"},t._l(t.userStats.vip_level_stats,(function(s,a){return e("div",{key:a,staticClass:"vip-item"},[e("div",{staticClass:"vip-info"},[e("div",{staticClass:"vip-level"},[t._v("VIP "+t._s(s.level))]),e("div",{staticClass:"vip-count"},[t._v(t._s(s.count)+"人")])]),e("div",{staticClass:"vip-progress"},[e("div",{staticClass:"progress-bar"},[e("div",{staticClass:"progress-fill",style:{width:s.count/t.userStats.total_users*100+"%"}})]),e("div",{staticClass:"vip-percentage"},[t._v(" "+t._s((s.count/t.userStats.total_users*100).toFixed(1))+"% ")])])])})),0):e("div",{staticClass:"chart-loading"},[e("div",{staticClass:"loading-spinner"}),e("p",[t._v("加载中...")])])])])])],1),e("el-row",{staticClass:"bottom-section",attrs:{gutter:24}},[e("el-col",{attrs:{xs:24,md:8}},[e("div",{staticClass:"action-card"},[e("div",{staticClass:"card-header"},[e("h3",[t._v("快速操作")])]),e("div",{staticClass:"action-grid"},[e("div",{staticClass:"action-item",on:{click:function(e){return t.$router.push("/admin/users")}}},[e("div",{staticClass:"action-icon"},[e("i",{staticClass:"el-icon-user"})]),e("div",{staticClass:"action-text"},[t._v("用户管理")])]),e("div",{staticClass:"action-item",on:{click:function(e){return t.$router.push("/admin/thesis")}}},[e("div",{staticClass:"action-icon"},[e("i",{staticClass:"el-icon-document"})]),e("div",{staticClass:"action-text"},[t._v("论文管理")])]),e("div",{staticClass:"action-item",on:{click:function(e){return t.$router.push("/admin/stats/overview")}}},[e("div",{staticClass:"action-icon"},[e("i",{staticClass:"el-icon-s-data"})]),e("div",{staticClass:"action-text"},[t._v("详细统计")])]),e("div",{staticClass:"action-item",on:{click:function(e){return t.$router.push("/admin/settings")}}},[e("div",{staticClass:"action-icon"},[e("i",{staticClass:"el-icon-setting"})]),e("div",{staticClass:"action-text"},[t._v("系统设置")])])])])]),e("el-col",{attrs:{xs:24,md:16}},[e("div",{staticClass:"system-card"},[e("div",{staticClass:"card-header"},[e("h3",[t._v("系统信息")])]),e("div",{staticClass:"system-grid"},[e("div",{staticClass:"system-item"},[e("div",{staticClass:"system-label"},[t._v("系统名称")]),e("div",{staticClass:"system-value"},[t._v("早鸟论文后台管理系统")])]),e("div",{staticClass:"system-item"},[e("div",{staticClass:"system-label"},[t._v("当前管理员")]),e("div",{staticClass:"system-value"},[t._v(t._s(t.adminInfo?.username||"未知"))])]),e("div",{staticClass:"system-item"},[e("div",{staticClass:"system-label"},[t._v("最后登录")]),e("div",{staticClass:"system-value"},[t._v(t._s(t.adminInfo?.last_login_time||"未知"))])]),e("div",{staticClass:"system-item"},[e("div",{staticClass:"system-label"},[t._v("系统时间")]),e("div",{staticClass:"system-value"},[t._v(t._s(t.currentTime))])])])])])],1)],1)},i=[],r=s(5353);const l={name:"AdminDashboard",data(){return{currentTime:"",currentDate:"",timer:null}},computed:{...(0,r.L8)("admin",["overviewData","userStats","adminInfo"])},mounted(){this.$nextTick((()=>{setTimeout((()=>{this.loadData()}),100)})),this.startTimer()},beforeDestroy(){this.timer&&clearInterval(this.timer)},methods:{...(0,r.i0)("admin",["getOverview","getUserStats"]),async loadData(){try{await Promise.all([this.getOverview(),this.getUserStats({period:"7d"})])}catch(t){console.error("加载数据失败:",t),this.$message.error("加载数据失败，请稍后重试")}},async refreshData(){this.$message.info("正在刷新数据..."),await this.loadData(),this.$message.success("数据刷新成功")},getBarHeight(t,e){if(!e||0===e.length)return"0%";const s=Math.max(...e.map((t=>t.count)));return s>0?t/s*100+"%":"0%"},startTimer(){this.updateTime(),this.timer=setInterval(this.updateTime,1e3)},updateTime(){const t=new Date;this.currentTime=t.toLocaleString("zh-CN"),this.currentDate=t.toLocaleDateString("zh-CN",{year:"numeric",month:"long",day:"numeric",weekday:"long"})}}},n=l;var o=s(1656),c=(0,o.A)(n,a,i,!1,null,"01383205",null);const d=c.exports},9503:(t,e,s)=>{"use strict";s.r(e),s.d(e,{default:()=>d});var a=function(){var t=this,e=t._self._c;return e("div",{staticClass:"order-detail"},[e("div",{staticClass:"page-header"},[e("h2",[t._v("支付订单详情")]),e("div",{staticClass:"header-actions"},[e("el-button",{attrs:{type:"primary",icon:"el-icon-refresh"},on:{click:t.loadOrderDetail}},[t._v("刷新")]),e("el-button",{attrs:{icon:"el-icon-back"},on:{click:t.goBack}},[t._v("返回")])],1)]),e("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}]},[e("el-card",{staticClass:"detail-card"},[e("div",{attrs:{slot:"header"},slot:"header"},[e("span",[t._v("基本信息")])]),e("el-descriptions",{attrs:{column:2,border:""}},[e("el-descriptions-item",{attrs:{label:"订单ID"}},[t._v(t._s(t.orderDetail.id||"-"))]),e("el-descriptions-item",{attrs:{label:"用户ID"}},[t._v(t._s(t.orderDetail.user_id||"-"))]),e("el-descriptions-item",{attrs:{label:"商品ID"}},[t._v(t._s(t.orderDetail.product_id||"-"))]),e("el-descriptions-item",{attrs:{label:"支付金额"}},[t._v(t._s(t.orderDetail.amount?`${t.orderDetail.amount.toFixed(2)} 元`:"-"))]),e("el-descriptions-item",{attrs:{label:"商户订单号"}},[t._v(t._s(t.orderDetail.out_trade_no||"-"))]),e("el-descriptions-item",{attrs:{label:"平台订单号"}},[t._v(t._s(t.orderDetail.trade_no||"-"))]),e("el-descriptions-item",{attrs:{label:"支付状态"}},[e("el-tag",{attrs:{type:t.getStatusType(t.orderDetail.status)}},[t._v(" "+t._s(t.getStatusText(t.orderDetail.status))+" ")])],1),e("el-descriptions-item",{attrs:{label:"支付方式"}},[t._v(t._s(t.orderDetail.pay_type||"-"))]),e("el-descriptions-item",{attrs:{label:"创建时间"}},[t._v(t._s(t.orderDetail.create_time||"-"))]),e("el-descriptions-item",{attrs:{label:"支付时间"}},[t._v(t._s(t.orderDetail.pay_time||"-"))])],1)],1),t.orderDetail.download_records&&t.orderDetail.download_records.length>0?e("el-card",{staticClass:"detail-card"},[e("div",{attrs:{slot:"header"},slot:"header"},[e("span",[t._v("关联下载记录")])]),e("el-table",{staticStyle:{width:"100%"},attrs:{data:t.orderDetail.download_records,border:""}},[e("el-table-column",{attrs:{prop:"id",label:"记录ID",width:"80",align:"center"}}),e("el-table-column",{attrs:{prop:"thesis_id",label:"论文ID",width:"100",align:"center"}}),e("el-table-column",{attrs:{prop:"is_paid",label:"支付状态",width:"100",align:"center"},scopedSlots:t._u([{key:"default",fn:function(s){return[e("el-tag",{attrs:{type:s.row.is_paid?"success":"warning"}},[t._v(" "+t._s(s.row.is_paid?"已支付":"未支付")+" ")])]}}],null,!1,2467579289)}),e("el-table-column",{attrs:{prop:"payment_time",label:"支付时间",width:"180",align:"center"}}),e("el-table-column",{attrs:{prop:"create_time",label:"创建时间",width:"180",align:"center"}})],1)],1):t._e(),t.orderDetail.notify_data?e("el-card",{staticClass:"detail-card"},[e("div",{attrs:{slot:"header"},slot:"header"},[e("span",[t._v("回调通知数据")])]),e("div",{staticClass:"json-viewer"},[e("pre",[t._v(t._s(t.formatJSON(t.orderDetail.notify_data)))])])]):t._e(),t.orderDetail.notify_data_parsed?e("el-card",{staticClass:"detail-card"},[e("div",{attrs:{slot:"header"},slot:"header"},[e("span",[t._v("解析后的通知数据")])]),e("div",{staticClass:"json-viewer"},[e("pre",[t._v(t._s(t.formatJSON(t.orderDetail.notify_data_parsed)))])])]):t._e()],1)])},i=[],r=s(9192);const l={name:"PaymentOrderDetail",data(){return{orderId:null,orderDetail:{},loading:!1}},created(){this.orderId=this.$route.params.id,this.orderId?this.loadOrderDetail():(this.$message.error("订单ID不能为空"),this.goBack())},methods:{async loadOrderDetail(){if(this.orderId)try{this.loading=!0;const t=await r.iX.getOrderDetail(this.orderId);t.success&&t.data?this.orderDetail=t.data:this.$message.error(t.message||"加载订单详情失败")}catch(t){console.error("加载订单详情失败:",t),this.$message.error("加载订单详情失败: "+(t.message||"未知错误"))}finally{this.loading=!1}},getStatusType(t){switch(t){case 0:return"warning";case 1:return"success";case 2:return"info";default:return"info"}},getStatusText(t){switch(t){case 0:return"未支付";case 1:return"已支付";case 2:return"已退款";default:return"未知"}},formatJSON(t){if(!t)return"";try{return"string"===typeof t?JSON.stringify(JSON.parse(t),null,2):JSON.stringify(t,null,2)}catch(e){return t}},goBack(){this.$router.push({name:"AdminPaymentOrders"})}}},n=l;var o=s(1656),c=(0,o.A)(n,a,i,!1,null,"20b3eda8",null);const d=c.exports},9680:(t,e,s)=>{"use strict";s.r(e),s.d(e,{default:()=>d});var a=function(){var t=this,e=t._self._c;return e("el-dialog",{attrs:{title:"手动编辑段落",top:"5vh",height:"80vh",visible:t.isShow,center:"","close-on-click-modal":!1,"before-close":t.discard},on:{close:t.dialogClose}},[e("el-form",{attrs:{"label-position":"top"}},[e("el-form-item",{attrs:{label:"段落标题",required:""}},[e("el-input",{attrs:{autocomplete:"off"},model:{value:t.form.title,callback:function(e){t.$set(t.form,"title",e)},expression:"form.title"}})],1),e("el-form-item",{attrs:{label:"段落内容",required:""}},[e("el-input",{staticStyle:{height:"400px"},attrs:{type:"textarea",autocomplete:"off"},on:{input:t.onContentChange},model:{value:t.form.text,callback:function(e){t.$set(t.form,"text",e)},expression:"form.text"}})],1)],1),e("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:t.discard}},[t._v("放弃修改")]),e("el-button",{attrs:{type:"primary"},on:{click:t.save}},[t._v("保存内容")])],1)],1)},i=[],r=s(4163);const l={props:{isShow:{type:Boolean,default:!1},para:{type:Object,default:{}}},data(){return{form:{id:0,title:"",text:""}}},watch:{para(t,e){this.form=t}},mounted(){console.log("ParagraphEditBox mounted")},methods:{resetForm(){this.$set(this.form,{id:0,title:"",text:""})},onContentChange(){},discard(){this.$confirm("此操作将放弃当前修改的内容,是否继续？","提示",{confirmButtonText:"确定放弃",cancelButtonText:"继续编辑",type:"warning"}).then((t=>this.dialogClose()))},save(){let t=this.$loading({text:"保存中，请稍等"});(0,r.saveSingleParagraph)(this.form).then((e=>{this.$notify.success({title:"成功",message:"保存成功"}),this.dialogClose(),t.close(),this.$emit("onParaUpdated")})).catch((e=>{this.$notify.error({title:"错误",message:JSON.stringify(e)}),t.close()}))},dialogClose(){console.log("close"),this.resetForm(),this.$emit("onClose")}}},n=l;var o=s(1656),c=(0,o.A)(n,a,i,!1,null,"05bb867c",null);const d=c.exports},9845:(t,e,s)=>{"use strict";s.r(e),s.d(e,{default:()=>c});var a=function(){var t=this;t._self._c;return t._m(0)},i=[function(){var t=this,e=t._self._c;return e("div",{staticClass:"left-box",staticStyle:{display:"none"}},[e("div",{staticClass:"title"},[t._v("AI论文生成")]),e("ul",[e("li",[t._v("AI一键创作论文初稿")]),e("li",[t._v("字数保障，灵活选择篇幅")]),e("li",[t._v("最长可达2万字")]),e("li",[t._v("逻辑严谨，格式专业")]),e("li",[t._v("Ai原创，可随意放心使用")]),e("li",[t._v("全面性，启发性，创新性")])])])}];const r={name:"PageLeftBox"},l=r;var n=s(1656),o=(0,n.A)(l,a,i,!1,null,"7fed3d26",null);const c=o.exports}}]);