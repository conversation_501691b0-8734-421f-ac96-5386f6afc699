#!/usr/bin/env python3
"""
启用免费下载功能
临时解决方案，禁用支付功能，让用户可以免费下载
"""

import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置环境变量
os.environ["FLASK_ENV"] = "production"

def enable_free_download():
    """启用免费下载功能"""
    try:
        import pymysql
        from EarlyBird.config.config import AppConfig
        
        # 解析数据库连接信息
        db_uri = AppConfig.SQLALCHEMY_DATABASE_URI
        print(f"数据库连接URI: {db_uri}")
        
        # 从URI中提取连接信息
        import re
        match = re.match(r'mysql\+pymysql://([^:]+):([^@]+)@([^:]+):(\d+)/([^?]+)', db_uri)
        if not match:
            print("❌ 无法解析数据库连接URI")
            return False
        
        username, password, host, port, database = match.groups()
        port = int(port)
        
        print(f"连接信息: {username}@{host}:{port}/{database}")
        
        # 连接数据库
        connection = pymysql.connect(
            host=host,
            port=port,
            user=username,
            password=password,
            database=database,
            charset='utf8mb4'
        )
        
        cursor = connection.cursor()
        
        print("🔧 开始配置免费下载...")
        
        # 检查表是否存在
        cursor.execute("SHOW TABLES LIKE 'earlybird_payment_config'")
        if not cursor.fetchone():
            print("❌ earlybird_payment_config表不存在")
            return False
        
        # 禁用论文下载收费功能
        cursor.execute("""
            UPDATE earlybird_payment_config 
            SET config_value = 'false' 
            WHERE config_key = 'thesis.download.is_active' AND is_deleted = 0
        """)
        
        if cursor.rowcount > 0:
            print("✅ 已禁用论文下载收费功能")
        else:
            # 如果没有找到配置，创建一个
            cursor.execute("""
                INSERT INTO earlybird_payment_config 
                (config_key, config_value, name, description, create_time, update_time, is_deleted, status) 
                VALUES ('thesis.download.is_active', 'false', '论文下载收费开关', '是否启用论文下载收费功能', NOW(), NOW(), 0, 1)
            """)
            print("✅ 已创建并禁用论文下载收费功能")
        
        # 提交更改
        connection.commit()
        
        # 验证配置
        print("\n🔍 验证配置结果:")
        cursor.execute("SELECT config_key, config_value, name FROM earlybird_payment_config WHERE config_key LIKE 'thesis.download.%' AND is_deleted = 0")
        configs = cursor.fetchall()
        
        for key, value, name in configs:
            print(f"  {name}: {key} = {value}")
        
        cursor.close()
        connection.close()
        print("\n🎉 免费下载配置完成！")
        return True
        
    except Exception as e:
        print(f"❌ 配置失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_free_download():
    """测试免费下载功能"""
    try:
        from EarlyBird.web_server import create_custom_app
        from EarlyBird.api.thesis.service import ThesisServie
        from EarlyBird.api.thesis.param import ParamThesisId
        
        app = create_custom_app()
        if not app:
            print("❌ 无法创建Flask应用")
            return False
        
        with app.app_context():
            print("🔍 测试免费下载功能...")
            
            # 模拟下载请求
            service = ThesisServie()
            
            # 创建参数对象（需要手动创建，因为param模块有问题）
            class MockParam:
                def __init__(self):
                    self.userId = 12
                    self.thesisId = 32
            
            param = MockParam()
            
            # 调用下载API
            result = service.exportThesis(param)
            
            if result.isSucc():
                print(f"  ✅ 下载成功")
                print(f"  📊 返回数据: {result.data}")
                if result.data and result.data.get('file'):
                    print(f"  📄 文件名: {result.data['file']}")
                if result.data and result.data.get('free_reason'):
                    print(f"  🆓 免费原因: {result.data['free_reason']}")
            else:
                print(f"  ❌ 下载失败: {result.message}")
                if result.data and result.data.get('need_payment'):
                    print(f"  💰 仍需要支付: {result.data}")
            
            return result.isSucc()
                
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 启用免费下载功能")
    print("=" * 50)
    
    # 启用免费下载
    if enable_free_download():
        print("\n" + "-" * 30)
        
        # 测试免费下载
        print("🔍 测试免费下载功能...")
        if test_free_download():
            print("\n🎉 免费下载功能配置成功！")
            print("\n📋 现在用户可以免费下载论文了")
            print("💡 如果需要重新启用收费功能，请将 thesis.download.is_active 设置为 'true'")
        else:
            print("\n⚠️ 免费下载功能测试失败，请检查配置")
    else:
        print("\n❌ 免费下载功能配置失败")

if __name__ == "__main__":
    main()
