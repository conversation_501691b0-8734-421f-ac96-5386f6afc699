{"name": "open-essay-gui", "version": "0.1.0", "description": "依赖AI大模型，辅助论文选题，辅助给出论文提纲，辅助生成论文内容的项目。是早鸟论文的核心功能的开源版", "scripts": {"dev": "vue-cli-service serve --mode development", "build": "vue-cli-service build --mode production", "lint": "vue-cli-service lint", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"@vue/cli-service": "^5.0.8", "axios": "^1.6.7", "echarts": "^5.6.0", "element-ui": "^2.15.14", "github-markdown-css": "^5.8.1", "js-cookie": "^3.0.5", "jsencrypt": "^3.3.2", "marked": "^15.0.12", "normalize.css": "^8.0.1", "open-essay": "file:", "qs": "^6.11.2", "sass": "^1.71.1", "sass-loader": "^14.1.1", "vue": "^2.6.14", "vue-clipboard2": "^0.3.3", "vue-count-to": "^1.0.13", "vue-cropper": "0.4.9", "vue-i18n": "^8.2.1", "vue-image-crop-upload": "^2.5.0", "vue-markdown": "^2.2.4", "vue-router": "3.0.2", "vue-splitpane": "1.0.4", "vuedraggable": "2.20.0", "vuex": "3.1.0"}, "devDependencies": {"webpack": "^5.90.3", "webpack-obfuscator": "^3.5.1", "webpack-parallel-uglify-plugin": "^2.0.0"}, "author": "<EMAIL>", "license": "MIT", "repository": {"type": "git", "url": "https://gitee.com/open-ai-essay/open-ai-essay.git"}, "bugs": {"url": "https://gitee.com/open-ai-essay/open-ai-essay/issues"}, "homepage": "http://ai.zaoniao.vip"}